import { TextStyle } from '@expo/html-elements/build/primitives/Text';
import React, { ReactElement } from 'react';

import { StyleProp, StyleSheet, View, ViewStyle, Text } from 'react-native';
import {
  IndexPath,
  Select,
  SelectItem,
} from '~node_modules/@ui-kitten/components';

export type Option<T> = {
  label: string;
  value: T | null;
  style?: StyleProp<TextStyle>;
};

type Props<T> = {
  options: Array<Option<T>>;
  selectedItem: Option<T>;
  setSelectedItem: (item: Option<T>) => void;
  label?: string;
  styles?: ViewStyle;
};

const CustomSelect = <T extends string | number | null>({
  options,
  selectedItem,
  setSelectedItem,
  label,
  styles,
}: Props<T>): ReactElement => (
  <Select
    label={label}
    style={{ ...localStyles.select, ...styles }}
    selectedIndex={new IndexPath(options.indexOf(selectedItem))}
    value={selectedItem?.label}
    onSelect={index => setSelectedItem(options[index - 1])}
  >
    {options.map(option => (
      <SelectItem
        key={option.value}
        title={
          <View>
            <Text style={option.style}>{option.label}</Text>
          </View>
        }
      />
    ))}
  </Select>
);

const localStyles = StyleSheet.create({
  select: { margin: 16 },
});

export default CustomSelect;
