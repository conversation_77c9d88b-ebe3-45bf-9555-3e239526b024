import React, { useLayoutEffect } from 'react';
import { KeyboardAvoidingView, Platform, ScrollView, View } from 'react-native';
import { Formik } from 'formik';
import {
  Divider,
  Layout,
  useStyleSheet,
  StyleService,
  Input,
  CheckBox,
} from '@ui-kitten/components';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import * as Yup from 'yup';
import { StackScreenProps } from '~node_modules/@react-navigation/stack';
import useProperties from '~api/useProperties';
import SpinnerButton from '~components/SpinnerButton';
import TitleWithHint from '~components/TitleWithHint';
import { MoreParamList } from '~types';

const schema = Yup.object().shape({
  name: Yup.string().required('Required'),
  address: Yup.string().required('Required'),
  accessInformation: Yup.string().required('Required'),
  calendarLink: Yup.string().url().nullable(),
  isSuspended: Yup.boolean().required('Required'),
  internalNotes: Yup.string().nullable(),
  housekeepingEnabled: Yup.boolean().required('Required'),
});

type Props = StackScreenProps<MoreParamList, 'PropertyFormScreen'>;

const PropertyForm = ({ navigation, route }: Props) => {
  const styles = useStyleSheet(themedStyles);
  const { propertyId } = route.params ?? {};

  const { getProperty, addProperty, updateProperty } = useProperties();

  const { data: currentProperty } = useQuery(
    ['property', propertyId],
    () => getProperty(propertyId),
    { enabled: Boolean(propertyId) },
  );
  const queryClient = useQueryClient();

  const { mutate: addPropertyMutate, isLoading: addIsLoading } = useMutation(
    addProperty,
    {
      onSuccess: () => {
        queryClient.invalidateQueries('properties');
        navigation.goBack();
      },
    },
  );

  const { mutate: updatePropertyMutate, isLoading: updateIsLoading } =
    useMutation(updateProperty, {
      onSuccess: () => {
        queryClient.invalidateQueries('properties');
        queryClient.invalidateQueries('tasks');
        queryClient.invalidateQueries(['property', propertyId]);
        navigation.goBack();
      },
    });

  const isLoading = addIsLoading || updateIsLoading;

  let initialValues;

  const handleSaveProperty = async values => {
    if (currentProperty) {
      await updatePropertyMutate(values);
    } else {
      await addPropertyMutate(values);
    }
  };

  if (currentProperty) {
    initialValues = currentProperty;
  } else {
    initialValues = {
      name: '',
      address: '',
      accessInformation: '',
      notes: '',
      calendarLink: '',
      isSuspended: false,
      internalNotes: '',
      housekeepingEnabled: false,
    };
  }

  useLayoutEffect(() => {
    navigation.setOptions({
      headerTitle: currentProperty ? 'Edit Property' : 'Add Property',
    });
  }, [currentProperty, navigation]);

  return (
    <Formik
      initialValues={initialValues}
      onSubmit={handleSaveProperty}
      validationSchema={schema}
      validateOnMount
    >
      {({ handleSubmit, values, setFieldValue, isValid }) => (
        <KeyboardAvoidingView
          style={styles.container}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <ScrollView>
            <Layout style={styles.form} level="1">
              <Input
                label="Property Name"
                style={styles.input}
                placeholder="New Property"
                value={values.name}
                onChangeText={e => setFieldValue('name', e)}
              />
              <Input
                label="Address"
                style={styles.input}
                textStyle={styles.multilineInput}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
                placeholder="22 6th Street 12345"
                value={values.address}
                onChangeText={e => setFieldValue('address', e)}
              />
              <Input
                label="Access Information"
                style={styles.input}
                textStyle={styles.multilineInput}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
                placeholder="Gate key: 1234"
                value={values.accessInformation}
                onChangeText={e => setFieldValue('accessInformation', e)}
              />
              <Input
                label="Notes"
                style={styles.input}
                textStyle={styles.multilineInput}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
                placeholder="You can enter additional information about the property here"
                value={values.notes}
                onChangeText={e => setFieldValue('notes', e)}
              />
              <>
                <View style={styles.hintWrapper}>
                  <TitleWithHint
                    text="Internal notes"
                    hint="This field is for internal purposes. The text you enter here will not be visible to technicians."
                    textStyle={{ fontSize: 12, color: '#666' }}
                  />
                </View>
                <Input
                  style={styles.input}
                  textStyle={styles.multilineInput}
                  multiline
                  numberOfLines={4}
                  textAlignVertical="top"
                  value={values.internalNotes}
                  onChangeText={e => setFieldValue('internalNotes', e)}
                />
              </>
              <View style={styles.hintWrapper}>
                <TitleWithHint
                  text="Calendar Link"
                  hint="Add your Airbnb, Vrbo, etc. calendar link here to enable automatic job scheduling for check-outs or days without guests."
                  textStyle={{ fontSize: 12, color: '#666' }}
                />
              </View>
              <Input
                style={styles.input}
                value={values.calendarLink}
                onChangeText={e => setFieldValue('calendarLink', e)}
              />
              <CheckBox
                style={styles.input}
                checked={values.isSuspended}
                onChange={e => setFieldValue('isSuspended', e)}
              >
                Suspend property
              </CheckBox>
              <CheckBox
                style={styles.input}
                checked={values.housekeepingEnabled}
                onChange={e => setFieldValue('housekeepingEnabled', e)}
              >
                Enable Housekeeping Jobs
              </CheckBox>
            </Layout>
          </ScrollView>
          <Divider />
          <SpinnerButton
            text="Save Property"
            style={styles.addButton}
            onPress={handleSubmit}
            isLoading={isLoading}
            disabled={!isValid}
          />
        </KeyboardAvoidingView>
      )}
    </Formik>
  );
};

const themedStyles = StyleService.create({
  container: {
    flex: 1,
    backgroundColor: 'background-basic-color-2',
  },
  form: {
    flex: 1,
    paddingHorizontal: 4,
    paddingVertical: 24,
  },
  input: {
    marginHorizontal: 12,
    marginVertical: 8,
  },
  multilineInput: {
    height: 80,
    marginHorizontal: 12,
    marginVertical: 8,
  },
  middleInput: {
    width: 128,
  },
  addButton: {
    marginHorizontal: 16,
    marginVertical: 24,
  },
  hintWrapper: {
    marginLeft: 12,
    marginBottom: -20,
  },
});

export default PropertyForm;
