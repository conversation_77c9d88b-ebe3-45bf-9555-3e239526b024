import React, { useLayoutEffect, useCallback } from 'react';
import { StackScreenProps } from '@react-navigation/stack';
import { Button, StyleService, useStyleSheet } from '@ui-kitten/components';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import { HeaderBackButton } from '@react-navigation/elements';
import PropertyCard from '~components/PropertyCard';
import useProperties from '~api/useProperties';
import ButtonWithConfirm from '~components/ButtonWithConfirm';
import { MoreParamList } from '~types';
import PlaceholderCard from '~components/PlaceholderCard';

type Props = StackScreenProps<MoreParamList, 'PropertyScreen'>;

const PropertyDetails = ({ route, navigation }: Props): React.ReactElement => {
  const styles = useStyleSheet(themedStyles);

  const {
    params: { backnavTo },
  } = route;

  const { getProperty, deleteProperty, undeleteProperty } = useProperties();
  const { data: property } = useQuery(['property', route?.params?.id], () =>
    getProperty(route?.params?.id),
  );

  const queryClient = useQueryClient();

  const { mutateAsync: deletePropertyMutate } = useMutation(deleteProperty, {
    onSuccess: () => {
      queryClient.invalidateQueries('properties');
      navigation.navigate('PropertiesScreen');
    },
  });

  const { mutateAsync: undeletePropertyMutate } = useMutation(
    undeleteProperty,
    {
      onSuccess: () => {
        queryClient.invalidateQueries('properties');
        navigation.navigate('PropertiesScreen');
      },
    },
  );

  const closePropertyDetail = useCallback(() => {
    if (!backnavTo) {
      return;
    }

    navigation.navigate(backnavTo.stack, {
      screen: backnavTo.screen,
    });

    navigation.reset({
      index: 0,
      routes: [{ name: backnavTo.stack }],
    });
  }, [backnavTo, navigation]);

  useLayoutEffect(() => {
    if (backnavTo) {
      navigation.setOptions({
        headerLeft: headerLeft(closePropertyDetail),
      });
    }
  }, [backnavTo, closePropertyDetail, navigation]);

  if (!property) {
    return <PlaceholderCard text="No Property found." icon="pin-outline" />;
  }

  const handleRemoveProperty = () => deletePropertyMutate(property.id);

  const handleUndeleteProperty = () => undeletePropertyMutate(property.id);

  return (
    <>
      <PropertyCard {...property} showDetails />
      <Button
        style={styles.button}
        onPress={() =>
          navigation.navigate('PropertyFormScreen', { propertyId: property.id })
        }
      >
        Edit Property
      </Button>
      {property.deletedAt ? (
        <ButtonWithConfirm
          buttonText="Restore Property"
          confirmText="Do you want to restore this Property?"
          onConfirm={handleUndeleteProperty}
          style={styles.button}
        />
      ) : (
        <ButtonWithConfirm
          buttonText="Delete Property"
          confirmText="Do you want to delete this Property?"
          onConfirm={handleRemoveProperty}
          style={styles.button}
        />
      )}
    </>
  );
};

const headerLeft = (closePropertyDetail: () => void) => () =>
  <HeaderBackButton onPress={closePropertyDetail} />;

const themedStyles = StyleService.create({
  button: {
    marginHorizontal: 16,
    marginTop: 16,
  },
});

export default PropertyDetails;
