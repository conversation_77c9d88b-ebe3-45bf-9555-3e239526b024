import React, { useState } from 'react';
import { TouchableOpacity } from 'react-native';
import { StyleService, useStyleSheet } from '@ui-kitten/components';
import Gallery from './Gallery';
import Icon from './Icon';

type Props = {
  imageUri: string;
};

const ExpandImageButton = ({ imageUri }: Props) => {
  const styles = useStyleSheet(themedStyle);
  const [selectedGalleryItemIndex, setSelectedGalleryItemIndex] = useState(-1);

  const mediaFile = [
    {
      type: 'image',
      uri: imageUri,
    },
  ];

  return (
    <>
      <Gallery
        mediaFiles={mediaFile}
        galleryItem={selectedGalleryItemIndex}
        setGalleryItem={setSelectedGalleryItemIndex}
      />
      <TouchableOpacity
        style={styles.icon}
        onPress={() => {
          setSelectedGalleryItemIndex(0);
        }}
      >
        <Icon fill="white" stroke="lightgray" name="expand-outline" />
      </TouchableOpacity>
    </>
  );
};

const themedStyle = StyleService.create({
  icon: {
    margin: 10,
    width: 30,
    height: 30,
  },
});

export default ExpandImageButton;
