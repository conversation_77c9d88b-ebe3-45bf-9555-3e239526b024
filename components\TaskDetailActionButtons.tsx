import React, { ReactElement, useState } from 'react';
import { StyleService, useStyleSheet, Button } from '@ui-kitten/components';
import { View } from 'react-native';
import * as Clipboard from 'expo-clipboard';
import { StackNavigationProp } from '@react-navigation/stack';
import { AvailableTasksParamList, Task } from '~types';
import Confirm from './Confirm';
import ModifyNextOccurrenceButton from './ModifyNextOccurrenceButton';
import { priorities, TaskRecurringIntervals } from '~dummyData';
import ButtonWithConfirm from './ButtonWithConfirm';

const copyToClipboard = async (text: string): Promise<void> => {
  await Clipboard.setStringAsync(text);
};

type Props = {
  navigation: StackNavigationProp<AvailableTasksParamList>;
  handleAcceptTask: () => Promise<unknown>;
  acceptTaskIsLoading: boolean;
  disabled: boolean;
  task: Task;
  currentUserIsAdmin: boolean;
  currentUserIsTheTaskOwner: boolean;
  handleDeleteTask: () => void;
  jobDetailUrl: string;
};

const TaskDetailActionButtons = ({
  navigation,
  handleAcceptTask,
  acceptTaskIsLoading,
  disabled,
  task,
  currentUserIsAdmin,
  currentUserIsTheTaskOwner,
  handleDeleteTask,
  jobDetailUrl,
}: Props): ReactElement => {
  const styles = useStyleSheet(themedStyles);

  const [showConfirmDelete, setShowConfirmDelete] = useState(false);
  const [linkCopied, setLinkCopied] = useState(false);

  const messages = ['Are you sure you want to accept this Job?'];
  const priorityInfo = priorities.find(
    taskPriority => taskPriority.id === task.priority,
  )?.info;
  if (priorityInfo) {
    messages.unshift(priorityInfo);
  }
  const message = messages.join('\n\n');

  return (
    <View style={styles.actionButtonsContainer}>
      <ButtonWithConfirm
        style={styles.button}
        buttonText="Accept Job"
        confirmText={message}
        onConfirm={handleAcceptTask}
        confirmTitle="Accept Job"
        confirmButtonText="Accept Job"
        disabled={disabled}
        isLoading={acceptTaskIsLoading}
        cancelButtonText="Cancel"
      />
      <Button
        style={styles.button}
        onPress={() =>
          navigation.navigate('AddTaskScreen', { taskId: task.id })
        }
        appearance="outline"
      >
        Edit Task
      </Button>
      {(currentUserIsTheTaskOwner || currentUserIsAdmin) && (
        <>
          <Button
            style={styles.button}
            onPress={() =>
              navigation.navigate('TaskHistoryScreen', {
                taskId: task.id,
                description: task.description,
              })
            }
            appearance="outline"
          >
            Task History
          </Button>
          {task.recurring !== TaskRecurringIntervals.OneTime && (
            <ModifyNextOccurrenceButton task={task} />
          )}
          {Boolean(task.blockedUsers?.length) && (
            <Button
              style={styles.button}
              onPress={() =>
                navigation.navigate('UnblockTaskScreen', {
                  id: task.id,
                })
              }
              appearance="outline"
            >
              Unblock job for blocked users
            </Button>
          )}
          {!task.deletedAt && (
            <Button
              style={styles.button}
              onPress={() => setShowConfirmDelete(true)}
              appearance="outline"
            >
              Delete Task
            </Button>
          )}
          <Confirm
            show={showConfirmDelete}
            onCancel={() => setShowConfirmDelete(false)}
            onConfirm={handleDeleteTask}
            text="Are you sure you want to delete this task?"
          />
        </>
      )}
      <Button
        style={styles.button}
        onPress={() => {
          // Change the text on the button
          setLinkCopied(true);
          copyToClipboard(jobDetailUrl);
        }}
        appearance="outline"
      >
        {linkCopied ? 'Copied' : 'Copy Job Link'}
      </Button>
    </View>
  );
};

const themedStyles = StyleService.create({
  actionButtonsContainer: {
    marginTop: 16,
  },
  button: {
    marginBottom: 16,
  },
});

export default TaskDetailActionButtons;
