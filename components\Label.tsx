import React, { ReactElement } from 'react';
import { StyleService, useStyleSheet, Text } from '@ui-kitten/components';
import _ from 'lodash';

export enum LabelType {
  Completed = 'completed',
  Accepted = 'accepted',
  Unfinished = 'unfinished',
  Overdue = 'overdue',
  Urgent = 'urgent',
  CurrentUserIsBlocked = 'You’re blocked from this job',
  Deleted = 'deleted',
}

type Props = {
  type: LabelType;
};

const statuses = {
  completed: {
    color: 'green',
  },
  accepted: {
    color: 'orange',
  },
  unfinished: {
    color: 'red',
  },
  overdue: {
    color: 'red',
  },
  urgent: {
    color: 'red',
  },
  'You’re blocked from this job': {
    color: 'red',
  },
  deleted: {
    color: 'red',
  },
};

const Label = ({ type }: Props): ReactElement => {
  const styles = useStyleSheet(themedStyles);

  return (
    <Text
      style={{ ...styles.label, backgroundColor: statuses[type].color }}
      category="label"
    >
      {_.toUpper(type)}
    </Text>
  );
};

const themedStyles = StyleService.create({
  label: {
    backgroundColor: 'gray',
    borderRadius: 6,
    color: 'white',
    marginRight: 8,
    marginBottom: 8,
    padding: 8,
    opacity: 0.9,
    fontSize: 10,
  },
});

export default Label;
