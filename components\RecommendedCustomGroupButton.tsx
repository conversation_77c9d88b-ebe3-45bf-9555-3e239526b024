import React, { ReactElement } from 'react';
import { Card, StyleService, Text, useStyleSheet } from '@ui-kitten/components';
import { View } from 'react-native';
import { TaskGroup } from '~types';
import { InfoIcon } from './Icon';
import { carrotColor } from '~constants/Colors';

type Props = {
  name: string;
  taskGroup: TaskGroup;
  onPress: () => void;
};

const RecommemdedCustomGroupButton = ({
  name,
  taskGroup,
  onPress,
}: Props): ReactElement => {
  const styles = useStyleSheet(themedStyles);

  const { tasks } = taskGroup;

  return (
    <Card style={styles.item} onPress={onPress}>
      <View style={styles.itemHeader}>
        <InfoIcon style={styles.headerIcon} />
        <Text category="h4">
          {name}: {tasks ? tasks.length : 0} jobs
        </Text>
      </View>
    </Card>
  );
};

const themedStyles = StyleService.create({
  item: {
    borderRadius: 0,
    height: 70,
    marginVertical: 15,
    borderColor: carrotColor,
  },
  headerIcon: {
    width: 30,
    height: 30,
    tintColor: carrotColor,
    marginRight: 2,
  },
  itemHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: 2,
    marginHorizontal: -14,
  },
});

export default RecommemdedCustomGroupButton;
