import React, { ReactElement } from 'react';
import { Text, View, StyleSheet, Platform } from 'react-native';
import { Button, ButtonGroup } from '@ui-kitten/components';

type Props = {
  selectedTabIndex: number;
  setSelectedTabIndex: (number: number) => void;
  firstTabTitle: string;
  firstTabContent: ReactElement;
  secondTabTitle: string;
  secondTabContent: ReactElement;
};

const TabBar = ({
  selectedTabIndex,
  setSelectedTabIndex,
  firstTabTitle,
  firstTabContent,
  secondTabTitle,
  secondTabContent,
}: Props): ReactElement => (
  <>
    <View style={styles.tabContainer}>
      <ButtonGroup style={styles.buttonGroup}>
        <Button
          onPress={() => {
            setSelectedTabIndex(0);
          }}
          style={[
            styles.button,
            selectedTabIndex !== 0 ? styles.notSelected : '',
          ]}
        >
          <View>
            <Text
              style={[
                styles.buttonText,
                selectedTabIndex === 0 && { color: 'white' },
              ]}
            >
              {firstTabTitle}
            </Text>
          </View>
        </Button>
        <Button
          onPress={() => {
            setSelectedTabIndex(1);
          }}
          style={[
            styles.button,
            selectedTabIndex !== 1 ? styles.notSelected : '',
          ]}
        >
          <View>
            <Text
              style={[
                styles.buttonText,
                selectedTabIndex === 1 && { color: 'white' },
              ]}
            >
              {secondTabTitle}
            </Text>
          </View>
        </Button>
      </ButtonGroup>
    </View>
    {selectedTabIndex === 0 ? firstTabContent : secondTabContent}
  </>
);

const styles = StyleSheet.create({
  tabContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  button: {
    width: Platform.OS !== 'web' ? '50%' : 'auto',
  },
  buttonGroup: {
    marginTop: 16,
    marginHorizontal: Platform.OS !== 'web' ? 16 : 0,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: 'rgb(242, 76, 89)',
  },
  taskListContainer: {
    flex: 1,
    marginTop: 10,
  },
  notSelected: {
    backgroundColor: 'rgba(242, 76, 89, 0.08)',
    color: 'rgb(242, 76, 89)',
  },
  buttonText: {
    color: 'rgb(242, 76, 89)',
    fontWeight: 'bold',
  },
});

export default TabBar;
