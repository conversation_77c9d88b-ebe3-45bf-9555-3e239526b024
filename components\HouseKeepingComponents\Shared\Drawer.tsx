import React, {
  ReactNode,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';
import {
  View,
  Animated,
  PanResponder,
  Modal,
  ScrollView,
  useWindowDimensions,
} from 'react-native';
import { StyleService, useStyleSheet } from '@ui-kitten/components';
import ModalHeader from '~components/HouseKeepingComponents/Shared/ModalHeader';

type Props = {
  visible: boolean;
  onClose: () => void;
  title: string;
  stickyHeader?: boolean;
  children: ReactNode;
};

const Drawer = ({
  visible,
  onClose,
  title,
  stickyHeader = true,
  children,
}: Props) => {
  const styles = useStyleSheet(themedStyles);
  const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = useWindowDimensions();
  const [slideAnim, setSlideAnim] = useState(new Animated.Value(-SCREEN_WIDTH));
  const [panResponder, setPanResponder] = useState(PanResponder.create({}));

  const handleClose = useCallback(() => {
    Animated.timing(slideAnim, {
      toValue: -SCREEN_WIDTH,
      duration: 100,
      useNativeDriver: true,
    }).start(onClose);
  }, [SCREEN_WIDTH, onClose, slideAnim]);

  const drawerSizeStyle: StyleService = useMemo(
    () => ({
      width: SCREEN_WIDTH,
      height: SCREEN_HEIGHT,
    }),
    [SCREEN_HEIGHT, SCREEN_WIDTH],
  );

  useEffect(() => {
    if (visible) {
      slideAnim.setValue(-SCREEN_WIDTH);
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }).start();
    }
  }, [SCREEN_WIDTH, slideAnim, visible]);

  useEffect(() => {
    setSlideAnim(new Animated.Value(-SCREEN_WIDTH));
  }, [SCREEN_WIDTH]);

  useEffect(() => {
    setPanResponder(
      PanResponder.create({
        onMoveShouldSetPanResponder: (_evt, gestureState) =>
          gestureState.dx < -10 && Math.abs(gestureState.dy) < 50,
        onPanResponderMove: (_evt, gestureState) => {
          if (gestureState.dx < 0) {
            slideAnim.setValue(gestureState.dx);
          }
        },
        onPanResponderRelease: (_evt, gestureState) => {
          if (gestureState.dx < -SCREEN_WIDTH * 0.2) {
            handleClose();
          } else {
            Animated.timing(slideAnim, {
              toValue: 0,
              duration: 200,
              useNativeDriver: true,
            }).start();
          }
        },
      }),
    );
  }, [SCREEN_WIDTH, handleClose, slideAnim]);

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={handleClose}
    >
      <View style={styles.container}>
        <Animated.View
          style={[
            styles.drawer,
            drawerSizeStyle,
            {
              transform: [{ translateX: slideAnim }],
            },
          ]}
          {...panResponder.panHandlers}
        >
          <ScrollView
            style={styles.content}
            stickyHeaderIndices={stickyHeader ? [0] : []}
          >
            <ModalHeader title={title} onClose={handleClose} />
            {children}
          </ScrollView>
        </Animated.View>
      </View>
    </Modal>
  );
};

const themedStyles = StyleService.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  drawer: {
    backgroundColor: 'white',
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
});

export default Drawer;
