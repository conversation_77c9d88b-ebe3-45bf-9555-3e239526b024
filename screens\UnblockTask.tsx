import { StackScreenProps } from '@react-navigation/stack';
import React from 'react';
import { ScrollView } from 'react-native';
import { StyleService, useStyleSheet, Text } from '@ui-kitten/components';
import { useMutation, useQueryClient } from 'react-query';
import { useToast } from 'react-native-toast-notifications';
import useTask from '~queries/useTask';
import { AvailableTasksParamList } from '~types';
import Spinner from '~components/Spinner';
import useTasks from '~api/useTasks';
import SpinnerButton from '~components/SpinnerButton';
import ListItem from '~components/ListItem';

type Props = StackScreenProps<AvailableTasksParamList, 'UnblockTaskScreen'>;

const UnblockTask = ({
  navigation,
  route: {
    params: { id },
  },
}: Props) => {
  const styles = useStyleSheet(themedStyles);
  const toast = useToast();
  const queryClient = useQueryClient();
  const { data: task, isLoading: taskIsLoading } = useTask({ id });
  const { unblockTask } = useTasks();
  const { mutate: unblockTaskMutate, isLoading: unblockTaskIsLoading } =
    useMutation(unblockTask, {
      onSuccess: async () => {
        await queryClient.invalidateQueries('tasks');
        await queryClient.invalidateQueries(['task', id]);
        toast.show('The job is unblocked now');
      },
    });

  const handleUnblockTask = async () => {
    await unblockTaskMutate(id);
    navigation.goBack();
  };

  if (taskIsLoading) {
    return <Spinner />;
  }

  return (
    <ScrollView>
      <Text style={styles.title} category="h6">
        Blocked users
      </Text>
      {task?.blockedUsers?.map(({ name, email }) => (
        <ListItem
          name={
            <>
              <Text appearance="hint" category="s1">
                {name}
              </Text>
              <Text>{email}</Text>
            </>
          }
        />
      ))}
      <SpinnerButton
        style={styles.button}
        onPress={handleUnblockTask}
        isLoading={unblockTaskIsLoading}
        text="Unblock"
      />
    </ScrollView>
  );
};

const themedStyles = StyleService.create({
  title: {
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 16,
  },
  button: {
    margin: 16,
  },
});

export default UnblockTask;
