import React, { ReactElement } from 'react';
import { Toggle, Text } from '~node_modules/@ui-kitten/components';

type Props = {
  values: Array<any>;
  keyName: string;
  setFieldValue: (key: string, value: string) => void;
  text: string;
};

const SwitchInput = ({
  values,
  keyName,
  setFieldValue,
  text,
  style,
}: Props): ReactElement => (
  <Toggle
    style={style}
    checked={values[keyName]}
    onChange={value => {
      setFieldValue(keyName, value);
    }}
  >
    <Text>{text}</Text>
  </Toggle>
);

export default SwitchInput;
