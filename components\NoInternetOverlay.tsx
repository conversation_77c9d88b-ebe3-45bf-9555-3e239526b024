import { StyleService, useStyleSheet } from '@ui-kitten/components';
import React from 'react';
import { useNetInfo } from '@react-native-community/netinfo';
import { View, Text } from 'react-native';

type Props = {
  children?: any;
};

const NoInternetOverlay = ({ children }: Props) => {
  const styles = useStyleSheet(themedStyle);

  const { isConnected } = useNetInfo();

  return (
    <>
      {!isConnected && (
        <View style={styles.offlineContainer}>
          <Text style={styles.offlineText}>Not connected to the Internet.</Text>
        </View>
      )}
      {children}
    </>
  );
};

export default NoInternetOverlay;

const themedStyle = StyleService.create({
  offlineContainer: {
    zIndex: 3, // works on ios
    elevation: 3, // works on android
    backgroundColor: '#b52424',
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    width: '100%',
    position: 'absolute',
    top: 64,
  },
  offlineText: {
    color: '#fff',
  },
  views: {
    height: 400,
  },
});
