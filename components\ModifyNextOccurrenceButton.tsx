import {
  StyleService,
  useStyleSheet,
  Button,
  Card,
  Modal,
} from '@ui-kitten/components';
import React, { useState } from 'react';
import { Formik } from 'formik';
import * as Yup from 'yup';
import { View } from 'react-native';
import { useMutation, useQueryClient } from 'react-query';
import { format } from 'date-fns';
import PreferredDateEditor from './PreferredDateEditor';
import { Task } from '~types';
import useTasks from '~api/useTasks';
import { invalidateTasksAfterAddOrUpdate } from '~screens/TaskSettings';
import { formatTaskDatetimeFields } from '~utilities/dateAndTime';
import SpinnerButton from './SpinnerButton';
import { TaskPriorities } from '~dummyData';

const schema = Yup.object().shape({
  preferredDate: Yup.string().required('Required'),
  preferredStartTime: Yup.string().required('Required'),
  preferredEndTime: Yup.string().required('Required'),
});

type Props = {
  task: Task;
};

const ModifyNextOccurrenceButton = ({ task }: Props) => {
  const styles = useStyleSheet(themedStyles);
  const [visible, setVisible] = useState(false);
  const { updateTask } = useTasks();
  const queryClient = useQueryClient();

  const { mutateAsync: updateTaskMutate, isLoading } = useMutation(updateTask, {
    onSuccess: (data: Task) => {
      invalidateTasksAfterAddOrUpdate(queryClient, data.id);
      setVisible(false);
    },
  });

  const handleTaskSubmit = (taskToSave: Task) => {
    const transformedValues = formatTaskDatetimeFields(taskToSave);

    updateTaskMutate(transformedValues);
  };

  const {
    property: { calendarDates },
  } = task;

  const enabledDates =
    task.priority === TaskPriorities.OnCheckoutDate && calendarDates.length > 0
      ? calendarDates.map(({ date }) => format(new Date(date), 'yyyy-MM-dd'))
      : undefined;

  return (
    <>
      <Button
        style={styles.button}
        onPress={() => setVisible(true)}
        appearance="outline"
      >
        Modify next occurrence
      </Button>

      <Modal visible={visible}>
        <Card disabled>
          <Formik
            initialValues={task}
            onSubmit={handleTaskSubmit}
            enableReinitialize
            validationSchema={schema}
            validateOnMount
          >
            {({ handleSubmit, values, setFieldValue, isValid }) => (
              <>
                <PreferredDateEditor
                  values={values}
                  setFieldValue={setFieldValue}
                  enabledDates={enabledDates}
                />
                <View style={styles.buttons}>
                  <Button
                    appearance="outline"
                    onPress={() => setVisible(false)}
                  >
                    Cancel
                  </Button>
                  <SpinnerButton
                    onPress={handleSubmit}
                    isLoading={isLoading}
                    text="Save"
                    disabled={!isValid}
                    size="normal"
                  />
                </View>
              </>
            )}
          </Formik>
        </Card>
      </Modal>
    </>
  );
};

const themedStyles = StyleService.create({
  button: {
    marginBottom: 16,
  },
  buttons: {
    marginTop: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
});

export default ModifyNextOccurrenceButton;
