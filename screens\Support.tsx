import React from 'react';
import { View } from 'react-native';
import { StyleService, Text, useStyleSheet } from '@ui-kitten/components';
import { openURL } from 'expo-linking';
import { A } from '@expo/html-elements';
import { Button } from '~node_modules/@ui-kitten/components';
import PreloginInfoScreenLayout from '~components/PreloginInfoScreenLayout';
import Version from '~components/Version';

export default (): React.ReactElement => {
  const styles = useStyleSheet(themedStyles);

  const handlePressMesssageUsButton = () => {
    openURL('https://tawk.to/airteam');
  };

  return (
    <PreloginInfoScreenLayout title="Support">
      <Button onPress={handlePressMesssageUsButton}>Message Us</Button>
      <View style={styles.or}>
        <Text style={styles.orText}>OR</Text>
      </View>
      <Text style={styles.content}>
        Contact us via email:{' '}
        <A href="mailto:<EMAIL>"><EMAIL></A>
      </Text>
      <Version />
    </PreloginInfoScreenLayout>
  );
};

const themedStyles = StyleService.create({
  content: {
    color: 'black',
  },
  or: {
    marginVertical: 40,
  },
  orText: {
    fontSize: 24,
  },
});
