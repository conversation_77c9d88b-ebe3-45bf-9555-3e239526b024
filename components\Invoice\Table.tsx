import { List } from '@ui-kitten/components';
import React, { ReactElement, ReactNode } from 'react';
import { View, Text, StyleSheet, ListRenderItemInfo } from 'react-native';
import Currency from '~components/Currency';
import { GroupedTaskAssignment, Payment, TaskAssignment } from '~types';

type TableFooterProps = {
  sum: number;
  sumTitle: string;
};

export const TableFooter = ({
  sum,
  sumTitle,
}: TableFooterProps): ReactElement => (
  <View style={styles.footer}>
    <View style={styles.dateCell} />
    <View style={styles.descriptionCell}>
      <Text
        style={{
          ...styles.boldText,
          ...styles.rightText,
          ...styles.inlineLabel,
        }}
      >
        {sumTitle}
      </Text>
    </View>
    <View style={styles.costCell}>
      <Currency
        value={sum}
        style={{ ...styles.rightText, ...styles.boldText }}
      />
    </View>
  </View>
);

type TechnicianTableFooterProps = {
  costOfLabor: number;
  costOfMaterials: number;
  totalCost: number;
  sumTitle: string;
};

export const TechnicianTableFooter = ({
  costOfLabor,
  costOfMaterials,
  totalCost,
  sumTitle,
}: TechnicianTableFooterProps): ReactElement => (
  <View style={styles.footer}>
    <View style={{ width: '40%' }}>
      <Text
        style={{
          ...styles.boldText,
          ...styles.rightText,
          ...styles.inlineLabel,
        }}
      >
        {sumTitle}
      </Text>
    </View>
    <View style={{ width: '20%' }}>
      <Currency
        value={costOfLabor}
        style={{ ...styles.rightText, ...styles.boldText }}
      />
    </View>
    <View style={{ width: '20%' }}>
      <Currency
        value={costOfMaterials}
        style={{ ...styles.rightText, ...styles.boldText }}
      />
    </View>
    <View style={{ width: '20%' }}>
      <Currency
        value={totalCost}
        style={{ ...styles.rightText, ...styles.boldText }}
      />
    </View>
  </View>
);

export const PaymentsHeader = (): ReactElement => (
  <View style={styles.header}>
    <View style={styles.dateCell}>
      <Text style={styles.boldText}>Date</Text>
    </View>
    <View style={styles.descriptionCell}>
      <Text style={styles.boldText}>Description</Text>
    </View>
    <View style={styles.costCell}>
      <Text style={{ ...styles.rightText, ...styles.boldText }}>Amount</Text>
    </View>
  </View>
);

export const PropertyInvoiceHeader = (): ReactElement => (
  <View style={styles.header}>
    <View style={styles.dateCell}>
      <Text style={styles.boldText}>Finish Date</Text>
    </View>
    <View style={styles.descriptionCell}>
      <Text style={styles.boldText}>Description</Text>
    </View>
    <View style={styles.costCell}>
      <Text style={{ ...styles.rightText, ...styles.boldText }}>Cost</Text>
    </View>
  </View>
);

export const TechnicianInvoiceHeader = (): ReactElement => (
  <View style={styles.header}>
    <View style={{ width: '40%' }}>
      <Text style={styles.boldText}>Finished / Description</Text>
    </View>
    <View style={{ width: '20%' }}>
      <Text style={{ ...styles.rightText, ...styles.boldText }}>Labor</Text>
    </View>
    <View style={{ width: '20%' }}>
      <Text style={{ ...styles.rightText, ...styles.boldText }}>Material</Text>
    </View>
    <View style={{ width: '20%' }}>
      <Text style={{ ...styles.rightText, ...styles.boldText }}>Total</Text>
    </View>
  </View>
);

type TableProps = {
  data: (Payment | TaskAssignment)[];
  renderItem:
    | ((
        info: ListRenderItemInfo<TaskAssignment | GroupedTaskAssignment>,
      ) => ReactElement)
    | ((info: ListRenderItemInfo<Payment>) => ReactElement);
  header?: ReactNode;
  footer?: ReactNode;
};

const Table = ({
  data,
  renderItem,
  header,
  footer,
}: TableProps): ReactElement => (
  <>
    {header}
    <List data={data} renderItem={renderItem} />
    {footer}
  </>
);

const styles = StyleSheet.create({
  list: { padding: 16, flex: 1 },
  header: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-start',
    margin: 10,
    marginTop: 0,
    fontWeight: 'bold',
  },
  footer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 10,
    fontWeight: 'bold',
  },
  dateCell: { width: '20%' },
  descriptionCell: { width: '50%' },
  costCell: { width: '30%', textAlign: 'right' },
  rightText: { textAlign: 'right' },
  inlineLabel: { paddingRight: 10 },
  boldText: { fontWeight: 'bold' },
});

export default Table;
