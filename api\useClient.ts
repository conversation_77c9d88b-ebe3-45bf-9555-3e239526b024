import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { useContext } from 'react';
import _ from 'lodash';
import { camelizeKeys, decamelizeKeys } from 'humps';
import { Platform } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useToast } from 'react-native-toast-notifications';
import AuthContext from '~context/AuthContext';
import useRemoveAuth from '~hooks/useRemoveAuth';

const apiBaseUrl = process.env.API_BASE_URL;

export function fileToAppend(value, type) {
  if (Platform.OS !== 'web') {
    return {
      uri: value.uri,
      name: 'video',
      type,
    };
  }

  // credit https://stackoverflow.com/a/15754051/2999763
  const dataURI = value.uri;
  const byteString = atob(dataURI.split(',')[1]);
  const ab = new ArrayBuffer(byteString.length);
  const ia = new Uint8Array(ab);
  for (let i = 0; i < byteString.length; i += 1) {
    ia[i] = byteString.charCodeAt(i);
  }
  return new Blob([ab], { type });
}

export const formData = fields => {
  const decamelizedFields = decamelizeKeys(fields);
  const data = new FormData();
  Object.entries(decamelizedFields).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      value.forEach((v, index) => {
        if (key === 'receipts') {
          if (v?.uri.startsWith('data:')) {
            data.append(`${key}[${index}]`, fileToAppend(v, 'image/jpeg'));
          }
        } else {
          data.append(`${key}[${index}]`, v);
        }
      });
    } else if (['video', 'media'].includes(key)) {
      if (value?.uri) {
        data.append(key, fileToAppend(value, 'video/mp4'));
      }
    } else if (['thumbnail'].includes(key)) {
      if (value?.uri) {
        data.append(key, fileToAppend(value, 'image/jpeg'));
      }
    } else if (value !== null && value !== undefined) {
      data.append(key, value);
    }
  });

  const config = {
    headers: {
      Accept: 'application/json',
      'Content-Type': 'multipart/form-data',
    },
  };

  return { data, config };
};

export default () => {
  const toast = useToast();
  const { authToken } = useContext(AuthContext);
  const { removeAuth } = useRemoveAuth();
  const navigation = useNavigation();

  const headers = _.pickBy({
    Authorization: authToken ? `Bearer ${authToken}` : null,
  });

  const client = axios.create({
    baseURL: apiBaseUrl,
    headers,
  });

  const PRESERVED_KEYS = /^_method$/;

  const processor = (key, convert) =>
    PRESERVED_KEYS.test(key) ? key : convert(key);

  client.interceptors.response.use(
    (response: AxiosResponse) => {
      if (
        response.data &&
        response.headers['content-type'] === 'application/json'
      ) {
        response.data = camelizeKeys(response.data, processor);
      }
      return response;
    },
    error => {
      if (error.response.status === 401) {
        removeAuth();
      }
      if (error.response.status === 402) {
        navigation.navigate('ActivateAccountScreen');
        return;
      }
      let message = '';
      if (error.response.status === 422) {
        const validationErrors = Object.values(error.response.data.errors);

        for (
          let outerIndex = 0;
          outerIndex < validationErrors.length;
          outerIndex += 1
        ) {
          for (
            let index = 0;
            index < validationErrors[outerIndex].length;
            index += 1
          ) {
            message += `${validationErrors[outerIndex][index]} `;
          }
        }
      } else {
        message =
          error.response.data.message ||
          'Something went wrong. Please try again in a few seconds.';
      }

      toast.show(message);

      throw error;
    },
  );

  client.interceptors.request.use((config: AxiosRequestConfig) => {
    const newConfig = { ...config };

    if (newConfig.headers['Content-Type'] === 'multipart/form-data')
      return newConfig;
    if (config.params) {
      newConfig.params = decamelizeKeys(config.params, processor);
    }
    if (config.data) {
      newConfig.data = decamelizeKeys(config.data, processor);
    }
    return newConfig;
  });

  return client;
};
