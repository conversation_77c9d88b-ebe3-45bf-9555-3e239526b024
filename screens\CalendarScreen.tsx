import React, { useRef } from 'react';
import { Text, StyleService, useStyleSheet } from '@ui-kitten/components';
import {
  ScrollView,
  View,
  NativeSyntheticEvent,
  NativeScrollEvent,
} from 'react-native';
import {
  eachDayOfInterval,
  format,
  isAfter,
  isBefore,
  isEqual,
  isFirstDayOfMonth,
  max,
  min,
} from 'date-fns';
import _ from 'lodash';
import useProperties from '~queries/useProperties';
import Spinner from '~components/Spinner';
import useTimezone from '~hooks/useTimezone';
import { Property } from '~types';
import { carrotColor, lightGrey } from '~constants/Colors';
import PlaceholderCard from '~components/PlaceholderCard';

enum States {
  Start = 'start',
  Blocked = 'blocked',
  End = 'end',
}

type Day = {
  date: Date;
  state: string;
  standalone?: boolean;
};

function HeaderRow({ days }: { days: Array<Day> }): React.ReactElement {
  const styles = useStyleSheet(themedStyles);

  return (
    <>
      <View style={[styles.row, styles.firstRow, styles.monthRow]}>
        <View
          style={[
            styles.cell,
            styles.index,
            styles.firstRow,
            styles.firstColumn,
            styles.monthRow,
          ]}
        />
        {days.map((day, index) => {
          const date = new Date(day.date);
          const shouldRenderMonth = index === 0 || isFirstDayOfMonth(date);
          return (
            <View
              key={day.date}
              style={[
                styles.cell,
                styles.index,
                styles.firstRow,
                styles.monthRow,
                shouldRenderMonth && styles.monthColumn,
              ]}
            >
              {shouldRenderMonth && <Text>{format(date, 'Y MMM')}</Text>}
            </View>
          );
        })}
      </View>
      <View style={[styles.row, styles.firstRow]}>
        <View
          style={[
            styles.cell,
            styles.index,
            styles.firstRow,
            styles.firstColumn,
            styles.dayRow,
          ]}
        />
        {days.map((day, index) => {
          const date = new Date(day.date);
          const shouldRenderMonth = index === 0 || isFirstDayOfMonth(date);
          return (
            <View
              key={day.date}
              style={[
                styles.cell,
                styles.index,
                styles.firstRow,
                shouldRenderMonth && styles.monthColumn,
                styles.dayRow,
              ]}
            >
              <Text>{format(date, 'iiiiii')}</Text>
              <Text style={styles.bold}>{format(date, 'd')}</Text>
            </View>
          );
        })}
      </View>
    </>
  );
}

type CalendarRowProps = {
  days: Array<Day>;
  property: Property;
};

function CalendarRow({ days, property }: CalendarRowProps): React.ReactElement {
  const styles = useStyleSheet(themedStyles);

  const convertRowData = (rowData: Array<Day>): Array<Day> => {
    const localData = [] as Array<Day>;

    for (let i = 0; i < rowData.length; i += 1) {
      const tempDay = { ...rowData[i] };
      if (
        rowData[i].state === States.Blocked &&
        rowData[i + 1]?.state === States.Start
      ) {
        tempDay.state = States.End;
      }

      if (
        rowData[i].state === States.Start &&
        rowData[i + 1]?.state === States.Start
      ) {
        tempDay.standalone = true;
      }

      localData.push(tempDay);
    }

    return localData;
  };

  const rowData = [] as Array<Day>;

  days.forEach(day => {
    const localDay = { ...day };

    property.calendarDates.forEach(calendarDate => {
      if (isEqual(localDay.date, new Date(calendarDate.checkInDate))) {
        localDay.state = States.Start;
      } else if (
        isAfter(localDay.date, new Date(calendarDate.checkInDate)) &&
        isBefore(localDay.date, new Date(calendarDate.date))
      ) {
        localDay.state = States.Blocked;
      } else if (isEqual(localDay.date, new Date(calendarDate.date))) {
        localDay.state = States.End;
      }
    });

    rowData.push(localDay);
  });

  return (
    <View style={styles.row}>
      <View
        style={[
          styles.cell,
          styles.index,
          styles.firstColumn,
          styles.firstColumnContent,
        ]}
      >
        <Text numberOfLines={1}>{property.name}</Text>
      </View>
      {convertRowData(rowData).map(day => (
        <View key={day.date} style={styles.cell}>
          <View
            style={[
              styles.worm,
              day.state && styles.highlighted,
              day.state === States.Start && styles.start,
              day.state === States.End && styles.end,
              day.standalone && styles.standalone,
            ]}
          />
        </View>
      ))}
    </View>
  );
}

type RowsProps = {
  days: Array<Day>;
  properties: Array<Property>;
};

function Rows({ days, properties }: RowsProps): React.ReactElement {
  return (
    <View>
      {properties.map(property => (
        <CalendarRow key={property.id} days={days} property={property} />
      ))}
    </View>
  );
}

const CalendarScreen = () => {
  const { zonedTimeToUtc } = useTimezone();

  const { data: properties, isLoading } = useProperties({
    showCalendarDates: true,
  });

  if (isLoading) {
    return <Spinner />;
  }

  if (!properties?.length) {
    return <PlaceholderCard text="No Properties are added yet." icon="list" />;
  }

  const calendarDates = _.flatten(
    properties.map(property => property.calendarDates),
  ).filter(calendarDate => calendarDate?.checkInDate);

  if (!calendarDates?.length) {
    return <PlaceholderCard text="No Calendars are added yet." icon="list" />;
  }

  const checkInDates = calendarDates.map(
    calendarDate => calendarDate && new Date(calendarDate.checkInDate),
  );
  const checkOutDates = calendarDates.map(
    calendarDate => calendarDate && new Date(calendarDate.date),
  );

  const start = min(checkInDates);
  const end = max(checkOutDates);

  const days = eachDayOfInterval({
    start,
    end,
  }).map(day => ({ date: zonedTimeToUtc(day), state: '' }));

  return <TableView days={days} properties={properties} />;
};

type TableViewProps = {
  days: Array<Day>;
  properties: Array<Property>;
};

const TableView = ({ days, properties }: TableViewProps) => {
  const styles = useStyleSheet(themedStyles);
  const containerRef = useRef<View>(null);
  const handleHorizontalScroll = (
    event: NativeSyntheticEvent<NativeScrollEvent>,
  ) => {
    containerRef?.current?.setNativeProps({
      left: -event.nativeEvent.contentOffset.x,
    });
  };

  return (
    <View style={styles.container}>
      <Corner />
      <ScrollView
        contentContainerStyle={styles.verticalScrollContainer}
        stickyHeaderIndices={[1]}
        bounces={false}
      >
        <StickyPropertyNames properties={properties} />
        <View ref={containerRef} style={styles.headerRowContainer}>
          <HeaderRow days={days} />
        </View>
        <ScrollView
          horizontal
          onScroll={handleHorizontalScroll}
          scrollEventThrottle={16}
          bounces={false}
        >
          <View>
            <Rows days={days} properties={properties} />
          </View>
        </ScrollView>
      </ScrollView>
    </View>
  );
};

type StickyPropertyNamesProps = {
  properties: Array<Property>;
};

const StickyPropertyNames = ({ properties }: StickyPropertyNamesProps) => {
  const styles = useStyleSheet(themedStyles);

  return (
    <View style={styles.stickyPropertyNames}>
      {properties.map(({ name }) => (
        <View
          style={[
            styles.cell,
            styles.index,
            styles.firstColumn,
            styles.firstColumnContent,
            styles.firstRow,
            styles.row,
          ]}
        >
          <Text numberOfLines={1}>{name}</Text>
        </View>
      ))}
    </View>
  );
};

const Corner = () => {
  const styles = useStyleSheet(themedStyles);

  return (
    <View
      style={[
        styles.row,
        styles.cell,
        styles.index,
        styles.firstColumn,
        styles.firstColumnContent,
        styles.corner,
      ]}
    />
  );
};

const cellWidth = 72;
const borderWidth = 1;
const radius = 20;
const wormOffset = cellWidth / 2 - cellWidth * 0.15;

const themedStyles = StyleService.create({
  container: {
    margin: 4,
  },
  verticalScrollContainer: {
    flexDirection: 'column',
  },
  row: {
    flexDirection: 'row',
    height: 40,
  },
  cell: {
    width: cellWidth,
    justifyContent: 'center',
    borderWidth,
    borderColor: lightGrey,
  },
  index: {
    borderWidth: 0,
    backgroundColor: 'white',
  },
  highlighted: {
    backgroundColor: carrotColor,
  },
  start: {
    borderTopLeftRadius: radius,
    borderBottomLeftRadius: radius,
    marginLeft: wormOffset,
  },
  end: {
    borderTopRightRadius: radius,
    borderBottomRightRadius: radius,
    width: cellWidth * 0.2,
  },
  firstColumn: {
    width: 100,
    borderLeftWidth: borderWidth,
    paddingLeft: 6,
  },
  firstColumnContent: {
    borderWidth,
  },
  bold: {
    fontWeight: '500',
  },
  firstRow: {
    height: 50,
    alignItems: 'center',
  },
  monthRow: {
    height: 25,
    borderColor: lightGrey,
    borderTopWidth: borderWidth,
  },
  dayRow: {
    borderColor: lightGrey,
    borderBottomWidth: borderWidth,
  },
  monthColumn: {
    borderLeftWidth: 2,
    paddingLeft: 2,
  },
  worm: {
    height: 20,
    width: cellWidth,
  },
  standalone: {
    borderTopRightRadius: radius,
    borderBottomRightRadius: radius,
    width: cellWidth * 0.85,
  },
  corner: {
    position: 'absolute',
    zIndex: 2,
    height: 75,
  },
  headerRowContainer: {
    position: 'absolute',
    marginLeft: -1,
  },
  stickyPropertyNames: {
    position: 'absolute',
    zIndex: 1,
    paddingTop: 75,
  },
});

export default CalendarScreen;
