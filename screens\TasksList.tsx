import {
  IndexPath,
  Input,
  Select,
  SelectItem,
  Text,
} from '@ui-kitten/components';
import React, { ReactElement, useState } from 'react';
import { StyleSheet } from 'react-native';
import { StackScreenProps } from '@react-navigation/stack';
import { useQuery } from 'react-query';
import { useDebounce } from 'ahooks';
import useUser from '~api/useUser';
import TasksListContent from '~components/TaskListContent';
import Spinner from '~components/Spinner';
import TabBar from '~components/TabBar';
import RecommendedTasksContent from '~components/RecommendedTasksContent';
import TasksListHeaderRight from '~components/TasksListHeaderRight';
import useAvailableTasks from '~queries/useAvailableTasks';

type Props = StackScreenProps & { filter: 'available' | 'all' };

const capitalize = (string: string) =>
  string.charAt(0).toUpperCase() + string.slice(1);

type FilterSelectProps = {
  availableSelectItems: string[];
  propertyNameToVisualize: string;
  countName: string;
  keyToSet: string;
  initialValue: string | null;
  setValue: () => void;
  postfix?: string | null;
  style?: string | null;
  disabled?: boolean;
};

const filterSelectOption = (item, title) => () =>
  (
    <Text
      style={{
        ...styles.selectText,
        textDecorationLine: item.isDeleted ? 'line-through' : 'none',
      }}
    >
      {title}
    </Text>
  );

const FilterSelect = ({
  availableSelectItems,
  propertyNameToVisualize,
  countName,
  keyToSet,
  initialValue,
  setValue,
  postfix,
  style,
  disabled,
}: FilterSelectProps) => {
  const initialValueIndex = availableSelectItems?.findIndex(
    item => item.id === initialValue,
  );

  const [selectedIndex, setSelectedIndex] = useState(
    initialValueIndex ? new IndexPath(initialValueIndex) : new IndexPath(0),
  );

  const generateItemTitle = (item, titlePostfix) => {
    let title = capitalize(item[propertyNameToVisualize]);

    if (titlePostfix) {
      title += ` ${titlePostfix}`;
    }

    title += item[countName] !== undefined ? ` (${item[countName]})` : '';

    return filterSelectOption(item, title);
  };

  const generateFormattedValue = () => {
    const selectedRow = availableSelectItems?.[selectedIndex.row];

    if (!selectedRow) {
      return '';
    }

    let formattedValue = capitalize(selectedRow?.[propertyNameToVisualize]);

    if (postfix) {
      formattedValue += ` ${postfix}`;
    }

    formattedValue +=
      selectedRow?.[countName] !== undefined
        ? ` (${selectedRow?.[countName]})`
        : '';

    return formattedValue;
  };

  return (
    <Select
      style={{ ...styles.select, ...style }}
      selectedIndex={selectedIndex}
      value={generateFormattedValue()}
      onSelect={index => {
        setValue(availableSelectItems?.[index.row][keyToSet]);
        setSelectedIndex(index);
      }}
      disabled={disabled}
    >
      {availableSelectItems?.map(item => (
        <SelectItem
          key={item[keyToSet]}
          title={generateItemTitle(item, postfix)}
        />
      ))}
    </Select>
  );
};

const TasksList = ({ navigation, filter }: Props): ReactElement => {
  const { getUser } = useUser();
  const { data: user } = useQuery('user', () => getUser());

  const currentUserRole = user?.accounts[0].pivot.role;

  const [propertyIdToFilter, setPropertyIdToFilter] = useState(null);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [textSearch, setTextSearch] = useState('');
  const [compactModeEnabled, setCompactModeEnabled] = useState(false);

  const debouncedTextSearchValue = useDebounce(textSearch, { wait: 1000 });

  const isRecommendedTabSelected = selectedIndex === 0;

  const { data: recommended, isLoading } = useAvailableTasks({
    propertyId: propertyIdToFilter || undefined,
    textSearch: debouncedTextSearchValue || undefined,
    groupByDateAndProperty: isRecommendedTabSelected || undefined,
  });

  if (isLoading) {
    return <Spinner />;
  }

  const { tasks, properties } = recommended;

  const availablePropertySelectItems = [
    { id: null, name: 'Select Property' },
    ...(properties || []),
  ].map(property => ({ ...property, isDeleted: Boolean(property.deletedAt) }));

  const availableAndAllTasksTemplate = (
    <>
      <TasksListHeaderRight
        navigation={navigation}
        currentUserRole={currentUserRole}
        compactModeEnabled={compactModeEnabled}
        setCompactModeEnabled={setCompactModeEnabled}
      />
      <TasksListContent
        navigation={navigation}
        tasks={tasks}
        currentUserRole={currentUserRole}
        isLoading={isLoading}
        compactModeEnabled={compactModeEnabled}
      />
    </>
  );

  let content;
  if (filter === 'available') {
    content = (
      <>
        <Input
          style={styles.propertySelectStyle}
          value={textSearch}
          onChangeText={value => setTextSearch(value)}
          placeholder="Search"
        />
        <FilterSelect
          availableSelectItems={availablePropertySelectItems || []}
          propertyNameToVisualize="name"
          countName="taskCount"
          keyToSet="id"
          initialValue={propertyIdToFilter}
          setValue={setPropertyIdToFilter}
          style={styles.propertySelectStyle}
        />
        <TabBar
          selectedTabIndex={selectedIndex}
          setSelectedTabIndex={setSelectedIndex}
          firstTabTitle="Recommended"
          firstTabContent={
            <RecommendedTasksContent
              navigation={navigation}
              taskGroups={tasks}
              compactModeEnabled={compactModeEnabled}
              textSearch={debouncedTextSearchValue}
            />
          }
          secondTabTitle="All"
          secondTabContent={availableAndAllTasksTemplate}
        />
      </>
    );
  } else {
    content = availableAndAllTasksTemplate;
  }

  return (
    <>
      <TasksListHeaderRight
        navigation={navigation}
        currentUserRole={currentUserRole}
        compactModeEnabled={compactModeEnabled}
        setCompactModeEnabled={setCompactModeEnabled}
      />
      {content}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingTop: 16,
    paddingHorizontal: 16,
    backgroundColor: 'rgb(247, 249, 252)',
  },
  select: {
    marginBottom: 10,
  },
  propertySelectStyle: {
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 0,
  },
  selectText: {
    fontWeight: '600',
    marginLeft: 10,
  },
});

export default TasksList;
