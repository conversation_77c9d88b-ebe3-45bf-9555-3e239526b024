import React, { useEffect, useState } from 'react';
import { View, Alert, Platform, Image, Linking } from 'react-native';
import { StyleService, Text, useStyleSheet } from '@ui-kitten/components';
import Constants, { ExecutionEnvironment } from 'expo-constants';
import { PurchasesOffering, PurchasesPackage } from 'react-native-purchases';
import { StackScreenProps } from '@react-navigation/stack';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { Button } from '~node_modules/@ui-kitten/components';
import Spinner from '~components/Spinner';
import useAccount from '~api/useAccount';
import { RootStackParamList } from '~types';
import image from '../assets/images/appstore.png';
import PreloginInfoScreenLayout from '~components/PreloginInfoScreenLayout';
import useSignOut from '~hooks/useSignOut';

const apiKey = process.env.REVENUECAT_APPLE_API_KEY;
const appStoreUrl = process.env.APP_STORE_URL!;
const title = 'Activate account';

type PurchaseResult = {
  customerInfo: {
    originalAppUserId: string;
    nonSubscriptionTransactions: { purchaseDateMillis: number }[];
  };
};

type PurchasesModule = {
  configure(config: { apiKey: string; appUserId?: string }): Promise<void>;
  getOfferings(): Promise<{ current: PurchasesOffering }>;
  purchasePackage(packageToBuy?: PurchasesPackage): Promise<PurchaseResult>;
};

type Props = StackScreenProps<RootStackParamList>;

const Activate = ({ navigation }: Props): React.ReactElement => {
  const styles = useStyleSheet(themedStyles);
  const [isPurchasing, setIsPurchasing] = useState(false);
  const [currentOffering, setCurrentOffering] =
    useState<PurchasesOffering | null>(null);
  const [offeringsAreLoading, setOfferingsAreLoading] = useState(false);
  const { activateAccount } = useAccount();
  const { handleSignOut } = useSignOut();

  const handlePressAppStoreButton = () => {
    Linking.openURL(appStoreUrl);
  };

  const isExpoGo =
    Constants.executionEnvironment === ExecutionEnvironment.StoreClient;
  const isWeb = Platform.OS === 'web';

  let Purchases: PurchasesModule | undefined;
  if (!isExpoGo && !isWeb) {
    // eslint-disable-next-line global-require, @typescript-eslint/no-var-requires
    const purchasesModule = require('react-native-purchases');
    Purchases = purchasesModule.default;
  }

  useEffect(() => {
    const setupRevenuecatPurchases = async () => {
      if (Purchases) {
        if (!apiKey) {
          throw new Error('No Revenuecat API key provided');
        }
        setOfferingsAreLoading(true);
        await Purchases.configure({ apiKey });
        const offerings = await Purchases.getOfferings();
        setCurrentOffering(offerings.current);
        setOfferingsAreLoading(false);
      }
    };

    setupRevenuecatPurchases();
  }, [Purchases]);

  if (isWeb) {
    return (
      <PreloginInfoScreenLayout title={title}>
        <Text style={styles.layoutElementSpacing}>
          Please download the app to continue
        </Text>
        <TouchableOpacity
          onPress={handlePressAppStoreButton}
          style={styles.layoutElementSpacing}
        >
          <Image source={image} />
        </TouchableOpacity>
        <Button onPress={handleSignOut} appearance="ghost">
          Sign out
        </Button>
      </PreloginInfoScreenLayout>
    );
  }

  if (offeringsAreLoading) {
    return <Spinner />;
  }

  // If the app runs under ExpoGo we cannot use the Purchase package since it's olny supported in developlent builds
  const handlePressPayButton = async () => {
    const packageToBuy = currentOffering?.availablePackages[0];

    setIsPurchasing(true);
    try {
      if (Purchases) {
        const {
          customerInfo: { originalAppUserId, nonSubscriptionTransactions },
        } = await Purchases.purchasePackage(packageToBuy);

        const lastTransactionPurchaseTimestamp =
          nonSubscriptionTransactions[nonSubscriptionTransactions.length - 1]
            .purchaseDateMillis;

        // Handle the successful purchase
        await activateAccount(
          originalAppUserId,
          lastTransactionPurchaseTimestamp,
        );
        navigation.navigate('SkillsAfterRegistrationScreen');
      }
    } catch (e) {
      if (!e.userCancelled) {
        Alert.alert('Error purchasing package', e.message);
      }
    } finally {
      setIsPurchasing(false);
    }
  };

  return (
    <PreloginInfoScreenLayout title={title}>
      <Text style={[styles.content, styles.layoutElementSpacing]}>
        Hi there! To unlock the full potential of AirTeam and create a new Team,
        a one-time payment is required.
      </Text>
      <Button
        disabled={isExpoGo}
        onPress={handlePressPayButton}
        style={styles.layoutElementSpacing}
      >
        Click here to purchase AirTeam for $10
      </Button>
      <Text style={styles.layoutElementSpacing}>or</Text>
      <Button onPress={handleSignOut} appearance="ghost">
        Sign out
      </Button>
      {isPurchasing && (
        <View style={styles.overlay}>
          <Spinner />
        </View>
      )}
    </PreloginInfoScreenLayout>
  );
};

const themedStyles = StyleService.create({
  content: {
    color: 'black',
    textAlign: 'center',
  },
  overlay: {
    flex: 1,
    position: 'absolute',
    left: 0,
    top: 0,
    right: 0,
    bottom: 0,
    opacity: 0.5,
    backgroundColor: 'black',
  },
  layoutElementSpacing: {
    marginBottom: 40,
  },
});

export default Activate;
