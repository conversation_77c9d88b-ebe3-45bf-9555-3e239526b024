import React from 'react';
import {
  Input,
  StyleService,
  Text,
  useStyleSheet,
} from '@ui-kitten/components';
import { View } from 'react-native';
import CopyButton, {
  CopyButtonProps,
} from '~components/HouseKeepingComponents/Shared/CopyButton';
import { HKChecklist, HKExtendedJob } from '~/types';

type Props = {
  job: HKExtendedJob;
  checklist: HKChecklist;
  copyButtonStatus?: CopyButtonProps['status'];
};

const LinkForOtherPerson = ({
  job,
  checklist,
  copyButtonStatus = 'info',
}: Props) => {
  const styles = useStyleSheet(themedStyles);
  const token = job.checklistTokens?.find(
    access => access.checklist === checklist,
  )?.token;
  const name =
    job.leaderPerforms === checklist ? job.leaderName : job.helperName;
  const helperUrl = `${process.env.FRONTEND_BASE_URL}Root/House%20Keeping/HouseKeepingScreen?checklistToken=${token}`;

  return (
    <View style={styles.container}>
      <Text category="s1">Link for {name}</Text>
      <Text category="c1" appearance="hint">
        Copy and share the following Link with {name} so that They can access
        and complete their part of the Job.
      </Text>
      <View style={styles.inputContainer}>
        <Input status="info" value={helperUrl} readOnly style={styles.input} />
        <View style={styles.rightSection}>
          <CopyButton
            href={helperUrl}
            status={copyButtonStatus}
            style={styles.copyButton}
          />
        </View>
      </View>
    </View>
  );
};

export default LinkForOtherPerson;

const themedStyles = StyleService.create({
  container: {
    gap: 4,
  },
  rightSection: {
    position: 'absolute',
    right: 5,
    top: 5,
    bottom: 5,
    width: 120,
  },
  inputContainer: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
  },
  input: {
    flex: 1,
  },
  copyButton: {
    paddingVertical: 0,
    minHeight: 0,
    height: '100%',
  },
});
