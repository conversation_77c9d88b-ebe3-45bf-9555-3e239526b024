import React from 'react';
import { View } from 'react-native';
import {
  Button,
  Input,
  Layout,
  StyleService,
  Text,
  useStyleSheet,
} from '@ui-kitten/components';
import { Formik } from 'formik';
import * as Yup from 'yup';
import { useMutation } from 'react-query';
import { StackScreenProps } from '@react-navigation/stack';
import { useToast } from 'react-native-toast-notifications';
import { PersonIcon } from '~components/Icon';
import KeyboardAvoidingView from '~components/KeyboardAvoidingView';
import useClient from '~api';
import SpinnerButton from '~components/SpinnerButton';
import { RootStackParamList } from '~types';

const appName = process.env.APP_NAME;

const schema = Yup.object().shape({
  email: Yup.string().email().required('Required'),
});

type Props = StackScreenProps<RootStackParamList, 'ForgotPassword'>;

const ForgotPassword = ({ navigation }: Props): React.ReactElement => {
  const styles = useStyleSheet(themedStyles);
  const toast = useToast();
  const { forgotPassword } = useClient();
  const { mutate, isLoading } = useMutation(forgotPassword);

  const handleResetPassword = ({ email }: { email: string }) => {
    mutate(
      { email },
      {
        onSuccess: async () => {
          toast.show('Password reset email has been sent. Check your mailbox.');
          navigation.navigate('SignIn');
        },
      },
    );
  };

  return (
    <KeyboardAvoidingView style={styles.container}>
      <View style={styles.headerContainer}>
        <Text category="h1" status="control">
          {appName}
        </Text>
        <Text style={styles.label} category="s1" status="control">
          Reset your password
        </Text>
      </View>
      <Formik
        initialValues={{ email: '' }}
        onSubmit={handleResetPassword}
        validationSchema={schema}
        validateOnMount
      >
        {({ values: { email }, setFieldValue, submitForm, isValid }) => (
          <>
            <Layout style={styles.formContainer} level="1">
              <Input
                placeholder="Email"
                accessoryRight={PersonIcon}
                value={email}
                onChangeText={value => setFieldValue('email', value)}
                autoCapitalize="none"
                keyboardType="email-address"
              />
            </Layout>
            <SpinnerButton
              text="Send password reset email"
              style={styles.button}
              onPress={submitForm}
              isLoading={isLoading}
              disabled={!isValid}
            />
            <Button
              style={styles.button}
              appearance="ghost"
              status="basic"
              onPress={() => navigation.goBack()}
            >
              Sign In
            </Button>
          </>
        )}
      </Formik>
    </KeyboardAvoidingView>
  );
};

const themedStyles = StyleService.create({
  container: {
    backgroundColor: 'background-basic-color-1',
  },
  headerContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 216,
    backgroundColor: 'color-primary-default',
  },
  formContainer: {
    flex: 1,
    paddingTop: 32,
    paddingHorizontal: 16,
  },
  label: {
    marginTop: 16,
  },
  button: {
    margin: 16,
  },
});

export default ForgotPassword;
