import React, { Dispatch, ReactElement, SetStateAction } from 'react';
import { StyleProp, StyleSheet, ViewStyle } from 'react-native';

import { IndexPath, Select, SelectItem } from '@ui-kitten/components';
import {
  eachMonthOfInterval,
  format,
  addDays,
  subMonths,
  isAfter,
} from 'date-fns';

type Props = {
  selectedMonth: Date;
  lastMonth: Date;
  setSelectedMonth: Dispatch<SetStateAction<Date>>;
  label?: string;
  styles?: StyleProp<ViewStyle>;
  firstMonth?: string | null;
};

const MonthSelector = ({
  selectedMonth,
  lastMonth,
  setSelectedMonth,
  label,
  styles,
  firstMonth,
}: Props): ReactElement => {
  const lastYear = subMonths(lastMonth, 12);

  const months = eachMonthOfInterval({
    start:
      firstMonth && isAfter(new Date(lastYear), new Date(firstMonth))
        ? new Date(firstMonth)
        : lastYear,
    end: lastMonth,
  })
    .reverse()
    .map(date => addDays(date, 10));

  return (
    <Select
      style={{ ...localStyles.select, ...styles }}
      selectedIndex={new IndexPath(months.indexOf(selectedMonth))}
      value={format(selectedMonth, 'Y MMMM')}
      onSelect={index => setSelectedMonth(months[index - 1])}
      label={label}
    >
      {months.map(month => (
        <SelectItem key={month.getDate()} title={format(month, 'Y MMMM')} />
      ))}
    </Select>
  );
};

const localStyles = StyleSheet.create({
  select: { margin: 16 },
});

export default MonthSelector;
