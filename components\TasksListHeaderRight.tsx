import React, {
  Dispatch,
  ReactElement,
  SetStateAction,
  useLayoutEffect,
} from 'react';
import { View } from 'react-native';
import CompactModeSwitch from './CompactModeSwitch';
import AddTaskAction from './AddTaskAction';

type Props = {
  navigation: any;
  currentUserRole: number;
  compactModeEnabled: boolean;
  setCompactModeEnabled: Dispatch<SetStateAction<boolean>>;
};

const TasksListHeaderRight = ({
  navigation,
  currentUserRole,
  compactModeEnabled,
  setCompactModeEnabled,
}: Props): null => {
  useLayoutEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <View style={{ flexDirection: 'row' }}>
          <CompactModeSwitch
            compactModeEnabled={compactModeEnabled}
            onEnableCompactMode={setCompactModeEnabled}
          />
          {currentUserRole !== 2 && (
            <AddTaskAction onPress={() => navigation.push('AddTaskScreen')} />
          )}
        </View>
      ),
    });
  }, [compactModeEnabled, currentUserRole, navigation, setCompactModeEnabled]);

  return null;
};

export default TasksListHeaderRight;
