import React, {
  Dispatch,
  ReactElement,
  SetStateAction,
  useEffect,
  useRef,
  useState,
} from 'react';
import { Dimensions, Image, Linking, Platform, View } from 'react-native';
import { SwiperFlatList } from 'react-native-swiper-flatlist';
import { Button, StyleService, useStyleSheet } from '@ui-kitten/components';
import <PERSON>Zoom from 'react-native-image-pan-zoom';
import PDFReader from 'rn-pdf-reader-js';
import Modal from '~components/Modal';
import Spinner from './Spinner';
import GalleryControlButton from './GalleryControlButton';
import { LeftArrowIcon, RightArrowIcon } from './Icon';
import { MediaFile } from '~types';
import VideoPlayer from './VideoPlayer/VideoPlayer';
import { ResizeMode } from '~node_modules/expo-av';
import { MediaTypes } from '~helpers';

type Props = {
  mediaFiles: Partial<MediaFile>[];
  galleryItem: number;
  setGalleryItem: Dispatch<SetStateAction<number>>;
};

enum Directions {
  Backward = 'backward',
  Forward = 'forward',
}

const Gallery = ({
  mediaFiles,
  galleryItem,
  setGalleryItem,
}: Props): ReactElement => {
  const styles = useStyleSheet(themedStyle);
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollRef = useRef<SwiperFlatList>(null);

  const naviate = (direction: Directions) => {
    let index = scrollRef.current?.getCurrentIndex() as number;

    if (direction === Directions.Backward && index > 0) {
      index -= 1;
    }

    if (direction === Directions.Forward && index < mediaFiles.length - 1) {
      index += 1;
    }

    scrollRef.current?.scrollToIndex({ index });
  };

  useEffect(() => {
    setCurrentIndex(galleryItem);
  }, [galleryItem]);

  return (
    <Modal visible={galleryItem !== -1} onClose={() => setGalleryItem(-1)}>
      <>
        {currentIndex > 0 && (
          <View style={[styles.navigation, styles.left]}>
            <GalleryControlButton
              Icon={LeftArrowIcon}
              onPress={() => naviate(Directions.Backward)}
            />
          </View>
        )}

        {currentIndex < mediaFiles.length - 1 && (
          <View style={[styles.navigation, styles.right]}>
            <GalleryControlButton
              Icon={RightArrowIcon}
              onPress={() => naviate(Directions.Forward)}
            />
          </View>
        )}
      </>
      <View style={styles.spinnerContainer}>
        <Spinner />
      </View>
      <SwiperFlatList
        ref={scrollRef}
        data={mediaFiles}
        onChangeIndex={({ index }) => {
          setCurrentIndex(index);
        }}
        renderItem={props => {
          const {
            item: { type, uri },
          } = props;

          if (type === MediaTypes.Image) {
            return (
              <ImageZoom
                cropWidth={Dimensions.get('window').width}
                cropHeight={Dimensions.get('window').height}
                imageWidth={Dimensions.get('window').width}
                imageHeight={Dimensions.get('window').height}
              >
                <Image style={styles.video} source={{ uri }} />
              </ImageZoom>
            );
          }

          if (type === MediaTypes.PDF) {
            return (
              <View style={styles.video}>
                {Platform.OS === 'web' ? (
                  <View style={styles.spinnerContainer}>
                    <Button onPress={() => Linking.openURL(uri)}>
                      Download PDF
                    </Button>
                  </View>
                ) : (
                  <PDFReader source={{ uri }} />
                )}
              </View>
            );
          }

          return (
            <View style={styles.video}>
              <VideoPlayer media={uri} resizeMode={ResizeMode.CONTAIN} />
            </View>
          );
        }}
        index={galleryItem}
      />
    </Modal>
  );
};

const themedStyle = StyleService.create({
  video: {
    alignSelf: 'center',
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height,
    resizeMode: 'contain',
  },
  navigation: {
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    position: 'absolute',
    top: 0,
    height: '55%',
    flexDirection: 'row',
    zIndex: 1,
  },
  left: {
    left: 0,
  },
  right: {
    right: 0,
  },
  spinnerContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    height: '100%',
    width: '100%',
    zIndex: -1,
  },
});

export default Gallery;
