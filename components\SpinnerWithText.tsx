import * as React from 'react';
import { ActivityIndicator, View } from 'react-native';
import BackgroundUploadProgressContext, {
  BackgroundUploadStatuses,
} from '~context/BackgroundUploadProgressContext';
import { StyleService, Text } from '~node_modules/@ui-kitten/components';

type Props = {
  backgroundUploadTaskId: number;
};

const SpinnerWithText = ({
  backgroundUploadTaskId,
}: Props): React.ReactElement => {
  const { uploads } = React.useContext(BackgroundUploadProgressContext);

  return (
    <View style={styles.itemHeader}>
      <ActivityIndicator size="large" color="#999999" />
      {uploads.some(
        ({ backgroundUpload: { taskId }, status }) =>
          taskId === backgroundUploadTaskId &&
          status === BackgroundUploadStatuses.Started,
      ) && (
        <Text style={styles.text}>
          File upload in progress. You can navigate away from this screen but
          don&apos;t close the app until upload is finished.
        </Text>
      )}
    </View>
  );
};

const styles = StyleService.create({
  itemHeader: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    height: 200,
  },
  text: {
    textAlign: 'center',
    padding: 20,
  },
});

export default SpinnerWithText;
