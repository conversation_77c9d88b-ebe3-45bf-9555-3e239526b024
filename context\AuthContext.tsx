import React, {
  createContext,
  ReactNode,
  SetStateAction,
  useState,
  Dispatch,
  useEffect,
} from 'react';
import { getAuthToken } from '~services/auth';
import { useQueryClient } from '~node_modules/react-query';

const initialState: {
  authToken: string | null;
  setAuthToken: Dispatch<SetStateAction<string | null>>;
} = {
  authToken: '',
  setAuthToken: () => {},
};

const AuthContext = createContext(initialState);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const queryClient = useQueryClient();
  const [authToken, setAuthToken] = useState<string | null>(null);

  useEffect(() => {
    getAuthToken().then(token => {
      if (token) {
        setAuthToken(token);
      } else {
        setAuthToken('');
      }
    });
  }, []);

  useEffect(() => {
    if (!authToken) {
      queryClient.removeQueries();
    }
  }, [authToken, queryClient]);

  return (
    <AuthContext.Provider value={{ authToken, setAuthToken }}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
