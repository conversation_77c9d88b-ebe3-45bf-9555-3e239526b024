import { ListItem } from '@ui-kitten/components';
import React, { ReactElement } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import useTimezone from '~hooks/useTimezone';
import Currency from '~components/Currency';

type Props = {
  cost: string;
  date?: Date;
  description?: string;
  propertyName?: string;
  onPress?: () => void;
};

const PropertyInvoiceItem = ({
  date,
  description,
  cost,
  propertyName,
  onPress,
}: Props): ReactElement => {
  const { formatInTimeZone } = useTimezone();

  return (
    <ListItem onPress={onPress} style={styles.row}>
      <View style={styles.dateCell}>
        <Text>{formatInTimeZone(date, 'PP')}</Text>
      </View>
      <View style={styles.descriptionCell}>
        {description && <Text>{description}</Text>}
        {propertyName && <Text>{propertyName}</Text>}
      </View>
      <View style={styles.costCell}>
        {!!cost && <Currency value={cost} style={styles.rightText} />}
      </View>
    </ListItem>
  );
};

export default PropertyInvoiceItem;

const styles = StyleSheet.create({
  row: { display: 'flex', flexDirection: 'row', alignItems: 'flex-start' },
  rightText: { textAlign: 'right' },
  dateCell: { width: '25%' },
  descriptionCell: { width: '50%' },
  costCell: { width: '25%', textAlign: 'right' },
});
