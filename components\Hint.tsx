import * as React from 'react';
import {
  Button,
  StyleService,
  Text,
  Tooltip,
} from '~node_modules/@ui-kitten/components';
import { InfoIcon } from './Icon';

type Props = {
  text: string;
  size?: 'tiny' | 'small' | 'medium' | 'large' | 'giant';
};

const Hint = ({ text, size = 'medium' }: Props): React.ReactElement => {
  const [visible, setVisible] = React.useState(false);

  const renderToggleButton = () => (
    <Button
      onPress={() => setVisible(true)}
      accessoryLeft={InfoIcon}
      appearance="ghost"
      size={size}
    />
  );

  return (
    <Tooltip
      style={styles.tooltip}
      anchor={renderToggleButton}
      visible={visible}
      onBackdropPress={() => setVisible(false)}
    >
      {() => (
        <Text category="h1" style={styles.tooltipText}>
          {text}
        </Text>
      )}
    </Tooltip>
  );
};

const styles = StyleService.create({
  tooltip: {
    width: 260,
    backgroundColor: 'white',
    borderColor: '#999',
  },
  tooltipText: {
    paddingHorizontal: 5,
    paddingVertical: 10,
    fontSize: 15,
    fontWeight: 'normal',
    color: '#666',
    lineHeight: 20,
  },
});

export default Hint;
