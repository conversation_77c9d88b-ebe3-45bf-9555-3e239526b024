import {
  IndexPath,
  Select,
  SelectItem,
  StyleService,
  Text,
} from '@ui-kitten/components';
import { Field } from 'formik';
import React, { ReactElement } from 'react';

type Props = {
  formLabel?: string;
  name: string;
  options: Array<any>;
  style?: any;
  onSelect?: any;
  showEmpty?: boolean;
  disabled?: boolean;
};

const SelectInput = ({
  formLabel,
  name,
  options: initialOptions,
  style,
  onSelect,
  showEmpty,
  disabled,
}: Props): ReactElement => {
  const options = [
    ...(showEmpty ? [{ label: 'Please Select', value: '', color: '' }] : []),
    ...initialOptions,
  ];

  return (
    <Field>
      {({ form: { values, setFieldValue } }) => {
        const selectedIndex =
          options.findIndex(x => x.value === values[name]) || 0;
        const selectedValue = options[selectedIndex];

        if (!selectedValue) {
          return null;
        }

        return (
          <Select
            label={formLabel}
            style={(styles.input, { ...style })}
            selectedIndex={new IndexPath(selectedIndex)}
            value={
              selectedValue.color
                ? () => (
                    <Text
                      style={{
                        ...styles.customSelectContent,
                        color: selectedValue.color,
                      }}
                    >
                      {selectedValue.label}
                    </Text>
                  )
                : selectedValue.label
            }
            onSelect={index => {
              const { value } = options[index - 1];
              if (onSelect) {
                onSelect(index);
              }
              setFieldValue(name, value);
            }}
            disabled={disabled}
          >
            {options.map(({ value, label, color }) => (
              <SelectItem
                key={value}
                title={
                  color
                    ? () => (
                        <Text style={{ ...styles.customSelectContent, color }}>
                          {label}
                        </Text>
                      )
                    : label
                }
              />
            ))}
          </Select>
        );
      }}
    </Field>
  );
};

export default SelectInput;

const styles = StyleService.create({
  input: {
    marginHorizontal: 12,
    marginVertical: 8,
  },
  customSelectContent: {
    marginLeft: 8,
  },
});
