import { StatusBar } from 'expo-status-bar';
import React, { useRef } from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import * as eva from '@eva-design/eva';
import { ApplicationProvider, IconRegistry } from '@ui-kitten/components';
import { EvaIconsPack } from '@ui-kitten/eva-icons';
import { RootSiblingParent } from 'react-native-root-siblings';
import { QueryClient, QueryClientProvider } from 'react-query';
import { LogBox } from 'react-native';
import * as Sentry from '@sentry/react-native';
import { ToastProvider } from 'react-native-toast-notifications';
import { default as theme } from './theme.json';
import useCachedResources from './hooks/useCachedResources';
import useColorScheme from './hooks/useColorScheme';
import Navigation from './navigation';
import { AuthProvider } from '~context/AuthContext';
import NoInternetOverlay from '~components/NoInternetOverlay';
import Banner from '~components/Banner';
import NotificationHandler from '~components/NotificationHandler';
import MaterialIconsPack from './components/MaterialIcons';
import BackgroundUploadOverlay from '~components/BackgroundUploadOverlay';
import { BackgroundUploadContextProvider } from '~context/BackgroundUploadProgressContext';
import ForceUpdate from '~components/ForceUpdate';

const queryClient = new QueryClient();
LogBox.ignoreLogs(['Setting a timer for a long period of time']);

Sentry.init({
  dsn: 'https://<EMAIL>/4504022279651328',
  debug: true, // If `true`, Sentry will try to print out useful debugging information if something goes wrong with sending the event. Set it to `false` in production
});

const App = (): JSX.Element | null => {
  const isLoadingComplete = useCachedResources();
  const colorScheme = useColorScheme();

  const nav = useRef(null);

  if (!isLoadingComplete) {
    return null;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <BackgroundUploadContextProvider>
          <RootSiblingParent>
            <IconRegistry icons={[EvaIconsPack, MaterialIconsPack]} />
            <ApplicationProvider {...eva} theme={{ ...eva.light, ...theme }}>
              <ForceUpdate>
                <SafeAreaProvider>
                  <NoInternetOverlay>
                    <NotificationHandler nav={nav}>
                      <Banner nav={nav}>
                        <ToastProvider
                          placement="center"
                          animationType="zoom-in"
                          duration={6000}
                          textStyle={{ margin: 10 }}
                        >
                          <Navigation colorScheme={colorScheme} ref={nav} />
                        </ToastProvider>
                      </Banner>
                    </NotificationHandler>
                    <StatusBar />
                  </NoInternetOverlay>
                </SafeAreaProvider>
              </ForceUpdate>
              <BackgroundUploadOverlay />
            </ApplicationProvider>
          </RootSiblingParent>
        </BackgroundUploadContextProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
};

export default Sentry.wrap(App);
