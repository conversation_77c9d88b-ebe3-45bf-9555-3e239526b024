import { StackNavigationProp } from '@react-navigation/stack';
import { List, StyleService, useStyleSheet } from '@ui-kitten/components';
import React, { ReactElement } from 'react';
import { ListRenderItemInfo } from 'react-native';
import pluralize from 'pluralize';
import { format } from 'date-fns';
import { AvailableTasksParamList, PropertyTask, TaskGroup } from '~types';
import PropertyCard from './PropertyCard';
import RecommendedTaskDayBlock from './RecommendedTaskDayBlock';
import RecommemdedCustomGroupButton from './RecommendedCustomGroupButton';

type Props = {
  navigation: StackNavigationProp<
    AvailableTasksParamList,
    'RecommendedTaskListingScreen'
  >;
  taskGroups: TaskGroup[];
  compactModeEnabled: boolean;
  textSearch?: string;
};

const margin = 2;

const RecommendedTasksContent = ({
  navigation,
  taskGroups,
  compactModeEnabled,
  textSearch,
}: Props): ReactElement => {
  const styles = useStyleSheet(themedStyles);

  const renderPropertyItem = (
    info: ListRenderItemInfo<PropertyTask>,
    date: string | number,
  ): React.ReactElement => {
    const { item } = info;
    const { propertyName } = item;
    const { property } = item.tasks[0];
    const propertyTasks = item.tasks;

    const title = `${propertyName}: ${propertyTasks.length} ${pluralize(
      'job',
      propertyTasks.length,
    )}`;

    return (
      <PropertyCard
        name={title}
        address={property.address}
        key={property.id}
        marginVertical={margin}
        onPress={() =>
          navigation.push('RecommendedTaskListingScreen', {
            headerTitle: title,
            propertyId: property.id,
            date: format(new Date(date), 'yyyy-MM-dd'),
            compactModeEnabled,
            textSearch,
          })
        }
      />
    );
  };

  const HIGHLIGHT_TYPES = [
    { id: 1, title: 'Overdue', filter: 'overdue' },
    { id: 2, title: 'Urgent', filter: 'urgent' },
  ];

  const renderGroupItem = (
    info: ListRenderItemInfo<TaskGroup>,
  ): React.ReactElement => {
    const { item } = info;
    const { date: highlightTypeId } = item;

    const highlightType = HIGHLIGHT_TYPES.find(
      ({ id }) => id === (highlightTypeId as unknown as number),
    );

    if (highlightType) {
      const { title, filter } = highlightType;

      return (
        <RecommemdedCustomGroupButton
          name={title}
          taskGroup={item}
          onPress={() =>
            navigation.push('RecommendedTaskListingScreen', {
              headerTitle: title,
              compactModeEnabled,
              urgent: filter === 'urgent' ? true : undefined,
              overdue: filter === 'overdue' ? true : undefined,
              textSearch,
            })
          }
        />
      );
    }

    return (
      <RecommendedTaskDayBlock
        taskGroup={item}
        renderPropertyItem={renderPropertyItem}
      />
    );
  };

  return (
    <List style={styles.list} data={taskGroups} renderItem={renderGroupItem} />
  );
};

const themedStyles = StyleService.create({
  list: { padding: 16 },
  blockContainer: { marginTop: 8, marginVertical: 32 },
  heading: {
    fontSize: 20,
    fontWeight: '800',
    marginBottom: 8,
    color: '#333',
  },
});

export default RecommendedTasksContent;
