import { AxiosPromise } from 'axios';
import { useContext } from 'react';
import { Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';
import { PaginatedResult, Task, TaskAssignment, TaskLog, User } from '~types';
import useClient, { formData } from './useClient';
import AuthContext from '~context/AuthContext';
import BackgroundUploadProgressContext from '~context/BackgroundUploadProgressContext';
import downloadAsync from './downloadAsync';
import { useQueryClient } from '~node_modules/react-query';
import { handleBackgroundUploadStatus } from '~api/backgroundUpload';

export enum TaskAssignmentType {
  My = 'my',
  Completed = 'completed',
}

const apiBaseUrl = process.env.API_BASE_URL;

export default () => {
  const { authToken } = useContext(AuthContext);
  const { setUploads } = useContext(BackgroundUploadProgressContext);
  const client = useClient();
  const queryClient = useQueryClient();

  const getAvailableTasks = ({
    propertyId,
    textSearch,
    groupByDateAndProperty,
  }: {
    propertyId?: number;
    textSearch?: string;
    groupByDateAndProperty?: boolean;
  }): AxiosPromise =>
    client
      .get('/v2/tasks/available/all', {
        params: {
          property_id: propertyId,
          text_search: textSearch,
          group_by_date_and_property: groupByDateAndProperty,
        },
      })
      .then(res => res.data?.data);

  const getFilteredAvailableTasks = ({
    propertyId,
    date,
    urgent,
    overdue,
    textSearch,
  }: {
    propertyId?: number;
    date?: string;
    urgent?: boolean;
    overdue?: boolean;
    textSearch?: string;
  }): Promise<Task[]> => {
    let filter = 'filtered';
    if (urgent) {
      filter = 'urgent';
    }
    if (overdue) {
      filter = 'overdue';
    }

    const url = `/v2/tasks/available/${filter}`;
    return client
      .get(url, {
        params: {
          property_id: propertyId,
          date,
          text_search: textSearch,
        },
      })
      .then(res => res.data?.data);
  };

  const getAllTasks = (
    propertyId: number | null = null,
    userId: number | null = null,
    statusName: string | null = null,
    dateToFilter: number | null = null,
    recurringFilter: string | null = null,
    textSearch: string | null = null,
    blockedFilter: string | null = null,
    page: number = 1,
  ): AxiosPromise => {
    const url = `/tasks/all`;

    return client
      .get(url, {
        params: {
          property_id: propertyId,
          user_id: userId,
          status: statusName,
          date: dateToFilter,
          recurring_filter: recurringFilter,
          text_search: textSearch,
          show_blocked: blockedFilter,
          page,
        },
      })
      .then(res => res.data);
  };

  const getTask = (
    id: number,
    showCompletions?: boolean,
    copiedForToday?: boolean,
  ): Promise<Task> =>
    client
      .get(`/tasks/${id}`, {
        params: {
          show_completions: showCompletions,
          copied_for_today: copiedForToday,
        },
      })
      .then<Task>(res => res.data.data);

  const acceptTask = ({
    id,
    date = null,
  }): Promise<{
    taskAssignment?: TaskAssignment;
    message: { text: string; denied?: boolean };
  }> => client.post(`/tasks/${id}/accept`, { date }).then(res => res.data.data);

  const updateTask = ({ id, ...updatedTask }: Task): Promise<Task> => {
    const { data, config } = formData(updatedTask);
    data.append('_method', 'PUT');

    return client.post(`/tasks/${id}`, data, config).then(res => res.data.data);
  };

  const addTask = async (task: Task) => {
    const { media, thumbnail } = task;

    if (Platform.OS === 'web') {
      const { data, config } = formData(task);
      const response = await client.post('/tasks', data, config);

      return response.data.data;
    }

    const response = await client.post('/tasks', {
      ...task,
      media: undefined,
      thumbnail: undefined,
    });
    const { id } = response.data.data;
    const uploadUrl = `${apiBaseUrl}/tasks/${id}/media`;

    return {
      promises: handleBackgroundUploadStatus(
        [
          { uploadUrl, localUrl: media.uri, type: 'initial-media', taskId: id },
          {
            uploadUrl,
            localUrl: thumbnail.uri,
            type: 'initial-media-thumbnail',
            taskId: id,
          },
        ],
        authToken,
        setUploads,
        queryClient,
      ),
      id,
    };
  };

  const deleteTask = (id: number): AxiosPromise =>
    client.delete(`/tasks/${id}`).then(res => res.data.data);

  const unblockTask = (id: number): AxiosPromise =>
    client.post(`/tasks/${id}/ignore-expiration`).then(res => res.data.data);

  const getTaskAssignments = (
    status: TaskAssignmentType | null = null,
    month: Date | null = null,
    userId: number | null = null,
    propertyId: number | null = null,
    showAllInTheAccount: boolean | null = null,
    groupByTechnicians: boolean | null = null,
  ): Promise<TaskAssignment[]> => {
    let url = '/task-assignments';

    if (status) {
      url += `/${status}`;
    }

    return client
      .get(url, {
        params: {
          selected_month: month,
          user_id: userId,
          property_id: propertyId,
          show_all_in_the_account: Number(showAllInTheAccount),
          group_by_technicians: Number(groupByTechnicians),
        },
      })
      .then(res => res.data.data);
  };

  const getTaskAssignment = (id: string): Promise<TaskAssignment> =>
    client
      .get<{ data: TaskAssignment }>(`/task-assignments/${id}`)
      .then(res => res.data.data);

  const finishTaskAssignment = async ({ id, ...task }) => {
    const temporaryTask = task;

    const { video, thumbnail, receipts } = task;

    if (Platform.OS !== 'web') {
      delete temporaryTask.receipts;
      delete temporaryTask.thumbnail;
      delete temporaryTask.video;
    }

    const { data, config } = formData(task);

    const response = await client.post(
      `/task-assignments/${id}/finish`,
      data,
      config,
    );

    if (Platform.OS === 'web') {
      return response.data.data;
    }

    // If we undo finish task just a finished_at: null will be sent, no files
    const videoUri = video?.uri;
    const thumbnailUri = thumbnail?.uri;
    if (!videoUri || !thumbnailUri) {
      return [];
    }

    const taskAssignmentId = response.data.data.id;
    const uploadUrl = `${apiBaseUrl}/task-assignments/${taskAssignmentId}/media`;

    handleBackgroundUploadStatus(
      [
        { uploadUrl, localUrl: videoUri, type: 'completion-media', taskId: id },
        {
          uploadUrl,
          localUrl: thumbnailUri,
          type: 'completion-media-thumbnail',
          taskId: id,
        },
        ...receipts.map(({ uri }) => ({
          uploadUrl,
          localUrl: uri,
          type: 'receipt',
          taskId: id,
        })),
      ],
      authToken,
      setUploads,
      queryClient,
    );

    return response.data.data;
  };

  const unfinishTaskAssignment = (id: number): AxiosPromise =>
    client.post(`/task-assignments/${id}/unfinish`).then(res => res.data.data);

  const removeTaskAssignment = (id: number): AxiosPromise =>
    client.delete(`/task-assignments/${id}`).then(res => res.data.data);

  const rejectTaskAssignment = ({ id, ...task }): AxiosPromise => {
    const { data, config } = formData(task);

    return client
      .post(`/task-assignments/${id}/reject`, data, config)
      .then(res => res.data.data);
  };

  const getInvoicesPdfUrl = (
    selectedMonth: string,
    propertyId?: number | null,
    includeToken?: boolean,
  ): string => {
    const apiUrl = `${apiBaseUrl}/task-assignments/completed`;

    let url = `${apiUrl}?selected_month=${selectedMonth}&show_all_in_the_account=1&group_by_technicians=1&generate_pdf=1`;

    if (propertyId) {
      url += `&property_id=${propertyId}`;
    }

    if (includeToken) {
      url += `&token=${authToken}`;
    }

    return url;
  };

  const getTasksCreatedByMe = (): AxiosPromise =>
    client.get('/tasks/created-by-me').then(res => res.data.data);

  const downloadInvoicesPdf = (
    selectedMonth: string,
    propertyId?: number | null,
    filename?: string,
  ): Promise<FileSystem.FileSystemDownloadResult> =>
    downloadAsync(
      getInvoicesPdfUrl(selectedMonth, propertyId),
      authToken,
      filename,
    );

  const getTechnicians = (month: Date | null = null): Promise<User[]> =>
    client
      .get('/accounts/technicians', {
        params: {
          with_balances_for_month: month,
        },
      })
      .then(res => res.data.data);

  const getUnpaidInvoices = (): AxiosPromise => {
    const url = '/task-assignments/sum';

    return client.get(url).then(res => res.data.data);
  };

  const getTaskLogs = (
    taskId: number | null = null,
    taskAssignmentId: number | null = null,
  ): AxiosPromise =>
    client
      .get('/task-logs', {
        params: {
          taskId,
          taskAssignmentId,
        },
      })
      .then(res => res.data.data);

  const getActivityLogs = (page: number): Promise<PaginatedResult<TaskLog>> =>
    client.get(`/task-logs/all?page=${page || 1}`).then(res => res.data);

  return {
    getAllTasks,
    getTask,
    acceptTask,
    updateTask,
    addTask,
    deleteTask,
    getTaskAssignments,
    getTaskAssignment,
    finishTaskAssignment,
    unfinishTaskAssignment,
    removeTaskAssignment,
    rejectTaskAssignment,
    downloadInvoicesPdf,
    getTasksCreatedByMe,
    getInvoicesPdfUrl,
    getTechnicians,
    getUnpaidInvoices,
    getTaskLogs,
    getActivityLogs,
    unblockTask,
    getAvailableTasks,
    getFilteredAvailableTasks,
  };
};
