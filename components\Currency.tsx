import React from 'react';
import CurrencyFormat from 'react-currency-format';
import { Text, TextStyle } from 'react-native';
import { StyleProp } from '~node_modules/react-native';

type Props = {
  value: string | number;
  style?: StyleProp<TextStyle>;
};
const Currency = ({ value, style }: Props) => (
  <CurrencyFormat
    value={value}
    displayType="text"
    thousandSeparator
    decimalScale={2}
    fixedDecimalScale
    prefix="$"
    renderText={v => <Text style={style}>{v}</Text>}
  />
);

export default Currency;
