import React, { ReactElement, useState } from 'react';
import { <PERSON><PERSON>, Card, Text } from '@ui-kitten/components';
import { Image, StyleSheet, View, TouchableOpacity } from 'react-native';
import CurrencyFormat from 'react-currency-format';
import {
  determineMediaType,
  convertDecimalToHoursMinutes,
  MediaTypes,
} from 'helpers';
import { MediaFile, TaskAssignment } from '~types';
import useTimezone from '~hooks/useTimezone';
import Gallery from './Gallery';
import User from './User';
import RejectTask from './RejectTask';
import UndoFinishTask from './UndoFinishTask';

type Props = {
  navigation;
  taskAssignment: TaskAssignment;
  currentUserIsAdmin: boolean;
  currentUserIsTheTaskOwner: boolean;
};

const TaskCompletionCard = ({
  navigation,
  taskAssignment: {
    id: taskAssignmentId,
    thumbnail,
    video,
    comments,
    costOfLabor,
    costOfMaterials,
    finishedAt,
    receipts,
    task,
    user,
    timeSpent,
    hourlyRate,
  },
  currentUserIsAdmin,
  currentUserIsTheTaskOwner,
}: Props): ReactElement => {
  const { formatInTimeZone } = useTimezone();

  const [galleryItemIndex, setGalleryItemIndex] = useState(-1);
  const mediaFiles = [
    {
      id: 'taskMedia',
      type: determineMediaType(task.media),
      thumbnailUri: task.thumbnail,
      uri: task.media,
    },
    {
      id: 'taskAssignmentMedia',
      type: determineMediaType(video),
      thumbnailUri: thumbnail,
      uri: video,
    },
    ...receipts.map(({ mediaUrl, type: mimeType }, index) => ({
      id: index,
      thumbnailUri: mediaUrl,
      uri: mediaUrl,
      type:
        mimeType === 'image/jpeg' || mimeType === 'image/png'
          ? MediaTypes.Image
          : MediaTypes.PDF,
    })),
  ] as Array<MediaFile>;

  const { hours, minutes } = convertDecimalToHoursMinutes(Number(timeSpent));

  return (
    <Card style={styles.item}>
      <View style={styles.container}>
        <Text category="h4" style={styles.marginBottom}>
          Completion details
        </Text>
        <View style={styles.marginBottom}>
          <Text category="s2">
            Completed by <User user={user} style={styles.bold} />
          </Text>
          <Text category="s2">
            on{' '}
            <Text style={styles.bold}>
              {formatInTimeZone(finishedAt, 'MMM d, y h:mm aaa')}
            </Text>
          </Text>
        </View>
        {comments && (
          <View style={styles.marginBottom}>
            <Text category="h6">Comments</Text>
            <Text category="s1">{comments}</Text>
          </View>
        )}
        <Text category="h6">Costs</Text>
        <View style={styles.marginBottom}>
          <CurrencyFormat
            value={costOfLabor}
            displayType="text"
            thousandSeparator
            decimalScale={2}
            prefix="$"
            renderText={value => (
              <Text category="s1">
                Cost of labor: <Text style={styles.bold}>{value}</Text> ({hours}
                h {minutes}m @ ${hourlyRate}
                /hour)
              </Text>
            )}
          />
          {costOfMaterials > 0 && (
            <CurrencyFormat
              value={costOfMaterials}
              displayType="text"
              thousandSeparator
              decimalScale={2}
              prefix="$"
              renderText={value => (
                <Text category="s1">
                  Cost of materials: <Text style={styles.bold}>{value}</Text>
                </Text>
              )}
            />
          )}
        </View>
        <Text category="h6">Media files</Text>
        {mediaFiles && (
          <Gallery
            mediaFiles={mediaFiles}
            galleryItem={galleryItemIndex}
            setGalleryItem={setGalleryItemIndex}
          />
        )}
        <View style={styles.receipts}>
          {mediaFiles.map(({ id, thumbnailUri, type }, index) => (
            <TouchableOpacity
              key={id}
              onPress={() => setGalleryItemIndex(index)}
            >
              {type === MediaTypes.PDF ? (
                <View style={styles.receipt}>
                  <Text>PDF</Text>
                </View>
              ) : (
                <Image
                  style={styles.receipt}
                  source={{
                    uri: thumbnailUri,
                  }}
                />
              )}
            </TouchableOpacity>
          ))}
        </View>
        {(currentUserIsTheTaskOwner || currentUserIsAdmin) && (
          <Button
            style={styles.button}
            onPress={() =>
              navigation.navigate('FinishTaskScreen', {
                taskAssignmentId,
              })
            }
          >
            Edit
          </Button>
        )}
        {currentUserIsTheTaskOwner && (
          <UndoFinishTask navigation={navigation} id={taskAssignmentId} />
        )}
        {currentUserIsAdmin && (
          <RejectTask navigation={navigation} id={taskAssignmentId} />
        )}
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  item: {
    borderRadius: 0,
    margin: 16,
  },
  container: {
    alignItems: 'flex-start',
    marginTop: 0,
    marginHorizontal: -8,
  },
  receipts: {
    display: 'flex',
    flexDirection: 'row',
    marginTop: 12,
  },
  receipt: {
    backgroundColor: 'gray',
    width: 60,
    height: 60,
    borderRadius: 5,
    marginRight: 12,
    marginBottom: 12,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  marginBottom: {
    marginBottom: 16,
  },
  bold: {
    fontWeight: 'bold',
  },
  button: {
    marginTop: 16,
    width: '100%',
  },
});

export default TaskCompletionCard;
