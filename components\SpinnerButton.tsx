import React from 'react';
import { View } from 'react-native';

import { ButtonProps } from '@ui-kitten/components';
import { But<PERSON>, Spinner } from '~node_modules/@ui-kitten/components';

const LoadingIndicator = ({ size = 'small' }) => (
  <View style={{ marginVertical: -20 }}>
    <Spinner size={size} status="basic" />
  </View>
);

type Props = ButtonProps & {
  text: string;
  isLoading: boolean;
  disabled?: boolean;
};

const SpinnerButton = ({
  text,
  isLoading,
  style = {},
  disabled = false,
  size = 'giant',
  ...props
}: Props) => (
  <Button
    style={style}
    size={size}
    accessoryLeft={isLoading ? <LoadingIndicator size={size} /> : undefined}
    disabled={isLoading || disabled}
    {...props}
  >
    {text}
  </Button>
);

export default SpinnerButton;
