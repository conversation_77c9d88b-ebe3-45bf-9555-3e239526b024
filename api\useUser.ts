import useClient from './useClient';
import { User } from '~types';

export default () => {
  const client = useClient();

  const getUser = (accountId = null): Promise<User> =>
    client
      .get(`/auth/user-profile`, {
        params: {
          account_id: accountId,
        },
      })
      .then<User>(res => res.data);

  const updateUser = (data: Partial<User>) =>
    client.put(`/users/me`, data).then(res => res.data);

  const getAccountMembers = (
    accountId: number,
    showDeleted: boolean | null = null,
  ): Promise<User[]> =>
    client
      .get(`/accounts/${accountId}/members`, {
        params: {
          show_deleted: showDeleted,
        },
      })
      .then<User[]>(res => res.data.data);

  const getAccountMember = (accountId: number, memberId: number) =>
    client
      .get(`/accounts/${accountId}/members/${memberId}`)
      .then(res => res.data.data);

  const editAccountMember = ({
    accountId,
    memberId,
    role,
    skillsetIds = null,
    isSkillsetLocked = null,
    hourlyRate = null,
    jobLimit = null,
    allowedPropertyIds = null,
    housekeepingRate = null,
  }: {
    accountId: string;
    memberId: string;
    role: number;
    skillsetIds: Array<number> | null;
    isSkillsetLocked: boolean | null;
    hourlyRate: string | null;
    jobLimit: string | null;
    allowedPropertyIds: number[] | null;
    housekeepingRate: string | null;
  }) =>
    client
      .put(`/accounts/${accountId}/members/${memberId}`, {
        role,
        skillsetIds,
        isSkillsetLocked,
        hourlyRate,
        jobLimit,
        allowedPropertyIds,
        housekeepingRate,
      })
      .then(res => res.data.data);

  const deleteAccountMember = ({ accountId, memberId }) =>
    client
      .delete(`/accounts/${accountId}/members/${memberId}`)
      .then(res => res.data.data);

  const postInvitation = data =>
    client.post(`/invitations`, data).then(res => res.data);

  const getInvitation = (id: number) =>
    client.get(`/invitations/${id}`).then(res => res.data.data);

  const setInvitationRole = ({ id, role }) =>
    client.put(`/invitations/${id}`, { role }).then(res => res.data.data);

  const getPendingInvitations = () =>
    client.get('/invitations').then(res => res.data.data);

  const deleteInvitation = (id: number) =>
    client.delete(`/invitations/${id}`).then(res => res.data.data);

  const putAccount = (id: number, data) =>
    client.put(`/accounts/${id}`, data).then(res => res.data.data);

  const deleteAccount = () =>
    client.delete('/users/me').then(res => res.data.data);

  return {
    getUser,
    updateUser,
    getAccountMembers,
    getAccountMember,
    editAccountMember,
    deleteAccountMember,
    postInvitation,
    getInvitation,
    setInvitationRole,
    getPendingInvitations,
    deleteInvitation,
    putAccount,
    deleteAccount,
  };
};
