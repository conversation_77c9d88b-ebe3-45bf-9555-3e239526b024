import React from 'react';
import { View } from 'react-native';
import {
  Menu,
  MenuItem,
  StyleService,
  useStyleSheet,
} from '@ui-kitten/components';
import { StackNavigationProp } from '@react-navigation/stack';
import {
  ActivitiesIcon,
  ListOutlineIcon,
  HelpIcon,
  PeopleIcon,
} from '~components/Icon';
import { MoreParamList } from '~types';
import useCurrentUser from '~hooks/useCurrentUser';

type Props = {
  navigation: StackNavigationProp<MoreParamList, 'MoreScreen'>;
};

const More = ({ navigation }: Props): React.ReactElement => {
  const styles = useStyleSheet(themedStyle);

  const { isAdmin, isManager } = useCurrentUser();

  const menuItems = [
    <MenuItem
      key="activities"
      style={styles.menuItem}
      title="Activity"
      accessoryLeft={ActivitiesIcon}
      onPress={() => navigation.navigate('ActivitiesScreen')}
    />,
    <MenuItem
      key="support"
      style={styles.menuItem}
      title="Support"
      accessoryLeft={HelpIcon}
      onPress={() => navigation.navigate('SupportScreen')}
    />,
  ];

  if (isAdmin || isManager) {
    menuItems.push(
      <MenuItem
        key="all-tasks"
        style={styles.menuItem}
        title="All Jobs"
        accessoryLeft={ListOutlineIcon}
        onPress={() => navigation.navigate('AllTasksScreen')}
      />,
    );
  }

  if (isAdmin) {
    menuItems.push(
      <MenuItem
        key="admin"
        style={styles.menuItem}
        title="Admin"
        accessoryLeft={PeopleIcon}
        onPress={() => navigation.navigate('AdminScreen')}
      />,
    );
  }

  return (
    <View>
      <Menu contentContainerStyle={styles.menu}>{menuItems}</Menu>
    </View>
  );
};

const themedStyle = StyleService.create({
  menu: {
    height: '100%',
  },
  menuItem: {
    flexGrow: 1,
    flexBasis: '20%',
  },
});

export default More;
