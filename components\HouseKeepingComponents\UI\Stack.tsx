import React, { ComponentProps } from 'react';
import { View } from 'react-native';
import { StyleService, useStyleSheet } from '@ui-kitten/components';

type Props = ComponentProps<typeof View>;

const Stack = ({ style, ...props }: Props) => {
  const styles = useStyleSheet(themedStyles);

  return <View style={[styles.stack, style]} {...props} />;
};

export default Stack;

const themedStyles = StyleService.create({
  stack: {
    gap: 16,
    flexDirection: 'column',
    alignItems: 'stretch',
    justifyContent: 'flex-start',
  },
});
