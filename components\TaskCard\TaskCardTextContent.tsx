import { StyleService, Text } from '@ui-kitten/components';
import React, { ReactElement } from 'react';
import { View, TextStyle } from 'react-native';
import { User as UserType } from 'types';
import Hyperlink from 'react-native-hyperlink';
import TaskCardPreferredDate from '~components/TaskCardPreferredDate';
import useTimezone from '~hooks/useTimezone';
import { TaskPriorities, TaskRecurringIntervals } from '~dummyData';
import CardLineItem from '~components/CardLineItem';
import Spinner from '~components/Spinner';
import { Property, Skill } from '~types';
import User from '~components/User';
import TaskCardAcceptedAndCompletedDates from '~components/TaskCardAcceptedAndCompletedDates';
import { carrotColor, linkColor } from '~constants/Colors';
import { getRecurringIntervalName } from '~components/TaskCard/TaskCard';

type Props = {
  description: string;
  priority: TaskPriorities;
  isAssignment?: boolean;
  copiedForToday?: boolean;
  preferredDate: string;
  finishedAt?: Date;
  property: Property;
  recurring: TaskRecurringIntervals;
  requiredSkills: Skill[];
  createdAt: Date;
  creatorUser: UserType;
  acceptedByUser?: UserType;
  acceptedOn?: Date;
  overdue: number;
  showDetails?: boolean;
  propertyAvailability: string;
};

const TaskCardTextContent = ({
  description,
  priority,
  isAssignment,
  copiedForToday,
  preferredDate,
  finishedAt,
  property,
  recurring,
  requiredSkills,
  createdAt,
  creatorUser,
  acceptedByUser,
  acceptedOn,
  overdue,
  showDetails,
  propertyAvailability,
}: Props): ReactElement => {
  const { formatInTimeZone } = useTimezone();

  const recurringId = Number(recurring);
  const formattedCreatedAt = formatInTimeZone(createdAt, 'MMM d, y h:mm aaa');

  const strikeThroughStyle: TextStyle = {
    textDecorationLine: property?.deletedAt ? 'line-through' : 'none',
  };

  const recurringIntervalName = getRecurringIntervalName(Number(recurring));

  if (!recurringIntervalName) {
    return <Spinner />;
  }

  return (
    <>
      <Text category="h5" numberOfLines={2} ellipsizeMode="tail">
        {description}
      </Text>
      {showDetails && (
        <Hyperlink linkDefault linkStyle={{ color: linkColor }}>
          <Text style={styles.description}>{description}</Text>
        </Hyperlink>
      )}
      <View style={styles.itemFooter}>
        <TaskCardPreferredDate
          priority={priority}
          isAssignment={isAssignment}
          preferredDate={preferredDate}
          styles={styles.attribute}
          copiedForToday={copiedForToday}
          overdueInDays={overdue}
          finishedAt={finishedAt}
          propertyAvailability={propertyAvailability}
        />
        {recurringId > 0 && (
          <CardLineItem
            text={recurringIntervalName}
            iconName="repeat-outline"
          />
        )}
        <CardLineItem
          text={property?.name}
          textStyle={strikeThroughStyle}
          iconName="home-outline"
        />
        <CardLineItem
          text={property?.address.replace(/\n/g, ' ')}
          textStyle={strikeThroughStyle}
          iconName="pin-outline"
        />
        <CardLineItem
          text={requiredSkills.map(({ name }) => name).join(',')}
          iconName="color-palette-outline"
        />
        <CardLineItem
          text={
            <Text style={styles.text}>
              Added by <User user={creatorUser} style={styles.text} /> on{' '}
              {formattedCreatedAt}
            </Text>
          }
          iconName="person-add-outline"
        />
        {acceptedByUser && acceptedOn && (
          <CardLineItem
            text={
              <TaskCardAcceptedAndCompletedDates
                acceptedByUser={acceptedByUser}
                acceptedOn={acceptedOn}
                finishedAt={finishedAt}
              />
            }
            iconName="person-add-outline"
          />
        )}
      </View>
    </>
  );
};

export default TaskCardTextContent;

const styles = StyleService.create({
  itemFooter: {
    alignItems: 'flex-start',
    marginTop: 16,
    marginHorizontal: -18,
  },
  attribute: {
    marginTop: -12,
  },
  text: {
    paddingTop: 5,
    fontSize: 12,
    fontWeight: 'bold',
    color: carrotColor,
  },
  description: {
    paddingTop: 12,
  },
});
