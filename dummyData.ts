import { RecurringInterval, Priority, Role, TaskLogActionType } from './types';

export const recurringIntervals: RecurringInterval[] = [
  {
    id: 0,
    name: 'Just once',
  },
  {
    id: 1,
    name: 'Weekly',
  },
  {
    id: 2,
    name: 'Bi-Weekly',
  },
  {
    id: 3,
    name: 'Monthly',
  },
  {
    id: 4,
    name: 'Every 3 Months',
  },
  {
    id: 5,
    name: 'Every 6 Months',
  },
  {
    id: 6,
    name: 'Yearly',
  },
];

export enum TaskRecurringIntervals {
  OneTime = recurringIntervals[0].id,
  Weekly = recurringIntervals[1].id,
  BiWeekly = recurringIntervals[2].id,
  Monthly = recurringIntervals[3].id,
  Every3Months = recurringIntervals[4].id,
  Every6Months = recurringIntervals[5].id,
  Yearly = recurringIntervals[6].id,
}

export const priorities: Priority[] = [
  {
    id: 0,
    name: 'Anytime',
    info: 'This job can be completed anytime regardless of the property being vacant or not. These type of jobs are usually scheduled for work to be performed outside of living areas.',
  },
  {
    id: 3,
    name: 'Urgent',
    info: 'This job must be completed immediately and technicians automatically have permission to enter living areas even if the property is occupied.',
  },
  {
    id: 1,
    name: 'On a set date',
    info: '',
  },
  {
    id: 2,
    name: 'On checkout day',
    info: 'This job will be primarily scheduled for checkout days when housekeeping is performed.',
  },
];

export enum TaskPriorities {
  Anytime = priorities[0].id,
  Urgent = priorities[1].id,
  OnSetDate = priorities[2].id,
  OnCheckoutDate = priorities[3].id,
}

export const taskLogActions: TaskLogActionType[] = [
  {
    id: 0,
    name: 'accepted “{{job}}”',
    icon: 'checkmark-outline',
  },
  {
    id: 1,
    name: 'released “{{job}}” back to available jobs',
    icon: 'close-outline',
  },
  {
    id: 2,
    name: 'rejected “{{job}}”',
    icon: 'close-outline',
  },
  {
    id: 3,
    name: 'finished “{{job}}”',
    icon: 'checkmark-square-2-outline',
  },
  {
    id: 4,
    name: 'unfinished “{{job}}”',
    icon: 'close-square-outline',
  },
  {
    id: 5,
    name: 'edited previously finished “{{job}}”',
    icon: 'edit-2-outline',
  },
  {
    id: 6,
    name: 'created “{{job}}”',
    icon: 'plus-square-outline',
  },
  {
    id: 7,
    name: "forgot to complete “{{job}}” so now it's available again",
    icon: 'clock-outline',
  },
  {
    id: 8,
    name: 'updated the “{{job}}”',
    icon: 'activity-outline',
  },
  {
    id: 9,
    name: 'deleted the “{{job}}”',
    icon: 'trash-outline',
  },
  {
    id: 10,
    name: 'rescheduled “{{job}}”',
    icon: 'edit-2-outline',
  },
];

export const accountRoles: Role[] = [
  {
    id: 0,
    name: 'Join an Existing Team',
  },
  {
    id: 1,
    name: 'Create My Own Team',
  },
];

export enum UserRoles {
  Admin = 0,
  Manager = 1,
  Worker = 2,
}

export default null;
