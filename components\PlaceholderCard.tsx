import React, { ReactElement } from 'react';
import {
  Button,
  Card,
  StyleService,
  Text,
  useStyleSheet,
} from '~node_modules/@ui-kitten/components';
import Icon from '~components/Icon';
import { View } from '~components/Themed';

type Props = {
  text: string;
  icon: string;
  onPress?: () => void;
  cta?: string;
};

const PlaceholderCard = ({ text, cta, onPress, icon }: Props): ReactElement => {
  const styles = useStyleSheet(themedStyles) as any;

  return (
    <Card style={styles.placeholderCard} onPress={onPress}>
      <View style={styles.placeholderContent}>
        <Icon name={icon} {...styles.propertyIcon} />
        <Text style={styles.text}>{text}</Text>
        {cta && (
          <Button appearance="filled" style={styles.button} onPress={onPress}>
            {cta}
          </Button>
        )}
      </View>
    </Card>
  );
};

const themedStyles = StyleService.create({
  placeholderCard: {
    flex: 1,
    margin: 16,
    alignItems: 'stretch',
    justifyContent: 'center',
  },
  placeholderContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  propertyIcon: {
    alignSelf: 'center',
    width: 48,
    height: 48,
    backgroundColor: 'white',
    fill: 'gray',
  },
  text: {
    textAlign: 'center',
    marginTop: 32,
    marginBottom: 48,
    fontSize: 16,
    color: 'gray',
  },
  button: {
    color: 'gray',
  },
});

export default PlaceholderCard;
