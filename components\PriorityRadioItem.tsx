import * as React from 'react';
import { View } from 'react-native';
import { StyleService, Text } from '~node_modules/@ui-kitten/components';
import Hint from './Hint';

type Props = {
  label: string;
  info?: string;
  color?: string;
};

const PriorityRadioItem = ({
  label,
  info,
  color,
}: Props): React.ReactElement => (
  <>
    <Text style={{ ...styles.customSelectContent, color }}>{label}</Text>
    {info && (
      <View style={styles.hintContainer}>
        <Hint text={info} size="medium" />
      </View>
    )}
  </>
);

const styles = StyleService.create({
  customSelectContent: {
    marginLeft: 12,
    fontSize: 14,
  },
  hintContainer: {
    margin: -10,
  },
});

export default PriorityRadioItem;
