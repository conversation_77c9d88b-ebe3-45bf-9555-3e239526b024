import React from 'react';
import { Formik } from 'formik';
import {
  Input,
  Button,
  Divider,
  Layout,
  useStyleSheet,
  StyleService,
} from '@ui-kitten/components';
import { useQuery } from 'react-query';
import { MediaTypeOptions } from 'expo-image-picker';
import * as Yup from 'yup';
import _ from 'lodash';
import { StackScreenProps } from '~node_modules/@react-navigation/stack';
import MediaPicker from '~components/MediaPicker';
import KeyboardAvoidingView from '~components/KeyboardAvoidingView';
import { Skill } from '~types';
import useTasks from '~api/useTasks';
import useProperties from '~queries/useProperties';
import PlaceholderCard from '~components/PlaceholderCard';

const schema = Yup.object().shape({
  media: Yup.lazy(value => {
    switch (typeof value) {
      case 'undefined':
        return Yup.object().required('Required');
      case 'string':
        return Yup.string();
      default:
        return Yup.mixed().required();
    }
  }),
  description: Yup.string().required('Required'),
});

type Props = StackScreenProps;

const AddTask = ({ navigation, route }: Props): React.ReactElement => {
  const styles = useStyleSheet(themedStyles);

  const { getTask } = useTasks();

  const { isLoading: propertiesAreLoading, data: properties } = useProperties({
    showDeleted: true,
  });

  const taskId = route?.params?.taskId;
  const { data: task } = useQuery(['task', taskId], () => getTask(taskId), {
    enabled: Boolean(taskId),
  });

  const handleAddTask = (values: {
    id: string;
    media: string;
    description: string;
    urgency: number;
    property: number;
    preferredDate: string;
    preferredStartTime: string;
    preferredEndTime: string;
    requiredSkillIds: Array<Skill>;
    recurring: number;
  }) => {
    // If the media is not changed we should remove the url to prevent file-upload issue

    navigation.push('TaskSettingsScreen', {
      values: _.omit(
        values,
        values.media === initialData.media ? ['media', 'thumbnail'] : [],
      ),
    });
  };

  let initialData = {
    media: '',
    thumbnail: '',
    description: '',
    preferredDate: '',
    preferredStartTime: '',
    preferredEndTime: '',
    recurring: '',
    propertyId: '',
    priority: '',
    requiredSkillIds: [],
  };

  if (route.params && task) {
    initialData = {
      id: task.id,
      media: task.media,
      thumbnail: task.thumbnail,
      description: task.description,
      preferredDate: task.preferredDate,
      preferredStartTime: new Date(task.preferredStartTime),
      preferredEndTime: new Date(task.preferredEndTime),
      recurring: task.recurring,
      propertyId: task.propertyId,
      priority: task.priority,
      requiredSkillIds: task.requiredSkillIds,
    };
  }

  if (propertiesAreLoading) {
    return null;
  }

  if (!properties.length) {
    return (
      <PlaceholderCard
        text="You haven't added any Properties to your account yet."
        cta="Add Your First Property"
        onPress={() =>
          navigation.navigate('Admin', { screen: 'PropertyFormScreen' })
        }
        icon="home-outline"
      />
    );
  }

  return (
    <Formik
      initialValues={initialData}
      onSubmit={handleAddTask}
      validationSchema={schema}
      validateOnMount
      enableReinitialize
    >
      {({
        handleChange,
        handleBlur,
        handleSubmit,
        values,
        setFieldValue,
        isValid,
      }) => (
        <KeyboardAvoidingView style={styles.container}>
          <Layout style={styles.form} level="1">
            <MediaPicker
              onChange={({ media, thumbnail }) => {
                setFieldValue('media', media);
                setFieldValue('thumbnail', thumbnail);
              }}
              mediaTypes={MediaTypeOptions.All}
              defaultValue={values.thumbnail}
            />
            <Input
              style={styles.input}
              textStyle={styles.multilineInput}
              label="Description"
              placeholder="Describe the issue (e.g. drain is clogged)"
              value={values.description}
              onChangeText={handleChange('description')}
              onBlur={handleBlur('description')}
              multiline
              numberOfLines={6}
              textAlignVertical="top"
            />
          </Layout>
          <Divider />
          <Button
            style={styles.addButton}
            size="giant"
            onPress={handleSubmit}
            disabled={!isValid}
          >
            Next
          </Button>
        </KeyboardAvoidingView>
      )}
    </Formik>
  );
};

const themedStyles = StyleService.create({
  container: {
    flex: 1,
    backgroundColor: 'background-basic-color-2',
  },
  form: {
    flex: 1,
    paddingHorizontal: 4,
    paddingVertical: 24,
  },
  input: {
    marginHorizontal: 12,
    marginVertical: 8,
  },
  multilineInput: {
    minHeight: 120,
    marginHorizontal: 12,
    marginVertical: 8,
  },
  middleInput: {
    width: 128,
  },
  addButton: {
    marginHorizontal: 16,
    marginVertical: 24,
  },
});

export default AddTask;
