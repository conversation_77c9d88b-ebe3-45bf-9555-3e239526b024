import React, {
  createContext,
  Dispatch,
  ReactElement,
  ReactNode,
  SetStateAction,
  useState,
} from 'react';
import { BackgroundUpload } from '~types';

export enum BackgroundUploadStatuses {
  Started,
  Finished,
  Failed,
}

export type UploadWithStatus = {
  status: BackgroundUploadStatuses;
  backgroundUpload: BackgroundUpload;
};

const initialState: {
  uploads: UploadWithStatus[];
  setUploads: Dispatch<SetStateAction<UploadWithStatus[]>>;
} = {
  uploads: [],
  setUploads: () => {},
};

const BackgroundUploadContext = createContext(initialState);

export const BackgroundUploadContextProvider = ({
  children,
}: {
  children: ReactNode;
}): ReactElement => {
  const [uploads, setUploads] = useState<UploadWithStatus[]>([]);

  return (
    <BackgroundUploadContext.Provider
      value={{
        uploads,
        setUploads,
      }}
    >
      {children}
    </BackgroundUploadContext.Provider>
  );
};

export default BackgroundUploadContext;
