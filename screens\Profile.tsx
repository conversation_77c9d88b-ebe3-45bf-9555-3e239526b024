import React, { useState } from 'react';
import { View } from 'react-native';
import {
  Menu,
  MenuItem,
  StyleService,
  useStyleSheet,
  Button,
} from '@ui-kitten/components';
import { StackNavigationProp } from '@react-navigation/stack';
import {
  KeyIcon,
  LogOutIcon,
  PersonIcon,
  SkillsetIcon,
} from '~components/Icon';
import useUser from '~api/useUser';
import DeleteAccountModal from '~components/DeleteAccountModal';
import useRegisterForPushNotifications, {
  Modes,
} from '~hooks/useRegisterForPushNotifications';
import useRemoveAuth from '~hooks/useRemoveAuth';
import { ProfileParamList } from '~types';
import useSignOut from '~hooks/useSignOut';

type Props = {
  navigation: StackNavigationProp<ProfileParamList>;
};

const Profile = ({ navigation }: Props): React.ReactElement => {
  const styles = useStyleSheet(themedStyle);
  const [showDelete, setShowDelete] = useState(false);
  const { deleteAccount } = useUser();
  const { registerForPush } = useRegisterForPushNotifications();
  const { removeAuth } = useRemoveAuth();
  const { handleSignOut } = useSignOut();

  const handleDeleteAccount = async () => {
    await registerForPush(Modes.Unsubscribe);
    await deleteAccount();
    removeAuth();
  };

  const menuItems = [
    <MenuItem
      key="personal-information"
      style={styles.menuItem}
      title="Personal Information"
      accessoryLeft={PersonIcon}
      onPress={() => navigation.navigate('PersonalInformationScreen')}
    />,
    <MenuItem
      key="skills"
      style={styles.menuItem}
      title="Skills"
      accessoryLeft={SkillsetIcon}
      onPress={() => navigation.navigate('SkillsScreen')}
    />,
    <MenuItem
      key="change-password"
      style={styles.menuItem}
      title="Change Password"
      accessoryLeft={KeyIcon}
      onPress={() => navigation.navigate('PasswordScreen')}
    />,
    <MenuItem
      key="sign-out"
      style={styles.menuItem}
      title="Sign Out"
      accessoryLeft={LogOutIcon}
      onPress={handleSignOut}
    />,
    <MenuItem
      key="delete-me"
      style={[styles.menuItem, styles.deleteButton]}
      title={
        <Button
          size="tiny"
          appearance="ghost"
          onPress={() => setShowDelete(true)}
        >
          Delete Account
        </Button>
      }
    />,
  ];

  return (
    <View>
      <Menu contentContainerStyle={styles.menu}>{menuItems}</Menu>
      <DeleteAccountModal
        show={showDelete}
        onCancel={() => setShowDelete(false)}
        onConfirm={handleDeleteAccount}
      />
    </View>
  );
};

const themedStyle = StyleService.create({
  menu: {
    height: '100%',
    flexGrow: 1,
  },
  menuItem: {
    flexGrow: 1,
    flexBasis: '18%',
  },
  deleteButton: {
    flexBasis: '10%',
    justifyContent: 'center',
  },
});

export default Profile;
