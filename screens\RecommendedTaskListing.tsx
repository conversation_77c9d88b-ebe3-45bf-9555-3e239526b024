import React, {
  Dispatch,
  ReactElement,
  SetStateAction,
  useLayoutEffect,
  useState,
} from 'react';
import { StackScreenProps } from '@react-navigation/stack';
import { useQuery } from 'react-query';
import { View } from 'react-native';
import { StyleService, useStyleSheet } from '@ui-kitten/components';
import useUser from '~api/useUser';
import TasksListContent from '~components/TaskListContent';
import CompactModeSwitch from '~components/CompactModeSwitch';
import { AvailableTasksParamList } from '~types';
import useFilteredAvailableTasks from '~queries/useFilteredAvailableTasks';

type Props = StackScreenProps<
  AvailableTasksParamList,
  'RecommendedTaskListingScreen'
>;

const RecommendedTaskListing = ({ route, navigation }: Props): ReactElement => {
  const styles = useStyleSheet(themedStyles);

  const {
    headerTitle,
    compactModeEnabled: compactModeEnabledInitially,
    propertyId,
    date,
    urgent,
    overdue,
    textSearch,
  } = route.params;

  const { getUser } = useUser();
  const { data: user } = useQuery('user', () => getUser());

  const currentUserRole = user?.accounts[0].pivot.role;

  const [compactModeEnabled, setCompactModeEnabled] = useState(
    compactModeEnabledInitially,
  );

  const { data: tasks, isLoading } = useFilteredAvailableTasks({
    propertyId,
    textSearch: textSearch || undefined,
    date,
    urgent,
    overdue,
  });

  useLayoutEffect(() => {
    navigation.setOptions({
      headerTitle,
      headerRight: headerRight(
        compactModeEnabled,
        setCompactModeEnabled,
        styles,
      ),
    });
  }, [compactModeEnabled, headerTitle, navigation, styles, styles.container]);

  return (
    <TasksListContent
      navigation={navigation}
      tasks={tasks || []}
      currentUserRole={currentUserRole}
      isLoading={isLoading}
      compactModeEnabled={compactModeEnabled}
    />
  );
};

const themedStyles = StyleService.create({
  container: {
    marginRight: 6,
  },
});

const headerRight =
  (
    compactModeEnabled: boolean,
    setCompactModeEnabled: Dispatch<SetStateAction<boolean>>,
    styles,
  ) =>
  () =>
    (
      <View style={styles.container}>
        <CompactModeSwitch
          compactModeEnabled={compactModeEnabled}
          onEnableCompactMode={setCompactModeEnabled}
        />
      </View>
    );

export default RecommendedTaskListing;
