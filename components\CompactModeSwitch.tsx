import React, { Dispatch, ReactElement, SetStateAction } from 'react';
import { StyleService, useStyleSheet, Icon } from '@ui-kitten/components';
import { TouchableOpacity } from 'react-native';

type Props = {
  compactModeEnabled: boolean;
  onEnableCompactMode: Dispatch<SetStateAction<boolean>>;
};

const CompactModeSwitch = ({
  compactModeEnabled,
  onEnableCompactMode,
}: Props): ReactElement => {
  const styles = useStyleSheet(themedStyles);

  return (
    <TouchableOpacity
      onPress={() => onEnableCompactMode(oldValue => !oldValue)}
    >
      <Icon
        style={styles.icon}
        fill="#222b45"
        name={compactModeEnabled ? 'grid-outline' : 'list-outline'}
      />
    </TouchableOpacity>
  );
};

const themedStyles = StyleService.create({
  icon: {
    alignSelf: 'center',
    width: 24,
    height: 24,
  },
});

export default CompactModeSwitch;
