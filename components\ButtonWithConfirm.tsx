import React, { ReactElement, useState } from 'react';
import { ImageStyle, TextStyle, ViewStyle } from 'react-native';
import { Button } from '@ui-kitten/components';
import Confirm from '~components/Confirm';

type Props = {
  buttonText: string;
  confirmText: string;
  onConfirm: () => Promise<unknown>;
  style?: ViewStyle | TextStyle | ImageStyle;
  confirmTitle?: string;
  confirmButtonText?: string;
  cancelButtonText?: string;
  disabled?: boolean;
  isLoading?: boolean;
};

const ButtonWithConfirm = ({
  buttonText,
  confirmText,
  onConfirm,
  style,
  confirmTitle,
  confirmButtonText = 'Yes',
  cancelButtonText = 'No',
  disabled,
  isLoading = false,
}: Props): ReactElement => {
  const [showConfirm, setShowConfirm] = useState(false);

  const handleConfirm = async () =>
    onConfirm().finally(() => {
      setShowConfirm(false);
    });

  return (
    <>
      <Button
        disabled={disabled}
        style={style}
        onPress={() => setShowConfirm(true)}
      >
        {buttonText}
      </Button>
      <Confirm
        show={showConfirm}
        onCancel={() => setShowConfirm(false)}
        onConfirm={handleConfirm}
        text={confirmText}
        confirmButtonText={confirmButtonText}
        cancelButtonText={cancelButtonText}
        confirmTitle={confirmTitle}
        isLoading={isLoading}
      />
    </>
  );
};

export default ButtonWithConfirm;
