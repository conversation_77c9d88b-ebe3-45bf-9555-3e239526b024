{"extends": ["airbnb", "airbnb/hooks", "plugin:@typescript-eslint/recommended", "prettier"], "plugins": ["@typescript-eslint", "react", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 2018, "sourceType": "module", "project": "./tsconfig.json"}, "rules": {"import/no-unresolved": 0, "react/jsx-filename-extension": [1, {"extensions": [".ts", ".tsx"]}], "prettier/prettier": ["error", {"singleQuote": true, "trailingComma": "all", "arrowParens": "avoid", "endOfLine": "auto"}], "no-use-before-define": "off", "@typescript-eslint/no-use-before-define": 0, "import/extensions": ["error", "never", {"json": "always", "png": "always", "mp3": "always"}], "react/prop-types": 0, "react/jsx-props-no-spreading": 0, "no-shadow": "off", "@typescript-eslint/no-shadow": ["error"], "import/no-named-default": 0, "react/require-default-props": 0, "react/function-component-definition": 0, "@typescript-eslint/no-empty-function": 0}}