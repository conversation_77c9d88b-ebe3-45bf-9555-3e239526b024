import React from 'react';
import { Button, StyleService, useStyleSheet } from '@ui-kitten/components';

const PreviewButton = ({ onPress }: { onPress: () => void }) => {
  const styles = useStyleSheet(themedStyle);

  return (
    <Button
      status="info"
      appearance="ghost"
      style={styles.previewButton}
      onPress={onPress}
    >
      Preview
    </Button>
  );
};

export default PreviewButton;

const themedStyle = StyleService.create({
  previewButton: {
    paddingVertical: 0,
    paddingHorizontal: 0,
    marginHorizontal: -10,
    minWidth: 0,
    minHeight: 30,
  },
});
