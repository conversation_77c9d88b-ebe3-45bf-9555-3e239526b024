import { Button, StyleService } from '@ui-kitten/components';
import { ListRenderItemInfo, ScrollView, Text, View } from 'react-native';
import React from 'react';
import { StackNavigationProp } from '@react-navigation/stack';
import Table, {
  PropertyInvoiceHeader,
  PaymentsHeader,
  TableFooter,
  TechnicianInvoiceHeader,
  TechnicianTableFooter,
} from './Table';
import {
  GroupedTaskAssignment,
  MoreParamList,
  Payment,
  TaskAssignment,
} from '~types';
import { Link } from '~node_modules/@react-navigation/native';
import { Option } from '../CustomSelect';

type Props = {
  taskAssignments: TaskAssignment[];
  payments: Array<Payment>;
  headerTitle: string;
  headerTitles: { technicians: string; properties: string };
  renderItem: (
    info: ListRenderItemInfo<TaskAssignment | GroupedTaskAssignment>,
  ) => React.ReactElement;
  monthlyTotals: {
    costOfLabor: number;
    costOfMaterials: number;
    total: number;
  };
  paymentRenderItem: (info: ListRenderItemInfo<Payment>) => React.ReactElement;
  monthlyPaymentTotal: number;
  selectedTechnician: Option<number>;
  navigation: StackNavigationProp<MoreParamList, 'InvoicesScreen'>;
  selectedMonth: Date;
  isProfitMarginEnabled?: boolean;
  profitMargin?: number;
};

const InvoiceScrollView = ({
  taskAssignments,
  payments,
  headerTitle,
  headerTitles,
  renderItem,
  monthlyTotals,
  paymentRenderItem,
  monthlyPaymentTotal,
  selectedTechnician,
  navigation,
  selectedMonth,
  isProfitMarginEnabled,
  profitMargin,
}: Props): React.ReactElement => {
  if (!(taskAssignments?.length || payments?.length)) {
    return <Text style={styles.noTasks}>No Activity this month.</Text>;
  }

  const balance = Number(
    (monthlyTotals.total + monthlyPaymentTotal).toFixed(2),
  );

  const invoiceType: 'technician' | 'property' =
    headerTitle === headerTitles.properties ? 'property' : 'technician';

  return (
    <ScrollView style={styles.list}>
      {invoiceType === 'technician' && (
        <View style={styles.title}>
          <Text style={styles.titleText}>Jobs</Text>
        </View>
      )}
      <Table
        data={taskAssignments}
        renderItem={renderItem}
        header={
          invoiceType === 'property' ? (
            <PropertyInvoiceHeader />
          ) : (
            <TechnicianInvoiceHeader />
          )
        }
        footer={
          invoiceType === 'property' ? (
            <TableFooter sum={monthlyTotals.total} sumTitle="Monthly Total" />
          ) : (
            <TechnicianTableFooter
              costOfMaterials={monthlyTotals.costOfMaterials}
              costOfLabor={monthlyTotals.costOfLabor}
              totalCost={monthlyTotals.total}
              sumTitle="Monthly Total"
            />
          )
        }
      />
      {(invoiceType === 'technician' || !headerTitle) && (
        <>
          <View style={styles.title}>
            <Text style={styles.titleText}>Payments</Text>
          </View>
          <Table
            data={payments}
            renderItem={paymentRenderItem}
            header={<PaymentsHeader />}
            footer={
              <>
                <TableFooter
                  sum={monthlyPaymentTotal}
                  sumTitle="Payment Total"
                />
                <TableFooter sum={balance} sumTitle="Balance:" />
              </>
            }
          />
        </>
      )}

      {invoiceType === 'technician' && (
        <Button
          appearance="outline"
          size="giant"
          onPress={() =>
            navigation.push('LogPaymentScreen', {
              userId: selectedTechnician.value,
              selectedMonth: selectedMonth.toISOString(),
            })
          }
        >
          Add Payment
        </Button>
      )}
      {invoiceType === 'property' && (
        <View style={styles.footer}>
          <Text>
            {isProfitMarginEnabled ? (
              <>
                *All items on this invoice include a {profitMargin}% Mark Up.
                You can change this in{' '}
              </>
            ) : (
              <>Set a mark up to property invoices in </>
            )}
            <Link to="/Root/Admin/ProfitMarginScreen" style={styles.link}>
              Mark Up settings
            </Link>
            .
          </Text>
        </View>
      )}
    </ScrollView>
  );
};

export default InvoiceScrollView;

const styles = StyleService.create({
  noTasks: { padding: 20, fontSize: 16 },
  list: { padding: 16, flex: 1 },
  title: {
    justifyContent: 'flex-start',
    marginVertical: 10,
  },
  titleText: { fontSize: 20, fontWeight: 'bold' },
  footer: { marginTop: 16 },
  link: { textDecorationLine: 'underline' },
});
