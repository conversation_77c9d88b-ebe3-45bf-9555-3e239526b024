import { StyleService, useStyleSheet } from '@ui-kitten/components';
import React, { ReactElement, ReactNode } from 'react';
import { Text } from 'react-native';

type Props = {
  children: ReactNode;
  errorMessage?: string;
};

const InputValidationWrapper = ({
  children,
  errorMessage,
}: Props): ReactElement => {
  const styles = useStyleSheet(themedStyles);

  return (
    <>
      {children}
      <Text style={styles.errorText}>{errorMessage}</Text>
    </>
  );
};

export default InputValidationWrapper;

const themedStyles = StyleService.create({
  errorText: {
    color: 'red',
  },
});
