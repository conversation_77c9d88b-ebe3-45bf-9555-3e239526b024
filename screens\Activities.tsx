import React, { ReactElement } from 'react';
import { useInfiniteQuery } from 'react-query';
import { FlatList, StyleProp, StyleSheet, ViewStyle } from 'react-native';
import _ from 'lodash';
import { StackNavigationProp } from '@react-navigation/stack';
import useTasks from '~api/useTasks';
import Spinner from '~components/Spinner';
import { taskLogActions } from '~dummyData';
import useTimezone from '~hooks/useTimezone';
import { MoreParamList, TaskLog } from '~types';
import ActivityListItem from '~components/ActivityListItem';
import { View } from '~components/Themed';

type Props = {
  navigation: StackNavigationProp<MoreParamList>;
};

const TaskHistory = ({ navigation }: Props): ReactElement => {
  const { getActivityLogs } = useTasks();
  const {
    data: taskLogs,
    isLoading,
    hasNextPage,
    fetchNextPage,
  } = useInfiniteQuery(
    'task-activities',
    url => getActivityLogs(url.pageParam),
    {
      getNextPageParam: lastPage => {
        if (lastPage.nextPageUrl !== null) {
          return lastPage.currentPage + 1;
        }

        return lastPage;
      },
    },
  );

  const loadMore = () => {
    if (hasNextPage) {
      fetchNextPage();
    }
  };

  const { formatInTimeZone } = useTimezone();

  if (isLoading || !taskLogs) {
    return <Spinner />;
  }

  const handleNavigation = (taskLog: TaskLog): void => {
    const {
      taskAssignmentId,
      task: { id: taskId },
    } = taskLog;

    if (taskAssignmentId) {
      navigation.push('TaskAssignmentScreen', {
        id: taskAssignmentId,
      });

      return;
    }

    navigation.push('TaskScreen', {
      id: taskId,
      backnavScreenName: 'ActivitiesScreen',
    });
  };

  const renderItem = ({ item }: { item: TaskLog }) => {
    const action = taskLogActions.find(
      taskLogAction => taskLogAction.id === item.actionType,
    );

    if (!action) {
      return null;
    }

    const name = item.user ? item.user.name : 'System';
    const text = `${name} ${action.name.replace(
      '{{job}}',
      _.truncate(item.task.description),
    )}`;

    return (
      <ActivityListItem
        key={item.id}
        text={text}
        iconName={action?.icon}
        date={formatInTimeZone(item.createdAt, 'MMM d, y h:mm aaa')}
        onPress={() => handleNavigation(item)}
        additionalInfo={item.additionalInfo}
      />
    );
  };

  const flatListData: TaskLog[] = taskLogs.pages.map(page => page.data).flat();

  return (
    <View style={styles.container}>
      <FlatList
        style={styles.list}
        ItemSeparatorComponent={separator(styles.separator)}
        data={flatListData}
        keyExtractor={item => item.id.toString()}
        renderItem={renderItem}
        onEndReached={loadMore}
        onEndReachedThreshold={0.3}
      />
    </View>
  );
};

const separator = (style: StyleProp<ViewStyle>) => () => <View style={style} />;

const styles = StyleSheet.create({
  container: {
    height: '100%',
    flexDirection: 'row',
    marginHorizontal: 16,
    backgroundColor: 'none',
  },
  list: {
    paddingVertical: 16,
  },
  separator: {
    height: 16,
    backgroundColor: 'none',
  },
});

export default TaskHistory;
