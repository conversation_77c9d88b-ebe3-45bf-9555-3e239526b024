import React, { useLayoutEffect } from 'react';
import { Formik } from 'formik';
import {
  Input,
  Divider,
  Layout,
  useStyleSheet,
  StyleService,
} from '@ui-kitten/components';
import { MediaTypeOptions } from 'expo-image-picker';
import * as Yup from 'yup';
import {
  QueryClient,
  useMutation,
  useQuery,
  useQueryClient,
} from 'react-query';
import { Platform, View } from 'react-native';
import _ from 'lodash';
import { StackScreenProps } from '~node_modules/@react-navigation/stack';
import MediaPicker from '~components/MediaPicker';
import KeyboardAvoidingView from '~components/KeyboardAvoidingView';
import DateTimePicker from '~components/DateTimePicker';
import MonthSelector from '~components/MonthSelector';
import usePayments from '~api/usePayments';
import useUser from '~api/useUser';
import CustomSelect from '~components/CustomSelect';
import SpinnerButton from '~components/SpinnerButton';
import Spinner from '~components/Spinner';
import { MoreParamList } from '~types';

const schema = Yup.object().shape({
  date: Yup.string().required('Required'),
  amount: Yup.number().required('Required'),
  media: Yup.lazy(value => {
    switch (typeof value) {
      case 'undefined':
        return Yup.object().required('Required');
      case 'string':
        return Yup.string();
      default:
        return Yup.mixed().required();
    }
  }),
  description: Yup.string().required('Required'),
  subjectMonth: Yup.string().required('Required'),
  userId: Yup.object().shape({
    value: Yup.string().required('Required'),
  }),
});

const invalidateQueries = (queryClient: QueryClient, paymentId: number) => {
  queryClient.invalidateQueries('payments');
  queryClient.invalidateQueries('unpaid-invoices');
  queryClient.invalidateQueries(['payment', paymentId]);
};

type Props = StackScreenProps<MoreParamList, 'LogPaymentScreen'>;

const LogPayment = ({ navigation, route }: Props): React.ReactElement => {
  const styles = useStyleSheet(themedStyles);

  const {
    params: { paymentId, selectedMonth, userId },
  } = route;

  const { createPayment, getPayment, updatePayment } = usePayments();
  const { getUser, getAccountMembers } = useUser();
  const { data: user } = useQuery('user', () => getUser());

  const accountId = user?.accounts?.[0].id;
  const { data: accountMembers, isLoading: getAccountMembersIsLoading } =
    useQuery(
      ['accountMembers', accountId],
      () => getAccountMembers(accountId!),
      { enabled: !!accountId },
    );

  useLayoutEffect(() => {
    if (paymentId) {
      navigation.setOptions({ headerTitle: 'Edit Payment' });
    }
  }, [navigation, paymentId]);

  const { mutateAsync: createPaymentMutate, isLoading: createIsLoading } =
    useMutation(createPayment, {
      onSuccess: async () => {
        await queryClient.invalidateQueries('unpaid-invoices');
        await queryClient.invalidateQueries('payments');
      },
    });

  const queryClient = useQueryClient();

  const { mutateAsync: updatePaymentMutate, isLoading: updateIsLoading } =
    useMutation(updatePayment, {
      onSuccess: data => {
        invalidateQueries(queryClient, data.id);
        if (Platform.OS !== 'web') {
          data.promises.then(() => {
            invalidateQueries(queryClient, data.id);
          });
        }
      },
    });

  const { data: payment } = useQuery(
    ['payment', paymentId],
    () => getPayment(paymentId),
    {
      enabled: Boolean(paymentId),
    },
  );

  const availableTechnicianSelectItems = [
    { label: 'Select Technician', value: null },
    ...(accountMembers?.map(item => ({ label: item.name, value: item.id })) ||
      []),
  ];

  const handleLogPayment = async (values: {
    id: number;
    date: Date;
    amount: string;
    media: string;
    description: string;
    subjectMonth: Date;
    userId: { label: string; value: number };
  }) => {
    let transformedValues = {
      ...values,
      date: values.date.toISOString(),
      subjectMonth: values.subjectMonth.toISOString(),
      userId: values.userId.value,
    };

    if (paymentId) {
      if (transformedValues.media === initialData.media) {
        transformedValues = _.omit(transformedValues, [
          'media',
        ]) as typeof transformedValues & { media?: string };
      }

      await updatePaymentMutate(transformedValues);
    } else {
      await createPaymentMutate(transformedValues);
    }

    await queryClient.invalidateQueries(['technicians', values.subjectMonth]);
    navigation.pop();
  };

  const selectedUser = availableTechnicianSelectItems.find(
    item => item.value === userId,
  );
  let initialData = {
    id: 0,
    date: new Date(),
    amount: '',
    media: '',
    description: '',
    subjectMonth: new Date(selectedMonth),
    userId:
      userId && selectedUser ? selectedUser : availableTechnicianSelectItems[0],
  };

  if (payment) {
    initialData = {
      id: payment.id,
      date: new Date(payment.date),
      amount: payment.amount,
      media: payment.media,
      description: payment.description,
      subjectMonth: new Date(payment.subjectMonth),
      userId: availableTechnicianSelectItems.find(
        item => item.value === payment.userId,
      )!,
    };
  }

  if (getAccountMembersIsLoading) {
    return <Spinner />;
  }

  return (
    <Formik
      initialValues={initialData}
      onSubmit={handleLogPayment}
      validationSchema={schema}
      validateOnMount
    >
      {({ handleChange, handleSubmit, values, setFieldValue, isValid }) => (
        <KeyboardAvoidingView style={styles.container}>
          <Layout style={styles.form} level="1">
            <CustomSelect
              options={availableTechnicianSelectItems}
              selectedItem={values.userId}
              setSelectedItem={id => setFieldValue('userId', id)}
              label="Technician"
              styles={styles.input}
            />
            <DateTimePicker
              style={styles.input}
              type="date"
              label="Date"
              value={values.date.toISOString()}
              onChange={date => setFieldValue('date', new Date(date))}
            />
            <Input
              style={styles.input}
              label="Amount (USD)"
              placeholder="e.g. 120.34"
              value={values.amount}
              onChangeText={handleChange('amount')}
              keyboardType="decimal-pad"
            />
            <View style={styles.mediaPicker}>
              <MediaPicker
                onChange={({ media }) => {
                  setFieldValue('media', media);
                }}
                mediaTypes={MediaTypeOptions.Images}
                defaultValue={values.media}
                previewResizeMode="contain"
                height={477}
              />
            </View>
            <Input
              style={styles.input}
              textStyle={styles.multilineInput}
              label="Description"
              placeholder=""
              value={values.description}
              onChangeText={handleChange('description')}
              multiline
              numberOfLines={6}
              textAlignVertical="top"
            />
            <MonthSelector
              selectedMonth={values.subjectMonth}
              lastMonth={values.subjectMonth}
              setSelectedMonth={month => setFieldValue('subjectMonth', month)}
              label="Subject month"
              styles={styles.input}
            />
          </Layout>
          <Divider />
          <SpinnerButton
            text="Save"
            style={styles.addButton}
            onPress={handleSubmit}
            isLoading={updateIsLoading || createIsLoading}
            disabled={!isValid}
          />
        </KeyboardAvoidingView>
      )}
    </Formik>
  );
};

const themedStyles = StyleService.create({
  container: {
    flex: 1,
    backgroundColor: 'background-basic-color-2',
  },
  form: {
    flex: 1,
    paddingHorizontal: 4,
    paddingVertical: 24,
  },
  input: {
    marginHorizontal: 12,
    marginVertical: 8,
  },
  mediaPicker: {
    marginVertical: 8,
  },
  multilineInput: {
    minHeight: 120,
    marginHorizontal: 12,
    marginVertical: 8,
  },
  addButton: {
    marginHorizontal: 16,
    marginVertical: 24,
  },
});

export default LogPayment;
