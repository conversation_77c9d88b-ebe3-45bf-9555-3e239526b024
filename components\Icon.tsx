import React from 'react';
import {
  Icon,
  IconElement,
  IconProps,
} from '~node_modules/@ui-kitten/components';

export const SkillsetIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="color-palette-outline" />
);

export const ClockIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="clock" />
);

export const ClockOutlineIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="clock-outline" />
);

export const HomeIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="home-outline" />
);

export const RecurringIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="repeat-outline" />
);

export const PinIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="pin-outline" />
);

export const PersonIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="person-outline" />
);
export const EmailIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="email" />
);

export const PlusIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="plus" />
);

export const PeopleIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="people" />
);

export const PeopleOutlineIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="people-outline" />
);

export const PersonAddOutlineIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="person-add-outline" />
);

export const PersonDoneOutlineIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="person-done-outline" />
);

export const KeyIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="keypad" />
);

export const LogOutIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="log-out" />
);

export const MoneyIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="credit-card" />
);

export const CloseIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="close" />
);

export const EditIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="edit" />
);

export const TrashIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="trash-outline" />
);

export const ListOutlineIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="list" />
);

export const LeftArrowIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="arrow-back-outline" />
);

export const RightArrowIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="arrow-forward-outline" />
);

export const DeleteAccountIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="person-delete-outline" />
);

export const InfoIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="info-outline" />
);

export const HelpIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="question-mark-circle-outline" />
);

export const CalendarIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="calendar-outline" />
);

export const ActivitiesIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="activity-outline" />
);

export const CameraIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="camera-outline" />
);

export const VideoIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="video-outline" />
);

export const PhotoIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="image-outline" />
);

export const FolderIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="folder-outline" />
);

export const CheckIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="checkmark-outline" />
);

export const ClipboardIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="clipboard-outline" />
);

export const ShareIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="share-outline" />
);

export const FileTextIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="file-text-outline" />
);

export const CarIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="car-outline" />
);

export const UnlockIcon = (props: IconProps): IconElement => (
  <Icon {...props} name="unlock-outline" />
);

export default (props: IconProps): IconElement => <Icon {...props} />;
