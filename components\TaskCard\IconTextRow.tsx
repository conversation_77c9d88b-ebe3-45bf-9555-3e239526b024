import React, { ReactNode } from 'react';
import { View, StyleSheet, ViewStyle, StyleProp } from 'react-native';
import { Text } from '@ui-kitten/components';
import ThemedIcon from '~components/ThemedIcon';
import { carrotColor } from '~constants/Colors';

type Props = {
  iconName: string;
  children: ReactNode;
  containerStyle?: StyleProp<ViewStyle>;
};

const IconTextRow = ({ iconName, children, containerStyle }: Props) => (
  <View style={[styles.container, containerStyle]}>
    <ThemedIcon name={iconName} size={18} />
    <Text style={styles.text}>{children}</Text>
  </View>
);

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    minHeight: 32,
  },
  text: {
    marginLeft: 6,
    fontSize: 12,
    fontWeight: 'bold',
    color: carrotColor,
  },
});

export default IconTextRow;
