import React, { ReactElement, useRef, useState } from 'react';
import { StyleService, useStyleSheet } from '@ui-kitten/components';
import { View, TouchableOpacity, Image } from 'react-native';
import { ResizeMode, Video } from 'expo-av';
import playButton from './play-button.png';

type Props = {
  media: string;
  resizeMode?: ResizeMode;
};

const VideoPlayer = ({
  media,
  resizeMode = ResizeMode.COVER,
}: Props): ReactElement => {
  const styles = useStyleSheet(themedStyles);
  const [showPlayButton, setShowPlayButton] = useState(true);
  const player = useRef<Video>(null);

  const handleClickPlayButton = () => {
    player?.current
      ?.playAsync()
      .then(() => setShowPlayButton(false))
      .catch(() => setShowPlayButton(true));
  };

  return (
    <View
      style={{
        width: '100%',
        height: '100%',
        position: 'relative',
      }}
    >
      <Video
        onPlaybackStatusUpdate={status => {
          const { didJustFinish } = status;

          if (didJustFinish) {
            setShowPlayButton(true);
          }
        }}
        style={{
          width: '100%',
          height: '100%',
          position: 'relative',
        }}
        ref={player}
        source={{ uri: media }}
        useNativeControls
        resizeMode={resizeMode}
        videoStyle={{
          ...(resizeMode === ResizeMode.COVER
            ? { width: '100%' }
            : { margin: 'auto' }),
        }}
      />
      {showPlayButton && (
        <TouchableOpacity
          style={[styles.flexCenter]}
          onPress={handleClickPlayButton}
        >
          <Image
            source={playButton} // eslint-disable-line global-require
            style={{ opacity: 0.5, height: 100, width: 100 }}
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

const themedStyles = StyleService.create({
  largeMediaView: {
    alignSelf: 'center',
    width: '100%',
    height: 200,
  },
  flexCenter: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
});

export default VideoPlayer;
