import { AxiosPromise } from 'axios';
import { Property } from '~types';
import useClient from './useClient';

export type GetPropertiesProps = {
  showDeleted?: boolean;
  showCalendarDates?: boolean;
};

export default () => {
  const client = useClient();
  const getProperties = ({
    showDeleted,
    showCalendarDates,
  }: GetPropertiesProps = {}): Promise<Property[]> => {
    const url = '/properties/my';

    return client
      .get(url, {
        params: {
          showDeleted,
          showCalendarDates,
        },
      })
      .then(res => res.data.data);
  };

  const getProperty = (id: number): Promise<Property> =>
    client.get(`/properties/${id}`).then(res => res.data.data);

  const deleteProperty = (id: number): AxiosPromise =>
    client.delete(`/properties/${id}`).then(res => res.data.data);

  const undeleteProperty = (id: number): AxiosPromise =>
    client.post(`/properties/${id}/undelete`).then(res => res.data.data);

  const addProperty = (property: Property): AxiosPromise =>
    client.post('/properties', property).then(res => res.data.data);

  const updateProperty = ({ id, ...updatedProperty }): AxiosPromise =>
    client.put(`/properties/${id}`, updatedProperty).then(res => res.data.data);

  return {
    getProperties,
    getProperty,
    deleteProperty,
    undeleteProperty,
    addProperty,
    updateProperty,
  };
};
