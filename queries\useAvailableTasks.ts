import useTasksAPI from '~api/useTasks';
import { useQuery } from '~node_modules/react-query';

type Props = {
  propertyId?: number;
  textSearch?: string;
  groupByDateAndProperty?: boolean;
};

export const useAvailableTasks = ({
  propertyId,
  textSearch,
  groupByDateAndProperty,
}: Props) => {
  const { getAvailableTasks } = useTasksAPI();

  const { data, isLoading } = useQuery(
    ['tasks', propertyId, textSearch, groupByDateAndProperty],
    () => getAvailableTasks({ propertyId, textSearch, groupByDateAndProperty }),
    { keepPreviousData: true },
  );

  return { data, isLoading };
};

export default useAvailableTasks;
