import ROLES from 'constants/Roles';
import React from 'react';
import { Text, LayoutProps, Button } from '@ui-kitten/components';
import { EditIcon } from './Icon';
import ListItem from '~components/ListItem';

export interface ProfileSettingProps extends LayoutProps {
  name: string;
  email: string;
  role: number;
  onEdit: () => void;
}

export const AccountMember = ({
  name,
  email,
  role,
  onEdit,
}: ProfileSettingProps): React.ReactElement => {
  const roleName = ROLES.find(({ id }) => id === role)?.name;

  return (
    <ListItem
      name={
        <>
          <Text appearance="hint" category="s1">
            {name}
          </Text>
          <Text>{email}</Text>
        </>
      }
      description={roleName}
      button={<Button size="small" accessoryLeft={EditIcon} onPress={onEdit} />}
    />
  );
};
