import React, { ReactElement, useState } from 'react';
import { ImageBackground, ViewStyle } from 'react-native';
import { StyleService, useStyleSheet } from '@ui-kitten/components';
import SpinnerWithText from './SpinnerWithText';
import ExpandImageButton from './ExpandImageButton';

type Props = {
  mediaUri: string;
  styles: ViewStyle;
  backgroundUploadTaskId: number;
  showExpandButton?: boolean;
};

const BackgroundWithSpinner = ({
  mediaUri,
  styles,
  backgroundUploadTaskId,
  showExpandButton = false,
}: Props): ReactElement => {
  const componentStyles = useStyleSheet(themedStyle);
  const [loading, setLoading] = useState(true);

  return (
    <ImageBackground
      style={[componentStyles.iconContainer, styles]}
      source={{ uri: mediaUri }}
      onLoadEnd={() => setLoading(false)}
    >
      {loading && (
        <SpinnerWithText backgroundUploadTaskId={backgroundUploadTaskId} />
      )}
      {!loading && showExpandButton && (
        <ExpandImageButton imageUri={mediaUri} />
      )}
    </ImageBackground>
  );
};

const themedStyle = StyleService.create({
  iconContainer: {
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
  },
});

export default BackgroundWithSpinner;
