import React from 'react';
import {
  Button,
  Input,
  StyleService,
  useStyleSheet,
  Card,
} from '@ui-kitten/components';
import { Formik } from 'formik';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import * as Yup from 'yup';
import { StackScreenProps } from '@react-navigation/stack';
import { useToast } from 'react-native-toast-notifications';
import useUser from '~api/useUser';
import SpinnerButton from '~components/SpinnerButton';
import Spinner from '~components/Spinner';
import useSkills from '~api/useSkills';
import { MoreParamList } from '~types';

const schema = Yup.object().shape({
  name: Yup.string().required('Required'),
});

type Props = StackScreenProps<MoreParamList>;

export default ({ navigation }: Props): React.ReactElement => {
  const toast = useToast();
  const styles = useStyleSheet(themedStyle);

  const { getUser } = useUser();
  const queryClient = useQueryClient();
  const { data: user } = useQuery('user', () => getUser());
  const { postSkill } = useSkills();

  const { mutate: postSkillMutate, isLoading } = useMutation(postSkill, {
    onSuccess: async () => {
      await queryClient.invalidateQueries('skills');
      navigation.goBack();
      toast.show('Skill has been added');
    },
  });

  if (!user) {
    return <Spinner />;
  }

  const initialData = {
    name: '',
  };

  const handleSubmit = (values: { name: string }) => postSkillMutate(values);

  return (
    <Formik
      initialValues={initialData}
      onSubmit={handleSubmit}
      enableReinitialize
      validationSchema={schema}
      validateOnMount
    >
      {({ values, setFieldValue, handleSubmit: handlePressSave, isValid }) => (
        <>
          <Card style={styles.card}>
            <Input
              placeholder="Skill name"
              value={values.name}
              onChangeText={value => setFieldValue('name', value)}
            />
          </Card>

          <SpinnerButton
            text="Save"
            style={styles.saveButton}
            onPress={() => handlePressSave()}
            isLoading={isLoading}
            disabled={!isValid}
          />
          <Button
            style={styles.saveButton}
            appearance="outline"
            size="giant"
            onPress={() => navigation.goBack()}
          >
            Cancel
          </Button>
        </>
      )}
    </Formik>
  );
};

const themedStyle = StyleService.create({
  saveButton: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  card: {
    margin: 16,
    paddingVertical: 6,
  },
});
