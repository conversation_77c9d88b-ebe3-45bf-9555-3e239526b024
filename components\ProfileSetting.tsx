import React, { ReactNode } from 'react';
import { StyleSheet } from 'react-native';
import { Divider, Layout, Text, LayoutProps } from '@ui-kitten/components';
import { View } from './Themed';

export interface ProfileSettingProps extends LayoutProps {
  hint: string;
  value: ReactNode;
}

export const ProfileSetting = (
  props: ProfileSettingProps,
): React.ReactElement => {
  const { style, hint, value, ...layoutProps } = props;

  return (
    <>
      <Layout level="1" {...layoutProps} style={[styles.container, style]}>
        <Text appearance="hint" category="s1">
          {hint}
        </Text>
        <View style={styles.inputContainer}>{value}</View>
      </Layout>
      <Divider />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  inputContainer: {
    flexGrow: 1,
    marginLeft: 16,
    alignItems: 'flex-start',
  },
});
