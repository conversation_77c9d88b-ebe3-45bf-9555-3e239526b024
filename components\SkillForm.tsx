import React, { ReactElement } from 'react';
import { Text, View } from 'react-native';
import {
  Button,
  Layout,
  StyleService,
  useStyleSheet,
} from '@ui-kitten/components';
import { Formik } from 'formik';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import { useNavigation } from '@react-navigation/native';
import KeyboardAvoidingView from '~components/KeyboardAvoidingView';
import useUser from '~api/useUser';
import CheckboxesInput from './CheckboxesInput';
import Spinner from './Spinner';
import useSkills from '~queries/useSkills';

type Props = {
  description?: string;
  showCancelButton?: boolean;
  navigateTo?;
};

const SkillForm = ({
  description,
  showCancelButton,
  navigateTo,
}: Props): ReactElement => {
  const styles = useStyleSheet(themedStyles);

  const navigation = useNavigation();
  const { getUser, updateUser } = useUser();
  const { isLoading: userIsLoading, data: user } = useQuery('user', () =>
    getUser(),
  );
  const queryClient = useQueryClient();

  const isSkillsetLocked = Boolean(user?.accounts[0].pivot.isSkillsetLocked);

  const { mutate: updateUserMutate } = useMutation(updateUser, {
    onSuccess: async () => {
      await queryClient.invalidateQueries('user');
      await queryClient.invalidateQueries('tasks');
    },
  });

  const { data: skills, isLoading: skillsAreLoading } = useSkills();

  if (userIsLoading || skillsAreLoading) {
    return <Spinner />;
  }

  const handleSave = values => {
    updateUserMutate({ skillset_ids: values.skillsetIds });

    if (navigateTo) {
      navigation.navigate(navigateTo);
    } else {
      navigation.goBack();
    }
  };

  if (!user) {
    return null;
  }

  return (
    <>
      {description && (
        <View style={styles.container}>
          <Text style={styles.descriptionContainer}>{description}</Text>
        </View>
      )}
      <Formik initialValues={user} onSubmit={handleSave} enableReinitialize>
        {({ handleSubmit, values }) => (
          <KeyboardAvoidingView style={styles.container}>
            <Layout style={styles.formContainer} level="1">
              <CheckboxesInput
                propertyName="skillsetIds"
                dataset={skills}
                disabled={isSkillsetLocked}
              />
            </Layout>
            {isSkillsetLocked && (
              <View style={styles.formContainer}>
                <Text>
                  * Skill selection has been locked by a team administratior.
                </Text>
              </View>
            )}

            <Button
              style={styles.saveButton}
              size="giant"
              onPress={handleSubmit}
              disabled={!values.skillsetIds.length || isSkillsetLocked}
            >
              Save Skills
            </Button>
            {showCancelButton && (
              <Button
                style={styles.saveButton}
                appearance="outline"
                size="giant"
                onPress={() =>
                  navigation.reset({
                    index: 0,
                    routes: [{ name: 'ProfileScreen' }],
                  })
                }
              >
                Cancel
              </Button>
            )}
          </KeyboardAvoidingView>
        )}
      </Formik>
    </>
  );
};

const themedStyles = StyleService.create({
  container: {
    backgroundColor: 'background-basic-color-1',
  },
  headerContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 216,
    backgroundColor: 'color-primary-default',
  },
  profileAvatar: {
    width: 116,
    height: 116,
    borderRadius: 58,
    alignSelf: 'center',
    backgroundColor: 'background-basic-color-1',
    tintColor: 'color-primary-default',
  },
  editAvatarButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  formContainer: {
    flex: 1,
    padding: 16,
  },
  signUpLabel: {
    marginTop: 16,
  },
  input: {
    marginTop: 16,
  },
  saveButton: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  descriptionContainer: {
    margin: 16,
  },
});

export default SkillForm;
