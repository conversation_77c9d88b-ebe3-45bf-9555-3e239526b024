import React, { ReactNode } from 'react';
import {
  View,
  Modal as NativeModal,
  ScrollView,
  ModalProps,
  ViewStyle,
  SafeAreaView,
} from 'react-native';
import { StyleService, useStyleSheet } from '@ui-kitten/components';
import ModalHeader from '~components/HouseKeepingComponents/Shared/ModalHeader';

type Props = {
  visible: boolean;
  title?: string;
  children: ReactNode;
  onClose?: () => void;
  style?: ViewStyle;
  contentStyle?: ViewStyle;
  stickyHeader?: boolean;
} & Omit<ModalProps, 'visible' | 'children'>;

const Modal = ({
  visible,
  onClose,
  title,
  children,
  style,
  contentStyle,
  stickyHeader = true,
  ...modalProps
}: Props) => {
  const styles = useStyleSheet(themedStyles);

  return (
    <NativeModal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
      {...modalProps}
    >
      <View style={styles.container}>
        <SafeAreaView style={[styles.modal, style]}>
          <ScrollView
            style={[styles.content, contentStyle]}
            stickyHeaderIndices={(title || onClose) && stickyHeader ? [0] : []}
          >
            {(title || onClose) && (
              <ModalHeader title={title} onClose={onClose} />
            )}
            {children}
          </ScrollView>
        </SafeAreaView>
      </View>
    </NativeModal>
  );
};

const themedStyles = StyleService.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  backdrop: {
    position: 'absolute',
    inset: 0,
  },
  modal: {
    flex: 1,
    margin: 16,
    borderRadius: 4,
    overflow: 'hidden',
    backgroundColor: 'white',
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
});

export default Modal;
