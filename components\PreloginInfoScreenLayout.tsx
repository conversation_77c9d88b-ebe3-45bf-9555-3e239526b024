import React, { ReactNode } from 'react';
import { View } from 'react-native';
import {
  Layout,
  StyleService,
  Text,
  useStyleSheet,
} from '@ui-kitten/components';

const appName = process.env.APP_NAME;

type Props = {
  title: string;
  children: ReactNode;
};

const PreloginInfoScreenLayout = ({ title, children }: Props) => {
  const styles = useStyleSheet(themedStyles);

  return (
    <>
      <View style={styles.headerContainer}>
        <Text category="h1" status="control">
          {appName}
        </Text>
        <Text style={styles.title} category="s1" status="control">
          {title}
        </Text>
      </View>
      <Layout style={styles.contentContainer} level="1">
        {children}
      </Layout>
    </>
  );
};

export default PreloginInfoScreenLayout;

const themedStyles = StyleService.create({
  headerContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 216,
    backgroundColor: 'color-primary-default',
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    marginTop: 16,
  },
});
