import React, { ReactElement } from 'react';
import { StyleService, useStyleSheet, Text, Icon } from '@ui-kitten/components';
import { View } from 'react-native';

type Props = {
  title: string;
};

const CollapsedTaskCardHeader = ({ title }: Props): ReactElement => {
  const styles = useStyleSheet(themedStyles);

  return (
    <View style={styles.container}>
      <Text style={styles.text}>{title}</Text>
      <Icon
        style={styles.icon}
        fill="black"
        name="arrow-ios-downward-outline"
      />
    </View>
  );
};

const themedStyles = StyleService.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 16,
    marginVertical: 2,
    padding: 16,
    backgroundColor: 'white',
    borderWidth: 1,
    borderRadius: 4,
    borderColor: '#e4e9f2',
  },
  icon: {
    width: 18,
    height: 18,
  },
  text: {
    paddingTop: 2,
    fontSize: 16,
    fontWeight: 'bold',
    width: '92%',
  },
});

export default CollapsedTaskCardHeader;
