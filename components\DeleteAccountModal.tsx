import React, { ReactElement } from 'react';
import { View, Text } from 'react-native';
import { useQuery } from 'react-query';
import useUser from '~api/useUser';

import {
  Button,
  Card,
  Input,
  Modal,
  StyleService,
  useStyleSheet,
} from '~node_modules/@ui-kitten/components';

type Props = {
  show: boolean;
  onCancel: () => void;
  onConfirm: () => void;
};

const DeleteAccountModal = ({
  show,
  onCancel,
  onConfirm,
}: Props): ReactElement => {
  const styles = useStyleSheet(themedStyles) as any;

  const [email, setEmail] = React.useState('');

  const { getUser } = useUser();
  const { data: currentUser } = useQuery('user', () => getUser());

  const currentUserEmail = currentUser?.email;

  return (
    <Modal
      visible={show}
      backdropStyle={styles.backdrop}
      onBackdropPress={onCancel}
    >
      <Card style={styles.container}>
        <Text style={styles.text}>
          Enter your e-mail address if you want to PERMANENTLY DELETE your
          Account.
        </Text>
        <Input
          placeholder="Email address"
          style={styles.input}
          value={email}
          onChangeText={nextValue => setEmail(nextValue)}
        />
        <View style={styles.buttons}>
          <Button
            style={styles.button}
            onPress={onConfirm}
            disabled={currentUserEmail !== email}
          >
            DELETE
          </Button>
          <Button style={styles.button} onPress={onCancel} appearance="outline">
            Cancel
          </Button>
        </View>
      </Card>
    </Modal>
  );
};

const themedStyles = StyleService.create({
  container: {
    marginHorizontal: 16,
  },
  buttons: { flexDirection: 'row', justifyContent: 'center' },
  button: {
    marginHorizontal: 16,
  },
  text: {
    fontSize: 16,
    marginBottom: 16,
  },
  input: {
    marginBottom: 16,
  },
});

export default DeleteAccountModal;
