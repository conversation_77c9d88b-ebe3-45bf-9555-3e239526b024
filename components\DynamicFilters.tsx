import * as React from 'react';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { memo, useEffect } from 'react';

const MAX_HEIGHT = 350;
const MIN_HEIGHT = 50;
const DynamicFilters = ({ children, scrollDirection }) => {
  const formHeight = useSharedValue(MAX_HEIGHT);

  useEffect(() => {
    if (scrollDirection === 'down') {
      formHeight.value = MIN_HEIGHT;
    } else if (scrollDirection === 'up') {
      formHeight.value = MAX_HEIGHT;
    }
  }, [formHeight, scrollDirection]);

  const config = {
    duration: 50,
    delay: 0,
  };

  const style = useAnimatedStyle(() => ({
    height: withTiming(formHeight.value, config),
  }));

  return <Animated.View style={style}>{children}</Animated.View>;
};

export default memo(DynamicFilters);
