import { formatDuration, intervalToDuration } from 'date-fns';
import { GeocodingCore } from '@mapbox/search-js-core';
import {
  getCurrentPositionAsync,
  requestForegroundPermissionsAsync,
} from 'expo-location';
import {
  HKChecklist,
  HKExtendedJob,
  HKJob,
  HKPerformType,
  HKSection,
  HKUser,
} from '~types';

export const checklistLabel = (job: HKJob, checklist: HKChecklist) => {
  switch (checklist) {
    case 'person1':
      if (job?.leaderPerforms === 'person1') {
        return job.leaderName;
      }
      if (job?.leaderPerforms === 'person2') {
        return job.helperName;
      }

      return 'Person 1';
    case 'person2':
      if (job?.leaderPerforms === 'person1') {
        return job.helperName;
      }
      if (job?.leaderPerforms === 'person2') {
        return job.leaderName;
      }

      return 'Person 2';
    case 'all':
      if (job?.leaderPerforms === 'all') {
        return job.leaderName;
      }

      return 'All';
    default:
      return 'All';
  }
};

export const enabledTabs = (performing: HKChecklist | null) => {
  switch (performing) {
    case 'person1':
    case 'person2':
      return ['person1', 'person2'];
    case 'all':
      return ['all'];
    default:
      return ['person1', 'person2', 'all'];
  }
};

export const whoAmI = (
  job: HKJob | null,
  helperView: boolean,
): HKUser | null => {
  if (!job || !job.leaderPerforms) {
    return null;
  }

  const { leaderPerforms, leaderName, helperName } = job;

  if (helperView) {
    if (leaderPerforms === 'all') {
      return null;
    }

    return {
      name: helperName,
      role: 'helper',
      performing: leaderPerforms === 'person1' ? 'person2' : 'person1',
    };
  }

  return {
    name: leaderName,
    role: 'leader',
    performing: leaderPerforms,
  };
};

export const getUserFromChecklistToken = (
  job: HKExtendedJob | null,
  checklistToken: string,
): HKUser | null => {
  if (!job || !job.leaderPerforms || !checklistToken) {
    return null;
  }

  const { leaderPerforms, leaderName, helperName, checklistTokens } = job;

  const performing = checklistTokens.find(
    ({ token }) => token === checklistToken,
  )?.checklist;
  if (!performing) {
    return null;
  }

  const role = performing === leaderPerforms ? 'leader' : 'helper';
  const name = role === 'leader' ? leaderName : helperName;

  return {
    name,
    role,
    performing,
  };
};

export const getActiveSection = (
  performing: HKChecklist | null,
  sections: HKSection[],
  job: HKExtendedJob | null,
) => {
  if (!performing || !job) {
    return null;
  }

  const activeJobSection = job.jobSections.find(
    ({ status, checklist }) => status === 'started' && checklist === performing,
  );

  return (
    sections.find(section => section.id === activeJobSection?.sectionId) || null
  );
};

const getRemainingSections = (
  checklist: HKChecklist | null,
  sections: HKSection[],
  job: HKExtendedJob | null,
) => {
  if (!checklist || !job) {
    return [];
  }

  return sections.filter(
    section =>
      !job?.jobSections.find(
        jobSection =>
          jobSection.sectionId === section.id &&
          jobSection.checklist === checklist &&
          jobSection.status === 'completed',
      ),
  );
};

export const getAllRemainingSections = (
  performing: HKChecklist | null,
  sections: HKSection[],
  job: HKExtendedJob | null,
) => {
  if (!performing || !job) {
    return [];
  }

  if (performing === 'all') {
    return getRemainingSections('all', sections, job);
  }

  return [
    ...getRemainingSections('person1', sections, job),
    ...getRemainingSections('person2', sections, job),
  ];
};

export const getChecklistsToShare = (
  performType: HKPerformType,
  leaderPerforms: HKChecklist | null,
): HKChecklist[] | null => {
  if (leaderPerforms === null) {
    return null;
  }

  if (performType === HKPerformType.Assigned) {
    if (leaderPerforms === 'all') {
      return ['all'];
    }
    return ['person1', 'person2'];
  }
  if (leaderPerforms === 'all') {
    return null;
  }
  return (['person1', 'person2'] as HKChecklist[]).filter(
    (checklist: HKChecklist) => checklist !== leaderPerforms,
  );
};

export const pdfReportPath = (jobId: HKJob['id'], token: string) =>
  `${process.env.API_BASE_URL}/jobs/${jobId}/report&token=${token}`;

export const formatPreviewName = (checklist: HKChecklist) => {
  switch (checklist) {
    case 'person1':
      return 'Person 1';
    case 'person2':
      return 'Person 2';
    case 'all':
      return 'All';
    default:
      return 'All';
  }
};

export const intervalToHuman = (
  completedAt?: string | number,
  startedAt?: string | number,
) => {
  if (!completedAt || !startedAt) {
    return 'N/A';
  }

  const startDate = new Date(startedAt);
  const endDate = new Date(completedAt);
  const duration = intervalToDuration({ start: startDate, end: endDate });

  try {
    return (
      formatDuration(duration, { format: ['days', 'hours', 'minutes'] }) ||
      'Less than a minute'
    );
  } catch {
    return 'N/A';
  }
};

export const getLocationPermission = async () => {
  const { status } = await requestForegroundPermissionsAsync();
  if (status !== 'granted') {
    return false;
  }

  return true;
};

export const getLocation = async (geocodingCore: GeocodingCore) => {
  const location = await getCurrentPositionAsync();
  const coordinates = {
    lat: location.coords.latitude,
    lng: location.coords.longitude,
  };
  const response = await geocodingCore.reverse(coordinates);
  const address = response.features[0];
  return { coordinates, address };
};
