import * as FileSystem from 'expo-file-system';
import { FileSystemUploadResult } from 'expo-file-system';
import { Dispatch, SetStateAction } from 'react';
import { QueryClient } from 'react-query';
import Toast from 'react-native-root-toast';
import { BackgroundUpload, BackgroundUploadFileType } from '~types';
import { buildBackgroundUploadFile } from '~helpers';
import {
  BackgroundUploadStatuses,
  UploadWithStatus,
} from '~context/BackgroundUploadProgressContext';
import { invalidateTasksAfterAddOrUpdate } from '~screens/TaskSettings';

export type BackgroundUploadReturnType = {
  localPath: string;
  uploadResult: Promise<FileSystemUploadResult>;
};

export const handleBackgroundUploadStatus = (
  uploads: BackgroundUpload[],
  authToken: string | null,
  setUploads: Dispatch<SetStateAction<UploadWithStatus[]>>,
  queryClient: QueryClient,
) => {
  if (!authToken) {
    Toast.show('Not authorized for uploading files.');

    return false;
  }

  const files = uploads.map(({ uploadUrl, localUrl, type }) =>
    buildBackgroundUploadFile(uploadUrl, localUrl, authToken, type),
  );

  setUploads(prevState => [
    ...prevState,
    ...uploads.map(backgroundUpload => ({
      status: BackgroundUploadStatuses.Started,
      backgroundUpload,
    })),
  ]);

  const promises = files.map(({ promise }) => promise);

  return Promise.all(promises).then(values => {
    values.forEach(value => {
      value.uploadResult.then(response => {
        const { body } = response;
        const { success, data } = JSON.parse(body);

        const { localPath } = value;

        const uploadStatus = success
          ? BackgroundUploadStatuses.Finished
          : BackgroundUploadStatuses.Failed;
        setUploads(prevState =>
          prevState.map(u =>
            u.backgroundUpload.localUrl === localPath
              ? { ...u, status: uploadStatus }
              : u,
          ),
        );

        invalidateTasksAfterAddOrUpdate(queryClient, data.id);
      });
    });

    return values;
  });
};

export default async (
  url: string,
  fileUri: string,
  authToken: string,
  type: BackgroundUploadFileType,
  fieldName = 'media',
): Promise<ReturnType> => ({
  localPath: fileUri,
  uploadResult: FileSystem.uploadAsync(url, fileUri, {
    headers: {
      Authorization: `Bearer ${authToken}`,
      Accept: 'application/json',
    },
    sessionType: FileSystem.FileSystemSessionType.BACKGROUND,
    httpMethod: 'POST',
    fieldName,
    uploadType: FileSystem.FileSystemUploadType.MULTIPART,
    parameters: { type },
  }),
});
