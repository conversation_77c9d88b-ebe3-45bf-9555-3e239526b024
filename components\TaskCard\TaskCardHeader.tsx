import React, { ReactElement } from 'react';
import { View } from 'react-native';
import BackgroundWithSpinner from '~components/BackgroundWithSpinner';
import Label, { LabelType } from '~components/Label';
import Spinner from '~components/Spinner';
import SpinnerWithText from '~components/SpinnerWithText';
import VideoPlayer from '~components/VideoPlayer/VideoPlayer';
import { TaskPriorities } from '~dummyData';
import { determineMediaType, MediaTypes } from '~helpers';
import { Text, StyleService } from '~node_modules/@ui-kitten/components';
import useUserQuery from '~queries/useUser';
import { User } from '~types';

const renderMedia = (mediaUri: string, backgroundUploadTaskId: number) => {
  if (determineMediaType(mediaUri) === MediaTypes.Image) {
    return (
      <BackgroundWithSpinner
        styles={styles.largeMediaView}
        mediaUri={mediaUri}
        backgroundUploadTaskId={backgroundUploadTaskId}
        showExpandButton
      />
    );
  }

  return <VideoPlayer media={mediaUri} />;
};

type Props = {
  id: number;
  finishedAt?: Date;
  priority: TaskPriorities;
  expired?: boolean;
  acceptedOn?: Date;
  repetitionNumber?: number;
  media: string;
  showDetails?: boolean;
  thumbnail: string;
  overdue: number;
  blockedUsers?: User[];
  deletedAt: string;
};

const TaskCardHeader = ({
  id,
  finishedAt,
  priority,
  expired,
  acceptedOn,
  repetitionNumber,
  media,
  showDetails,
  thumbnail,
  overdue,
  blockedUsers,
  deletedAt,
}: Props): ReactElement => {
  const { data: currentUser, isLoading: currentUserIsLoading } = useUserQuery();

  if (currentUserIsLoading) {
    return <Spinner />;
  }

  const isCurrentUserBlocked =
    currentUser &&
    blockedUsers?.find(
      ({ id: blockedUserId }) => blockedUserId === currentUser.id,
    );

  const taskStatus = () => {
    const statuses: LabelType[] = [];

    if (deletedAt) {
      statuses.push(LabelType.Deleted);

      return statuses;
    }

    if (finishedAt) {
      statuses.push(LabelType.Completed);

      return statuses;
    }

    if (overdue) {
      statuses.push(LabelType.Overdue);
    }

    if (priority === TaskPriorities.Urgent) {
      statuses.push(LabelType.Urgent);
    }

    if (isCurrentUserBlocked) {
      statuses.push(LabelType.CurrentUserIsBlocked);
    }

    if (expired) {
      statuses.push(LabelType.Unfinished);

      return statuses;
    }

    if (acceptedOn) {
      statuses.push(LabelType.Accepted);
    }

    return statuses;
  };

  const statuses = taskStatus();

  return (
    <>
      <View style={styles.labelContainer}>
        {statuses.map(status => (
          <Label key={status} type={status} />
        ))}
      </View>
      <Text style={styles.identifier}>
        <>
          {`#${String(id).padStart(5, '0')}`}
          {repetitionNumber && `/${repetitionNumber}`}
        </>
      </Text>
      {media === null ? (
        <SpinnerWithText backgroundUploadTaskId={id} />
      ) : (
        <View style={styles.media}>
          {showDetails ? (
            renderMedia(media)
          ) : (
            <BackgroundWithSpinner
              styles={styles.largeMediaView}
              mediaUri={thumbnail}
            />
          )}
        </View>
      )}
    </>
  );
};

export default TaskCardHeader;

const styles = StyleService.create({
  labelContainer: {
    position: 'absolute',
    zIndex: 100,
    flexDirection: 'row',
    margin: 16,
    width: '80%',
    flexWrap: 'wrap',
  },
  identifier: {
    backgroundColor: 'gray',
    borderRadius: 6,
    color: 'white',
    position: 'absolute',
    right: 0,
    zIndex: 100,
    margin: 16,
    padding: 8,
    opacity: 0.9,
    fontSize: 10,
    fontWeight: 'bold',
  },
  largeMediaView: {
    alignSelf: 'center',
    width: '100%',
    height: 200,
  },
  media: {
    width: '100%',
    height: 200,
    alignSelf: 'center',
  },
});
