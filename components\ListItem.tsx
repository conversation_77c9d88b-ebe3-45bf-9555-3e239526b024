import React, { ReactNode } from 'react';
import { StyleSheet } from 'react-native';
import { Divider, Layout, Text } from '~node_modules/@ui-kitten/components';
import { View } from '~components/Themed';

type ListItemProps = {
  name: ReactNode;
  description?: string;
  button?: ReactNode;
};

const ListItem = ({ name, description, button }: ListItemProps) => (
  <>
    <Layout level="1" style={styles.container}>
      <View style={styles.name}>{name}</View>
      {description && (
        <View style={styles.role}>
          <Text>{description}</Text>
        </View>
      )}
      {button && <View>{button}</View>}
    </Layout>
    <Divider />
  </>
);

const styles = StyleSheet.create({
  container: {
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  name: {
    marginRight: 'auto',
  },
  role: {
    marginRight: 16,
  },
});

export default ListItem;
