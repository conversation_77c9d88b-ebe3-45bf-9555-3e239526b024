import useTasksAPI from '~api/useTasks';
import { useQuery } from '~node_modules/react-query';

type Props = {
  id: number;
  showCompletions?: boolean;
};

const useTask = ({ id, showCompletions }: Props) => {
  const { getTask } = useTasksAPI();

  const { data, isLoading } = useQuery(['task', id], () =>
    getTask(id, showCompletions),
  );

  return { data, isLoading };
};

export default useTask;
