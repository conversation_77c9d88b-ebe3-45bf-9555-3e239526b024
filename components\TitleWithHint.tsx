import * as React from 'react';
import { TextStyle } from 'react-native';
import { StyleService, Text } from '~node_modules/@ui-kitten/components';
import { View } from '~components/Themed';
import Hint from './Hint';

type Props = {
  text: string;
  hint: string;
  textStyle?: TextStyle;
};

const TitleWithHint = ({
  text,
  textStyle = {},
  hint,
}: Props): React.ReactElement => (
  <View style={styles.container}>
    <Text category="h1" style={{ ...styles.text, ...textStyle }}>
      {text}
    </Text>
    <Hint text={hint} />
  </View>
);

const styles = StyleService.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  text: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default TitleWithHint;
