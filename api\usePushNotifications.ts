import useClient from './useClient';

export default () => {
  const client = useClient();

  const subscribeForPush = (token: string, JWT?: string) =>
    client
      .post(`/exponent/devices/subscribe${JWT ? `?token=${JWT}` : ''}`, {
        expo_token: token,
      })
      .then(res => res.data);

  const unsubscribeFromPush = (token: string, JWT?: string) =>
    client
      .post(`/exponent/devices/unsubscribe${JWT ? `?token=${JWT}` : ''}`, {
        expo_token: token,
      })
      .then(res => res.data);

  return {
    subscribeForPush,
    unsubscribeFromPush,
  };
};
