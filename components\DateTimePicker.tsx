import React, { useState } from 'react';
import { View, Modal, StyleProp, ViewStyle, TextStyle } from 'react-native';
import { Calendar, DateData } from 'react-native-calendars';
import {
  Button,
  Text,
  StyleService,
  useStyleSheet,
} from '@ui-kitten/components';
import { format, eachDayOfInterval, startOfMonth, endOfMonth } from 'date-fns';
import { TimerPickerModal } from 'react-native-timer-picker';
import * as Haptics from 'expo-haptics';
import { Audio } from 'expo-av';
import { LinearGradient } from 'expo-linear-gradient';
import { utcToZonedTime } from 'date-fns-tz';
import clickSoundAsset from '~/assets/sounds/time_select_click.mp3';
import { buttonMainColor, buttonSecondaryColor } from '~constants/Colors';

type Props = {
  type: 'date' | 'time';
  value: string;
  onChange: (date: string) => void;
  style: StyleProp<ViewStyle>;
  label?: string;
  placeholder?: string;
  enabledDates?: string[];
};

const DateTimePicker = ({
  type,
  value,
  onChange,
  style,
  label,
  placeholder,
  enabledDates,
}: Props) => {
  const styles = useStyleSheet(themedStyles);
  const [date, setDate] = useState(value ? new Date(value) : new Date());
  const [show, setShow] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(startOfMonth(new Date()));

  const formatViewValue = () => {
    if (type === 'time') {
      return format(new Date(value), 'p');
    }

    return format(utcToZonedTime(new Date(value), 'UTC'), 'PP');
  };

  const trigger = (
    <Button
      style={styles.button}
      onPress={() => setShow(true)}
      appearance="outline"
      status="basic"
    >
      {value
        ? formatViewValue()
        : placeholder || `Select ${type === 'time' ? 'Time' : 'Date'}`}
    </Button>
  );

  if (type === 'date') {
    const generateMarkedDates = (month: Date) => {
      const start = startOfMonth(month);
      const end = endOfMonth(month);

      const allDates = eachDayOfInterval({ start, end });
      const enabledDatesSet = new Set(enabledDates);

      const markedDates = allDates.reduce((acc, d) => {
        const dateStr = format(d, 'yyyy-MM-dd');

        if (!enabledDatesSet.has(dateStr)) {
          acc[dateStr] = { disabled: true, disableTouchEvent: true };
        }

        return acc;
      }, {});

      return markedDates;
    };

    const handleDayPress = (selectedDate: DateData) => {
      const dateObj = new Date(selectedDate.timestamp);
      setDate(dateObj);
      setShow(false);
      onChange(dateObj.toString());
    };

    const handleCancel = () => {
      setShow(false);
    };

    const handleMonthChange = (month: DateData) => {
      setCurrentMonth(new Date(month.timestamp));
    };

    return (
      <>
        <View style={style}>
          {label && <Text style={styles.label}>{label}</Text>}
          {trigger}
        </View>
        <Modal
          transparent
          visible={show}
          animationType="slide"
          onRequestClose={handleCancel}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalView}>
              <Calendar
                current={format(date, 'yyyy-MM-dd')}
                onDayPress={handleDayPress}
                onMonthChange={handleMonthChange}
                markedDates={
                  enabledDates ? generateMarkedDates(currentMonth) : undefined
                }
              />
              <Button onPress={handleCancel}>Cancel</Button>
            </View>
          </View>
        </Modal>
      </>
    );
  }

  const buttonProps: TextStyle = {
    borderRadius: 4,
    fontWeight: 'bold',
    borderColor: buttonMainColor,
    fontSize: 14,
  };

  return (
    <View style={style}>
      {trigger}
      <TimerPickerModal
        visible={show}
        setIsVisible={setShow}
        onConfirm={({ hours, minutes }) => {
          const time = new Date();
          time.setHours(hours);
          time.setMinutes(minutes);
          onChange(time.toString());
          setShow(false);
        }}
        onCancel={() => setShow(false)}
        closeOnOverlayPress
        Audio={Audio}
        clickSoundAsset={clickSoundAsset}
        LinearGradient={LinearGradient}
        Haptics={Haptics}
        hideSeconds
        use12HourPicker
        styles={{
          cancelButton: {
            ...buttonProps,
            color: buttonMainColor,
            backgroundColor: buttonSecondaryColor,
          },
          confirmButton: {
            ...buttonProps,
            color: 'white',
            backgroundColor: buttonMainColor,
          },
        }}
      />
    </View>
  );
};

const themedStyles = StyleService.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalView: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 35,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  label: {
    fontWeight: '700',
    fontSize: 12,
    color: 'gray',
  },
  button: {
    justifyContent: 'flex-start',
  },
});

export default DateTimePicker;
