import React, { useState } from 'react';
import { ScrollView } from 'react-native';
import {
  Button,
  StyleService,
  useStyleSheet,
  Text,
} from '@ui-kitten/components';
import { useToast } from 'react-native-toast-notifications';
import { AccountMember } from '~components/AccountMember';
import { useMutation, useQuery } from '~node_modules/react-query';
import useUser from '~api/useUser';
import { View } from '~components/Themed';
import { EditIcon } from '~components/Icon';
import TimezoneSelect from '~components/TimezoneSelect';

export default ({ navigation }): React.ReactElement => {
  const toast = useToast();
  const styles = useStyleSheet(themedStyle);
  const { getUser, getAccountMembers, getPendingInvitations, putAccount } =
    useUser();
  const { data: user } = useQuery('user', () => getUser());
  const accountId = user?.accounts?.[0].id;
  const { isLoading: accountMembersIsLoading, data: accountMembers } = useQuery(
    ['accountMembers', accountId],
    () => getAccountMembers(accountId, true),
    { enabled: !!accountId },
  );
  const { isLoading: pendingInvitationsIsLoading, data: pendingInvitations } =
    useQuery('pendingInvitations', getPendingInvitations);

  const [selectedTimezone, setSelectedTimezone] = useState(
    user.accounts[0].timezone
      ? user.accounts[0].timezone
      : 'America/Los_Angeles',
  );

  const { mutate } = useMutation(data => putAccount(accountId, data), {
    onSuccess: async () => {
      toast.show(`You have succesfully updated the timezone of your account`);
    },
  });

  const handleSaveTimezone = async data => {
    setSelectedTimezone(data);
    await mutate({ timezone: data });
  };

  if (
    accountMembersIsLoading ||
    pendingInvitationsIsLoading ||
    !accountMembers
  ) {
    return null;
  }

  return (
    <ScrollView>
      <View style={styles.header}>
        <Text style={styles.headerText}>{user.accounts[0].name}</Text>
        <Button
          accessoryLeft={EditIcon}
          onPress={() =>
            navigation.push('AccountEditScreen', {
              accountId,
            })
          }
        />
      </View>
      <Text style={styles.heading}>Timezone</Text>
      <View>
        <TimezoneSelect
          value={selectedTimezone}
          onSelect={handleSaveTimezone}
        />
      </View>
      <Text style={styles.heading}>Members</Text>
      <View>
        {accountMembers
          .filter(member => member?.pivot?.deletedAt === null)
          .map(({ id, name, email, role }) => (
            <AccountMember
              key={id}
              name={name}
              email={email}
              role={role}
              onEdit={() =>
                navigation.push('MemberScreen', {
                  accountId,
                  memberId: id,
                  userId: user.id,
                })
              }
            />
          ))}
      </View>
      <Text style={styles.heading}>Deleted Members</Text>
      <View>
        {accountMembers
          .filter(member => member?.pivot?.deletedAt !== null)
          .map(({ id, name, email, role }) => (
            <AccountMember
              key={id}
              name={name}
              email={email}
              role={role}
              onEdit={() =>
                navigation.push('MemberScreen', {
                  accountId,
                  memberId: id,
                  userId: user.id,
                })
              }
            />
          ))}
      </View>
      <Text style={styles.heading}>Pending Invitations</Text>
      <View>
        {pendingInvitations.map(({ id, name, email, role }) => (
          <AccountMember
            key={id}
            name={name}
            email={email}
            role={role}
            onEdit={() =>
              navigation.push('MemberScreen', {
                invitationId: id,
              })
            }
          />
        ))}
      </View>
      <Button
        style={styles.inviteButton}
        appearance="outline"
        size="giant"
        onPress={() => navigation.push('MemberScreen')}
      >
        Invite New Member
      </Button>
    </ScrollView>
  );
};

const themedStyle = StyleService.create({
  inviteButton: {
    margin: 16,
  },
  header: {
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    background: 'white',
  },
  headerText: {
    fontSize: 24,
  },
  heading: {
    fontSize: 18,
    margin: 16,
  },
});
