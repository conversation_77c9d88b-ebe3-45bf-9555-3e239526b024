import * as React from 'react';
import { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import { StackScreenProps } from '@react-navigation/stack';
import {
  Button,
  StyleService,
  useStyleSheet,
  Modal,
} from '@ui-kitten/components';
import { View } from 'react-native';
import { useToast } from 'react-native-toast-notifications';
import Confirm from '~components/Confirm';
import useTasks from '~api/useTasks';
import { PinIcon } from '~components/Icon';
import PropertyCard from '~components/PropertyCard';
import Spinner from '~components/Spinner';
import useUser from '~api/useUser';
import { invalidateQueries } from '~helpers';
import useTaskAssignment from '~hooks/useTaskAssignment';
import TaskCompletionCard from '~components/TaskCompletionCard';
import { UserRoles } from '~dummyData';
import TaskAssignmentCard from '~components/TaskAssignmentCard';
import CommentsCard from '~components/TaskComments';
import KeyboardAvoidingView from '~components/KeyboardAvoidingView';

type Props = StackScreenProps<MyTasksParamList>;

const TaskAssignmentDetails = ({
  route,
  navigation,
}: Props): React.ReactElement => {
  const styles = useStyleSheet(themedStyles);

  const taskAssignmentId = route?.params?.id;
  const showMinimal = route?.params?.showMinimal;

  const toast = useToast();

  const [showConfirmCancel, setShowConfirmCancel] = useState(false);
  const [showAccessInfo, setShowAccessInfo] = useState(false);
  const [showConfirmDelete, setShowConfirmDelete] = useState(false);

  const { removeTaskAssignment } = useTasks();
  const { deleteTask } = useTasks();
  const { getUser } = useUser();

  const queryClient = useQueryClient();
  const { data: currentUser, isLoading: curentUserIsLoading } = useQuery(
    'user',
    () => getUser(),
  );

  const { data: taskAssignment, isLoading } = useTaskAssignment({
    id: taskAssignmentId,
  });

  const { mutate: removeTaskAssignmentMutate } = useMutation(
    removeTaskAssignment,
    {
      onSuccess: async () => {
        navigation.goBack();
        await invalidateAfterRemoveOrDelete();
        toast.show('Your Job has been moved back to Available Jobs.');
      },
    },
  );

  const { mutate: deleteTaskMutate } = useMutation(deleteTask, {
    onSuccess: async () => {
      await invalidateAfterRemoveOrDelete();
      navigation.goBack();
    },
  });

  if (isLoading || curentUserIsLoading || !currentUser || !taskAssignment) {
    return <Spinner />;
  }

  const currentUserIsAdmin =
    currentUser?.accounts[0].pivot.role === UserRoles.Admin;
  const currentUserIsTheTaskOwner = taskAssignment.userId === currentUser.id;

  const { task } = taskAssignment;

  const invalidateAfterRemoveOrDelete = () =>
    invalidateQueries(queryClient, [
      { key: 'task-assignments', options: { exact: true } },
      { key: 'tasks-created-by-me', options: {} },
      { key: 'tasks', options: {} },
      { key: 'completed-tasks', options: {} },
    ]);

  const handleDeleteTask = () => {
    deleteTaskMutate(task.id);
  };

  const removeFromMyTasks = () => {
    removeTaskAssignmentMutate(taskAssignment.id);
  };

  let actionButton;

  const handleClickStart = () => {
    if (task.property.housekeepingEnabled) {
      const hk = taskAssignment?.housekeepingChecklistJob;
      navigation.navigate('House Keeping', {
        screen: 'HouseKeepingScreen',
        params: {
          jobId: hk?.id,
          propertyId: !hk ? task.property.id : undefined,
          taskAssignmentId: !hk ? taskAssignment.id : undefined,
        },
      });
    } else {
      navigation.navigate('FinishTaskScreen', {
        taskAssignmentId: taskAssignment.id,
      });
    }
  };

  if (!taskAssignment.finishedAt) {
    actionButton = (
      <>
        <Button style={styles.button} onPress={handleClickStart}>
          Start
        </Button>
        <Button
          style={styles.button}
          onPress={() => setShowConfirmCancel(true)}
          appearance="outline"
        >
          Remove From My Jobs
        </Button>
        <Confirm
          show={showConfirmCancel}
          onCancel={() => setShowConfirmCancel(false)}
          onConfirm={removeFromMyTasks}
          text="Are you sure you want to remove this job from your Jobs?"
        />
      </>
    );
  }

  return (
    <KeyboardAvoidingView contentContainerStyle={styles.container}>
      <TaskAssignmentCard taskAssignment={taskAssignment} showDetails>
        <>
          {!taskAssignment.finishedAt && (
            <>
              <Button
                style={styles.accessButton}
                size="small"
                accessoryLeft={PinIcon}
                onPress={() => setShowAccessInfo(true)}
              >
                How can I access the property?
              </Button>
              <Modal
                visible={showAccessInfo}
                style={styles.modal}
                backdropStyle={styles.backdrop}
                onBackdropPress={() => setShowAccessInfo(false)}
              >
                <View>
                  <PropertyCard {...task.property} showDetails />
                </View>
                <Button
                  style={styles.button}
                  onPress={() => setShowAccessInfo(false)}
                >
                  Close
                </Button>
              </Modal>
            </>
          )}
          {!showMinimal && actionButton}
          {(currentUserIsTheTaskOwner || currentUserIsAdmin) && (
            <>
              <Button
                style={styles.button}
                onPress={() =>
                  navigation.navigate('TaskHistoryScreen', {
                    taskAssignmentId: taskAssignment.id,
                    description: task.description,
                  })
                }
                appearance="outline"
              >
                Task History
              </Button>
              <Button
                style={styles.button}
                onPress={() => setShowConfirmDelete(true)}
                appearance="outline"
              >
                Delete Task
              </Button>
              <Confirm
                show={showConfirmDelete}
                onCancel={() => setShowConfirmDelete(false)}
                onConfirm={handleDeleteTask}
                text="Are you sure you want to delete this job?"
              />
            </>
          )}
        </>
      </TaskAssignmentCard>
      <CommentsCard
        taskId={task.id}
        taskAssignmentId={taskAssignmentId}
        comments={task.comments}
      />
      {taskAssignment.finishedAt && (
        <TaskCompletionCard
          taskAssignment={taskAssignment}
          currentUserIsAdmin={currentUserIsAdmin}
          currentUserIsTheTaskOwner={currentUserIsTheTaskOwner}
          navigation={navigation}
        />
      )}
    </KeyboardAvoidingView>
  );
};

const themedStyles = StyleService.create({
  container: {
    paddingVertical: 16,
  },
  button: {
    marginTop: 16,
  },
  accessButton: {
    marginHorizontal: 10,
    marginTop: 16,
    alignSelf: 'center',
  },
  modal: {
    paddingVertical: 42,
    justifyContent: 'space-between',
    width: '100%',
    height: '100%',
    backgroundColor: 'white',
  },
  backdrop: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
});

export default TaskAssignmentDetails;
