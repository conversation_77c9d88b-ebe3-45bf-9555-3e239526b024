import { Button, Text, Tooltip } from '@ui-kitten/components';
import React, { ReactElement } from 'react';
import { StyleProp, StyleSheet, View, ViewStyle } from 'react-native';
import { TODAY_DESCRIPTION } from '~constants/Descriptions';
import { priorities, TaskPriorities } from '~dummyData';
import { priorityIsUrgentOrAnytime } from '~helpers';
import useTimezone from '~hooks/useTimezone';
import { InfoIcon } from './Icon';
import Hint from './Hint';
import { carrotColor } from '~constants/Colors';
import IconTextRow from './TaskCard/IconTextRow';

type Props = {
  priority: number;
  isAssignment?: boolean;
  preferredDate: string;
  styles: StyleProp<ViewStyle>;
  copiedForToday?: boolean;
  overdueInDays: number;
  finishedAt?: Date;
  propertyAvailability: string;
};

const TaskCardPreferredDate = ({
  priority,
  isAssignment,
  preferredDate,
  styles,
  copiedForToday,
  overdueInDays,
  finishedAt,
  propertyAvailability,
}: Props): ReactElement => {
  const [isTooltipVisible, setIsTooltipVisible] = React.useState(false);
  const { formatInTimeZone } = useTimezone();

  const preferredDateWithHourMinutes = formatInTimeZone(
    preferredDate,
    'Y MMMM d. HH:mm',
  );
  const preferredDateWithoutHourMinues = formatInTimeZone(
    preferredDate,
    'Y MMMM d.',
  );

  let text = '';

  if (priority === TaskPriorities.OnCheckoutDate && isAssignment) {
    text = preferredDateWithoutHourMinues;
  }

  if (priority === TaskPriorities.OnCheckoutDate) {
    text = formatInTimeZone(preferredDate, 'PP');
  }

  if (priorityIsUrgentOrAnytime(priority)) {
    text = priorities.find(item => item.id === priority)?.name ?? '';
  }

  if (priority === TaskPriorities.OnSetDate) {
    text = preferredDateWithHourMinutes;
  }

  const priorityInfo = priorities.find(
    taskPriority => taskPriority.id === priority,
  )?.info;

  const renderToggleButton = () => (
    <Button
      style={localStyles.button}
      onPress={() => setIsTooltipVisible(true)}
      accessoryLeft={InfoIcon}
      appearance="ghost"
    />
  );

  const renderDateText = () => (
    <View style={localStyles.dateTextWrapper}>
      <Text
        style={[
          localStyles.text,
          {
            textDecorationLine: copiedForToday ? 'line-through' : 'none',
          },
        ]}
      >
        {text}
        {!finishedAt &&
        overdueInDays > 0 &&
        !priorityIsUrgentOrAnytime(priority)
          ? ` (${overdueInDays} days overdue)`
          : ''}
      </Text>
      {priorityInfo && <Hint text={priorityInfo} size="small" />}
      {copiedForToday && (
        <>
          <Text style={localStyles.text}>Today</Text>
          <Tooltip
            style={localStyles.tooltip}
            anchor={renderToggleButton}
            visible={isTooltipVisible}
            onBackdropPress={() => setIsTooltipVisible(false)}
          >
            {() => (
              <Text category="h1" style={localStyles.tooltipText}>
                {TODAY_DESCRIPTION}
              </Text>
            )}
          </Tooltip>
        </>
      )}
    </View>
  );

  return (
    <>
      <IconTextRow iconName="clock" containerStyle={styles}>
        {renderDateText()}
      </IconTextRow>
      <IconTextRow iconName="clock" containerStyle={styles}>
        {propertyAvailability}
      </IconTextRow>
    </>
  );
};

const localStyles = StyleSheet.create({
  text: {
    fontSize: 12,
    fontWeight: 'bold',
    margin: 2,
    color: carrotColor,
  },
  button: {
    width: 0,
    height: 0,
  },
  tooltip: {
    width: 260,
    backgroundColor: 'white',
    borderColor: '#999',
  },
  tooltipText: {
    paddingHorizontal: 5,
    paddingVertical: 10,
    fontSize: 15,
    fontWeight: 'normal',
    color: '#666',
    lineHeight: 20,
  },
  dateTextWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default TaskCardPreferredDate;
