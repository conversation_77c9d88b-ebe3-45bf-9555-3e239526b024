import React from 'react';
import { View } from 'react-native';
import { StyleService, Text, useStyleSheet } from '@ui-kitten/components';
import { ClockOutlineIcon } from '~/components/Icon';
import ElapsedTime from '~components/HouseKeepingComponents/ChecklistTab/ElapsedTime';
import Group from '~components/HouseKeepingComponents/UI/Group';
import Badge from '~components/HouseKeepingComponents/UI/Badge';
import useTimezone from '~hooks/useTimezone';

type Props = {
  title: string;
  description: string;
  firstSectionStartedAt?: string;
  startedAt?: string;
  completedAt?: string;
};

const ChecklistSectionHeader = ({
  title,
  description,
  firstSectionStartedAt,
  startedAt,
  completedAt,
}: Props) => {
  const styles = useStyleSheet(themedStyles);
  const { formatInTimeZone } = useTimezone();
  const startedAtWithTimezone = formatInTimeZone(startedAt, 'PP p');
  const showTotal =
    firstSectionStartedAt && firstSectionStartedAt !== startedAt;

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}</Text>
      <Text>{description}</Text>
      {startedAt && !completedAt && (
        <Group style={styles.badgeGroup}>
          <Badge
            size="large"
            style={styles.icon}
            leftSection={ClockOutlineIcon}
          >
            {startedAtWithTimezone}
          </Badge>
          <Badge
            size="large"
            style={styles.icon}
            leftSection={ClockOutlineIcon}
          >
            Section: <ElapsedTime startTime={startedAt} />
          </Badge>
          {showTotal && (
            <Badge
              size="large"
              style={styles.icon}
              leftSection={ClockOutlineIcon}
            >
              Total: <ElapsedTime startTime={firstSectionStartedAt} />
            </Badge>
          )}
        </Group>
      )}
    </View>
  );
};

export default ChecklistSectionHeader;

const themedStyles = StyleService.create({
  badgeGroup: {
    marginVertical: 8,
    gap: 8,
  },
  container: {
    padding: 5,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  icon: {
    paddingLeft: 5,
  },
});
