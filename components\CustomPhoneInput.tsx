import { StyleService } from '@ui-kitten/components';
import { Field } from 'formik';
import React from 'react';
import PhoneInput from 'react-native-phone-number-input';
import parsePhoneNumber from 'libphonenumber-js';
import { View } from 'react-native';

type Props = {
  name: string;
  initialPhoneValue?: string;
  status?: 'danger';
};

const CustomPhoneInput = ({ name, initialPhoneValue, status }: Props) => {
  let phoneNumber;

  if (initialPhoneValue) {
    phoneNumber = parsePhoneNumber(initialPhoneValue);
  }

  return (
    <View style={status === 'danger' && styles.dangerBorder}>
      <Field>
        {({ form: { setFieldValue } }) => (
          <PhoneInput
            containerStyle={styles.phoneInputContainer}
            textContainerStyle={styles.phoneInputTextContainer}
            codeTextStyle={styles.phoneInputCodeText}
            flagButtonStyle={styles.phoneInputCodeText}
            layout="second"
            placeholder="Phone number"
            onChangeFormattedText={value => {
              setFieldValue(name, value);
            }}
            defaultValue={phoneNumber ? phoneNumber?.nationalNumber : ''}
            defaultCode={phoneNumber ? phoneNumber.country : 'US'}
          />
        )}
      </Field>
    </View>
  );
};

export default CustomPhoneInput;

const styles = StyleService.create({
  phoneInputContainer: {
    borderWidth: 1,
    borderColor: 'rgb(228, 233, 242)',
    borderRadius: 4,
    height: 40,
    width: '100%',
  },
  phoneInputTextContainer: {
    backgroundColor: 'rgb(247, 249, 252)',
    paddingVertical: 0,
  },
  phoneInputCodeText: {
    backgroundColor: 'rgb(247, 249, 252)',
  },
  dangerBorder: {
    borderWidth: 0.5,
    borderRadius: 4,
    borderColor: 'red',
  },
});
