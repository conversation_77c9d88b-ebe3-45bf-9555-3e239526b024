import React from 'react';
import { HKJob } from '~/types';
import AssignRolesForm from '~/components/HouseKeepingComponents/AssignRolesForm/AssignRolesForm';
import Modal from '~components/HouseKeepingComponents/Shared/Modal';

type Props = {
  values: HKJob | null;
  opened: boolean;
  onClose: () => void;
  onSave: (values: HKJob) => Promise<void>;
};

const AssignRolesModal = ({ values, opened, onClose, onSave }: Props) => (
  <Modal visible={opened} onClose={onClose} title="Assign Roles">
    <AssignRolesForm values={values} onSave={onSave} />
  </Modal>
);

export default AssignRolesModal;
