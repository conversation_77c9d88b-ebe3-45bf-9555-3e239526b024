import React from 'react';
import { PersonIcon, PeopleOutlineIcon } from '~/components/Icon';
import { HKChecklistOption, HKPerformType } from '~types';

const CHECKLIST_OPTIONS: HKChecklistOption[] = [
  {
    value: HKPerformType.Personally,
    label: 'Yes',
    description: 'I am going to perform the checklist fully or partially.',
    icon: <PersonIcon fill="black" width={24} />,
  },
  {
    value: HKPerformType.Assigned,
    label: 'No',
    description: 'I am going to assign the whole checklist to other people.',
    icon: <PeopleOutlineIcon fill="black" width={24} />,
  },
];

export default CHECKLIST_OPTIONS;
