import React from 'react';
import { addHours, isAfter, isBefore, subHours } from 'date-fns';
import { StyleService, Text, useStyleSheet } from '@ui-kitten/components';
import { StyleProp, View, ViewStyle } from 'react-native';
import DateTimePicker from './DateTimePicker';
import { Task } from '~types';

type Props = {
  values: Task;
  setFieldValue: (field: string, value: Date) => void;
  enabledDates?: string[];
  style: StyleProp<ViewStyle>;
};

const PreferredDateEditor = ({
  values,
  setFieldValue,
  enabledDates,
  style,
}: Props) => {
  const styles = useStyleSheet(themedStyles);

  const handleChangePreferredStartTime = (date: string) => {
    const startDate = new Date(date);
    const endDate = new Date(values.preferredEndTime);

    const minEndDate = addHours(startDate, 2);
    if (!values.preferredEndTime || isBefore(endDate, minEndDate)) {
      setFieldValue('preferredEndTime', minEndDate);
    }
    setFieldValue('preferredStartTime', startDate);
  };

  const handleChangePreferredEndTime = (date: string) => {
    const endDate = new Date(date);
    const startDate = new Date(values.preferredStartTime);

    const maxStartDate = subHours(endDate, 2);

    if (!values.preferredStartTime || isAfter(startDate, maxStartDate)) {
      setFieldValue('preferredStartTime', maxStartDate);
    }
    setFieldValue('preferredEndTime', endDate);
  };

  return (
    <View style={style}>
      <DateTimePicker
        style={styles.input}
        type="date"
        label="Preferred Date"
        value={values.preferredDate}
        onChange={date => setFieldValue('preferredDate', new Date(date))}
        enabledDates={enabledDates}
      />
      <Text style={styles.formHeading}>Preferred Timeframe</Text>
      <View style={styles.timeframeContainer}>
        <DateTimePicker
          placeholder="From"
          style={styles.timeframePicker}
          type="time"
          value={values.preferredStartTime}
          onChange={handleChangePreferredStartTime}
        />
        <DateTimePicker
          placeholder="To"
          style={styles.timeframePicker}
          type="time"
          value={values.preferredEndTime}
          onChange={handleChangePreferredEndTime}
        />
      </View>
    </View>
  );
};

const themedStyles = StyleService.create({
  input: {
    marginVertical: 8,
  },
  timeframeContainer: {
    zIndex: 1,
    flexDirection: 'row',
    gap: 16,
  },
  timeframePicker: {
    flexGrow: 1,
  },
  formHeading: {
    fontSize: 12,
    fontWeight: '700',
    color: 'gray',
  },
});

export default PreferredDateEditor;
