import { MutableRefObject, ReactElement, useEffect, useRef } from 'react';
import * as Notifications from 'expo-notifications';

enum NavigationActions {
  OpenTask = 'open-task',
  OpenAvailableTasks = 'open-available-tasks',
  OpenTaskAssignment = 'open-task-assignment',
}

type Props = {
  children: ReactElement;
  nav: MutableRefObject<null>;
};

type PushDataProps = {
  data: { action: NavigationActions; taskId?: number };
};

const navigate = (
  nav,
  routes: Array<{ name: string; params? }>,
  stack?: string,
) => {
  nav.reset({
    index: 0,
    routes: [
      {
        name: stack || 'Available Jobs',
        state: {
          routes,
        },
      },
    ],
  });
};

const notificationNavigationHandler = ({ data }: PushDataProps, nav) => {
  const adminNavigationRoutes = [
    { name: 'AdminScreen' },
    { name: 'AllTasksScreen' },
  ];

  if (data.action === NavigationActions.OpenTaskAssignment) {
    adminNavigationRoutes.push({
      name: 'TaskAssignmentScreen',
      params: { id: data.taskId, showMinimal: true },
    });

    navigate(nav.current, adminNavigationRoutes, 'More');
  }

  const availableTasksNavigationRoutes = [{ name: 'AvailableTasksScreen' }];

  if (data.action === NavigationActions.OpenTask) {
    availableTasksNavigationRoutes.push({
      name: 'TaskScreen',
      params: { id: data.taskId },
    });
    navigate(nav.current, availableTasksNavigationRoutes);
  }

  if (data.action === NavigationActions.OpenAvailableTasks) {
    navigate(nav.current, availableTasksNavigationRoutes);
  }
};

const NotificationHandler = ({ children, nav }: Props): ReactElement => {
  const responseListener = useRef<Notifications.Subscription>();

  Notifications.setNotificationHandler({
    handleNotification: async () => ({
      shouldShowAlert: true,
      shouldPlaySound: false,
      shouldSetBadge: false,
    }),
  });

  useEffect(() => {
    responseListener.current =
      Notifications.addNotificationResponseReceivedListener(response => {
        notificationNavigationHandler(
          response.notification.request.content,
          nav,
        );
      });

    return () => {
      if (responseListener.current) {
        Notifications.removeNotificationSubscription(responseListener.current);
      }
    };
  }, [nav]);

  return children;
};

export default NotificationHandler;
