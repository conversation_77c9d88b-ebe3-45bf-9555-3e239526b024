import React, { ReactElement, useState } from 'react';
import {
  Button,
  StyleService,
  useStyleSheet,
  Text,
  CheckBox,
} from '@ui-kitten/components';
import { useMutation, useQueryClient } from 'react-query';
import { StackNavigationProp } from '@react-navigation/stack';
import { Modal, View } from 'react-native';
import { useToast } from 'react-native-toast-notifications';
import useTasks from '~api/useTasks';
import SpinnerButton from './SpinnerButton';
import { MyInvoicesParamList } from '~types';

type Props = {
  navigation: StackNavigationProp<MyInvoicesParamList>;
  id: number;
};

const RejectTask = ({ navigation, id }: Props): ReactElement => {
  const toast = useToast();
  const styles = useStyleSheet(themedStyles);

  const [deleteTaskToo, setDeleteTaskToo] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);

  const { rejectTaskAssignment } = useTasks();

  const queryClient = useQueryClient();

  const { mutate: rejectTaskAssignmentMutate, isLoading } = useMutation(
    rejectTaskAssignment,
    {
      onSuccess: async () => {
        await queryClient.invalidateQueries('completed-tasks');
        await queryClient.invalidateQueries('task-assignments');
        await queryClient.invalidateQueries('tasks');
        toast.show('The job has been rejected.');
        navigation.goBack();
      },
    },
  );

  const handleRejectTaskAssignment = () => {
    rejectTaskAssignmentMutate({
      id,
      deleteTaskToo,
    });
  };

  const modal = (
    <Modal animationType="slide" transparent visible={modalVisible}>
      <View style={styles.centeredView}>
        <View style={styles.modalView}>
          <Text style={styles.modalText}>
            Are you sure you want to reject this Job?
          </Text>
          <CheckBox
            checked={deleteTaskToo}
            onChange={nextChecked => setDeleteTaskToo(nextChecked)}
          >
            Delete original job
          </CheckBox>
          <View style={styles.buttonContainer}>
            <Button
              onPress={() => {
                setModalVisible(false);
              }}
            >
              Cancel
            </Button>
            <SpinnerButton
              text="Reject"
              onPress={handleRejectTaskAssignment}
              isLoading={isLoading}
              size="medium"
            />
          </View>
        </View>
      </View>
    </Modal>
  );

  return (
    <>
      <Button
        style={styles.button}
        onPress={() => setModalVisible(true)}
        appearance="outline"
      >
        Reject
      </Button>
      {modal}
    </>
  );
};

const themedStyles = StyleService.create({
  button: {
    marginTop: 16,
    width: '100%',
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 22,
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 35,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  buttonContainer: {
    width: 200,
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 15,
    justifyContent: 'space-between',
  },
  modalText: {
    marginBottom: 15,
    textAlign: 'center',
  },
});

export default RejectTask;
