import React, { ReactElement, ReactNode } from 'react';
import { StyleService, useStyleSheet, Text } from '@ui-kitten/components';
import { StyleProp, TextStyle, View } from 'react-native';
import ThemedIcon from './ThemedIcon';
import { carrotColor } from '~constants/Colors';

type Props = {
  text: ReactNode;
  iconName: string;
  textStyle?: StyleProp<TextStyle>;
};

const CardLineItem = ({ text, iconName, textStyle }: Props): ReactElement => {
  const styles = useStyleSheet(themedStyles);

  return (
    <View style={styles.container}>
      <ThemedIcon name={iconName} />
      <Text style={[styles.text, textStyle]}>{text as ReactElement}</Text>
    </View>
  );
};

const themedStyles = StyleService.create({
  container: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginVertical: 2,
  },
  text: {
    paddingTop: 2,
    marginLeft: 16,
    fontSize: 12,
    fontWeight: 'bold',
    color: carrotColor,
  },
});

export default CardLineItem;
