import { Button, List } from '@ui-kitten/components';
import * as React from 'react';
import { ListRenderItemInfo, StyleSheet, Text, View } from 'react-native';
import { StackScreenProps } from '@react-navigation/stack';
import { addDays, format } from 'date-fns';
import { useQuery } from 'react-query';
import { ScrollView } from 'react-native-gesture-handler';
import { GroupedTask, Task } from '~types';
import useTasks from '~api/useTasks';
import Spinner from '~components/Spinner';
import { headerTitles } from './InvoicesMenuScreen';
import { carrotColor } from '~constants/Colors';

type Props = StackScreenProps<any>;

const UnpaidInvoices = ({ navigation }: Props): React.ReactElement => {
  const { getUnpaidInvoices } = useTasks();

  const { data, isLoading } = useQuery('unpaid-invoices', getUnpaidInvoices);

  const renderItem = (
    info: ListRenderItemInfo<Task | GroupedTask>,
  ): React.ReactElement => {
    const { item } = info;

    const subjectMonth = new Date(item.subjectMonth);

    return (
      <View style={{ ...styles.header, ...styles.verticalMargin }}>
        <View style={styles.firstCell}>
          <Text>{item.user.name}</Text>
        </View>
        <View style={styles.secondCell}>
          <Text>{format(subjectMonth, 'MMM, yyyy')}</Text>
        </View>
        <View style={styles.thirdCell}>
          <Text
            style={{
              ...styles.rightText,
              ...styles.paddingRight,
            }}
          >
            {item.sum}
          </Text>
        </View>
        <View style={styles.fourthCell}>
          <Text
            style={{ color: carrotColor }}
            onPress={() =>
              navigation.push('InvoicesScreen', {
                headerTitle: headerTitles.technicians,
                userId: item.user.id,
                subjectMonth: addDays(subjectMonth, 10).toDateString(),
              })
            }
          >
            Go to invoice
          </Text>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {isLoading ? (
        <Spinner />
      ) : (
        <ScrollView>
          <View style={styles.header}>
            <View style={styles.firstCell}>
              <Text style={styles.boldText}>Technician</Text>
            </View>
            <View style={styles.secondCell}>
              <Text style={styles.boldText}>Month</Text>
            </View>
            <View style={styles.thirdCell}>
              <Text style={{ ...styles.rightText, ...styles.boldText }}>
                Outstanding
              </Text>
            </View>
            <View style={styles.fourthCell}>
              <Text style={{ ...styles.rightText, ...styles.boldText }} />
            </View>
          </View>
          <List data={data} renderItem={renderItem} />
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: { paddingTop: 16 },
  header: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-start',
    margin: 10,
    marginTop: 0,
    fontWeight: 'bold',
  },
  footer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 10,
    fontWeight: 'bold',
  },
  firstCell: { width: '30%' },
  secondCell: { width: '20%' },
  thirdCell: { width: '25%' },
  fourthCell: { width: '25%', textAlign: 'right' },
  rightText: { textAlign: 'right' },
  paddingRight: { paddingRight: 10 },
  boldText: { fontWeight: 'bold' },
  verticalMargin: { marginVertical: 16 },
});

export default UnpaidInvoices;
