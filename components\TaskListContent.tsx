import React, { ReactElement } from 'react';
import { ListRenderItemInfo } from 'react-native';
import { List, StyleService } from '@ui-kitten/components';
import TaskCard from './TaskCard/TaskCard';
import PlaceholderCard from './PlaceholderCard';
import {
  MyTasksStack,
  AvailableTasksParamList,
  Role,
  Task,
  MoreParamList,
} from '../types';
import Spinner from './Spinner';
import Collapsible from './Collapsible';
import TaskAssignmentCard from './TaskAssignmentCard';
import { StackNavigationProp } from '~node_modules/@react-navigation/stack';

type Props = {
  navigation: StackNavigationProp<
    AvailableTasksParamList | MyTasksStack | MoreParamList
  >;
  tasks: Task[];
  currentUserRole: Role;
  isLoading: boolean;
  compactModeEnabled: boolean;
  onScroll: () => void;
  onEndReached: () => void;
  flatListRef: React.RefObject<List<Task>>;
};

const TasksListContent = ({
  navigation,
  tasks,
  currentUserRole,
  isLoading,
  compactModeEnabled,
  onScroll,
  onEndReached,
  flatListRef,
}: Props): ReactElement => {
  const navigate = (item: Task) =>
    navigation.push('TaskScreen', {
      id: item.id,
      copiedForToday: item.copiedForToday,
    });

  const renderItem = (info: ListRenderItemInfo<Task>): React.ReactElement => {
    const { item } = info;
    const isTaskAssignment = Boolean(item?.task);

    return (
      <Collapsible enabled={compactModeEnabled} title={item.description}>
        {isTaskAssignment ? (
          <TaskAssignmentCard
            taskAssignment={item}
            onPress={() => navigate(item)}
          />
        ) : (
          <TaskCard
            {...item}
            key={item.id}
            repetitionNumber={
              item?.taskAssignment?.repetitionNumber ||
              item.nextRepetitionNumber
            }
            onPress={() => navigate(item)}
          />
        )}
      </Collapsible>
    );
  };

  if (isLoading) {
    return <Spinner />;
  }

  const tasksList = !tasks?.length ? (
    <PlaceholderCard
      text="No Jobs found."
      cta={currentUserRole !== 2 ? 'Add a Job' : undefined}
      onPress={() => navigation.push('AddTaskScreen')}
      icon="list"
    />
  ) : (
    <List
      contentContainerStyle={styles.list}
      data={tasks}
      renderItem={renderItem}
      onScroll={onScroll}
      scrollEventThrottle={16}
      onEndReached={onEndReached}
      onEndReachedThreshold={0}
      ref={flatListRef}
    />
  );

  return tasksList;
};

const styles = StyleService.create({
  addButtonText: { marginLeft: 10 },
  addButton: { flexDirection: 'row', alignItems: 'center' },
  list: {},
  container: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginVertical: 2,
  },
  text: {
    paddingTop: 2,
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default TasksListContent;
