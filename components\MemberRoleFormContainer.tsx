import { Formik, FormikConfig, Formik<PERSON>elpers, FormikValues } from 'formik';
import React, { ReactElement } from 'react';
import KeyboardAvoidingView from './KeyboardAvoidingView';

type Props<T> = {
  initialValues: T;
  onSubmit: (
    values: T,
    formikHelpers: FormikHelpers<T>,
  ) => void | Promise<void>;
  validationSchema: FormikConfig<T>['validationSchema'];
  children: (props: {
    values: T;
    setFieldValue: (
      field: string,
      value: unknown,
      shouldValidate?: boolean,
    ) => void;
    handleSubmit: () => void;
    isValid: boolean;
  }) => JSX.Element;
};

const MemberRoleFormContainer = <T extends FormikValues>({
  initialValues,
  onSubmit,
  validationSchema,
  children,
}: Props<T>): ReactElement => (
  <KeyboardAvoidingView>
    <Formik
      initialValues={initialValues}
      onSubmit={onSubmit}
      enableReinitialize
      validationSchema={validationSchema}
      validateOnMount
    >
      {children}
    </Formik>
  </KeyboardAvoidingView>
);

export default MemberRoleFormContainer;
