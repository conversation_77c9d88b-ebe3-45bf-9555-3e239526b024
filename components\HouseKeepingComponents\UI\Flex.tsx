import React, { ComponentProps } from 'react';
import { View } from 'react-native';
import { StyleService, useStyleSheet } from '@ui-kitten/components';

type Props = ComponentProps<typeof View>;

const Flex = ({ style, ...props }: Props) => {
  const styles = useStyleSheet(themedStyles);

  return <View style={[styles.flex, style]} {...props} />;
};

export default Flex;

const themedStyles = StyleService.create({
  flex: {
    gap: 16,
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
});
