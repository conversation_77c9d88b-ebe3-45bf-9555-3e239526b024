import { ListItem } from '@ui-kitten/components';
import React, { ReactElement } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import useTimezone from '~hooks/useTimezone';
import Currency from '~components/Currency';

type Props = {
  costOfLabor: number;
  costOfMaterials: number;
  cost: string;
  date?: Date;
  description?: string;
  propertyName?: string;
  onPress?: () => void;
};

const InvoiceListRow = ({
  date,
  description,
  costOfLabor,
  costOfMaterials,
  cost,
  propertyName,
  onPress,
}: Props): ReactElement => {
  const { formatInTimeZone } = useTimezone();

  return (
    <ListItem onPress={onPress} style={styles.row}>
      <View style={styles.dateCell}>
        <Text>{formatInTimeZone(date, 'PP')}</Text>
        {description && <Text>{description}</Text>}
        {propertyName && <Text>{propertyName}</Text>}
      </View>
      <View style={styles.costCell}>
        {!!costOfLabor && (
          <Currency value={costOfLabor} style={styles.rightText} />
        )}
      </View>
      <View style={styles.costCell}>
        {!!costOfMaterials && (
          <Currency value={costOfMaterials} style={styles.rightText} />
        )}
      </View>
      <View style={styles.costCell}>
        {!!cost && <Currency value={cost} style={styles.rightText} />}
      </View>
    </ListItem>
  );
};

export default InvoiceListRow;

const styles = StyleSheet.create({
  row: { display: 'flex', flexDirection: 'row', alignItems: 'flex-start' },
  rightText: { textAlign: 'right' },
  dateCell: { width: '40%' },
  costCell: { width: '20%', textAlign: 'right' },
});
