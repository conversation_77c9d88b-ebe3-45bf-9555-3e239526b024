import { QueryClient } from 'react-query';
import { TaskPriorities } from '~dummyData';
import backgroundUpload from '~api/backgroundUpload';
import { BackgroundUploadFileType } from '~types';

export const enum MediaTypes {
  Image = 'image',
  Video = 'video',
  Unknown = 'unknown',
  PDF = 'pdf',
}

export const determineMediaType = (media: string): MediaTypes | null => {
  if (!media) {
    return null;
  }

  const mediaString = media.toString();

  const imageRegex = /^.*\.(jpg|jpeg|png|heic|tiff)$/;

  if (imageRegex.test(mediaString)) {
    return MediaTypes.Image;
  }

  const videoRegex = /^.*\.(mp4|mov)$/;

  if (videoRegex.test(mediaString)) {
    return MediaTypes.Video;
  }

  return MediaTypes.Unknown;
};

export const invalidateQueries = async (
  queryClient: QueryClient,
  queries,
): Promise<void> => {
  const promises = [];
  for (let index = 0; index < queries.length; index += 1) {
    promises.push(
      queryClient.invalidateQueries(queries[index].key, queries[index].options),
    );
  }
  await Promise.all(promises);
};

export const convertDecimalToHoursMinutes = (
  decimalHours: number,
): { hours: number; minutes: number } => {
  const hours: number = Math.floor(decimalHours);
  const minutes: number = Math.round((decimalHours - hours) * 60);

  return { hours, minutes };
};

export const priorityIsUrgentOrAnytime = (priority: TaskPriorities): boolean =>
  [TaskPriorities.Urgent, TaskPriorities.Anytime].includes(priority);

export const buildBackgroundUploadFile = (
  apiUrl: string,
  fileUrl: string,
  authToken: string,
  type: BackgroundUploadFileType,
) => ({
  localPath: fileUrl,
  promise: backgroundUpload(apiUrl, fileUrl, authToken, type),
});
export default null;
