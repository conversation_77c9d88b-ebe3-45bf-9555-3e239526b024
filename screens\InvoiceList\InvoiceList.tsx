import { Button } from '@ui-kitten/components';
import * as React from 'react';
import {
  Linking,
  ListRenderItemInfo,
  Platform,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import { StackNavigationProp, StackScreenProps } from '@react-navigation/stack';
import { useState, useLayoutEffect } from 'react';
import { addDays, startOfMonth } from 'date-fns';
import { useQuery } from 'react-query';
import { useDeepCompareEffect } from 'ahooks';
import * as FileSystem from 'expo-file-system';
import {
  GroupedTaskAssignment,
  MoreParamList,
  Payment,
  TaskAssignment,
} from '~types';
import useTasks, { TaskAssignmentType } from '~api/useTasks';
import useUser from '~api/useUser';
import useProperties from '~queries/useProperties';
import { headerTitles } from '../InvoicesMenuScreen';
import CustomSelect, { Option } from '~components/CustomSelect';
import MonthSelector from '~components/MonthSelector';
import usePayments from '~api/usePayments';
import HeaderButtonAction from '~components/HeaderButton';
import TechnicianInvoiceItem from '~components/Invoice/TechnicianInvoiceItem';
import PropertyInvoiceItem from '~components/Invoice/PropertyInvoiceItem';
import Spinner from '~components/Spinner';
import InvoiceScrollView from '~components/Invoice/InvoiceScrollView';
import { calcCost, generateFilename, onShare } from './invoiceListFunctions';
import { carrotColor } from '~constants/Colors';
import Currency from '~components/Currency';

type Props = StackScreenProps<MoreParamList, 'InvoicesScreen'>;

const InvoiceList = ({
  navigation,
  route: {
    params: { headerTitle, userId, subjectMonth } = {
      headerTitle: '',
      userId: undefined,
      subjectMonth: undefined,
    },
  },
}: Props): React.ReactElement => {
  const download = async () => {
    const selectedMonthIsoString = selectedMonth.toISOString();
    if (Platform.OS === 'web') {
      const url = await getInvoicesPdfUrl(
        selectedMonthIsoString,
        selectedProperty.value,
        true,
      );

      Linking.openURL(url);
      return;
    }

    downloadInvoicesPdf(
      selectedMonthIsoString,
      selectedProperty.value,
      generateFilename(selectedProperty, selectedMonth),
    )
      .then(({ uri }) =>
        navigation.navigate('InvoicePDFScreen', { downloadedPdfUri: uri }),
      )
      .catch();
  };

  const { getUser } = useUser();
  const { getTechnicians } = useTasks();

  const { data: user } = useQuery('user', () => getUser());

  const { profitMargin, isProfitMarginEnabled } = user?.accounts[0] || {};

  const { data: properties } = useProperties();
  const availablePropertySelectItems = [
    { label: 'Select Property', value: null },
    ...(properties?.map(item => ({ label: item.name, value: item.id })) || []),
  ];

  const [selectedProperty, setSelectedProperty] = useState<Option<number>>(
    availablePropertySelectItems[0],
  );

  const lastMonth = addDays(startOfMonth(new Date()), 10);
  const [selectedMonth, setSelectedMonth] = useState(lastMonth);

  const { data: technicians } = useQuery(['technicians', selectedMonth], () =>
    getTechnicians(selectedMonth),
  );

  const [selectedTechnician, setSelectedTechnician] = useState<Option<number>>({
    label: 'Select Technician',
    value: null,
  });

  const { getTaskAssignments, downloadInvoicesPdf, getInvoicesPdfUrl } =
    useTasks();
  const { getPayments } = usePayments();

  const { data: taskAssignments = [], isLoading: taskAssignmentsIsLoading } =
    useQuery(
      [
        'completed-tasks',
        selectedMonth,
        selectedTechnician.value,
        selectedProperty.value,
        headerTitle && selectedTechnician.value === null,
        headerTitle === headerTitles.properties,
      ],
      () =>
        getTaskAssignments(
          TaskAssignmentType.Completed,
          selectedMonth,
          selectedTechnician.value,
          selectedProperty.value,
          Boolean(headerTitle && selectedTechnician.value === null),
          headerTitle === headerTitles.properties,
        ),
    );

  const { data: payments = [], isLoading: paymentsIsLoading } = useQuery(
    ['payments', selectedMonth, selectedTechnician.value],
    () => getPayments(selectedMonth.toISOString(), selectedTechnician.value),
  );

  const monthlyTotals = taskAssignments?.reduce(
    (
      {
        costOfLabor,
        costOfMaterials,
        total,
      }: { costOfLabor: number; costOfMaterials: number; total: number },
      currentTask,
    ) => {
      if (
        headerTitle === headerTitles.properties &&
        isGroupedTaskAssignment(currentTask)
      ) {
        return {
          costOfLabor: costOfLabor + Number(currentTask.costOfLabor),
          costOfMaterials:
            costOfMaterials + Number(currentTask.costOfMaterials),
          total: total + Number(currentTask.sum),
        };
      }

      return {
        costOfLabor: costOfLabor + Number(currentTask.costOfLabor),
        costOfMaterials: costOfMaterials + Number(currentTask.costOfMaterials),
        total: total + calcCost(currentTask),
      };
    },
    {
      costOfLabor: 0,
      costOfMaterials: 0,
      total: 0,
    },
  );

  const monthlyPaymentTotal = payments
    ? -payments.reduce(
        (previousValue: number, currentPayment) =>
          previousValue + Number(currentPayment.amount),
        0,
      )
    : 0;

  useLayoutEffect(() => {
    if (headerTitle) {
      navigation.setOptions({ headerTitle });
    }

    if (headerTitle === headerTitles.technicians) {
      navigation.setOptions({
        headerRight: headerRightTechnicians(
          navigation,
          selectedTechnician,
          selectedMonth,
        ),
      });
    }

    if (headerTitle === headerTitles.properties && Platform.OS !== 'web') {
      navigation.setOptions({
        headerRight: headerRightProperties(
          downloadInvoicesPdf,
          selectedMonth,
          selectedProperty,
        ),
      });
    }
  }, [
    downloadInvoicesPdf,
    headerTitle,
    navigation,
    selectedMonth,
    selectedProperty,
    selectedTechnician,
  ]);

  const availableTechnicianSelectItems = [
    { label: 'Select Technician', value: null },
    ...(technicians?.map(({ name, id, balance }) => {
      const textStyle = balance && balance > 0 ? { color: carrotColor } : {};

      return {
        label: name,
        value: id,
        style: textStyle,
      };
    }) || []),
  ];

  useDeepCompareEffect(() => {
    if (!headerTitle) {
      const technician = availableTechnicianSelectItems.find(
        item => item.value === user?.id,
      )!;

      if (technician) {
        setSelectedTechnician(technician);
      }
    }

    if (userId && subjectMonth) {
      const technician = availableTechnicianSelectItems?.find(
        item => item.value === userId,
      );

      if (technician) {
        setSelectedTechnician(technician);
      }

      setSelectedMonth(new Date(subjectMonth));
    }
  }, [availableTechnicianSelectItems]);

  const renderListItem = (taskAssignment: TaskAssignment): React.ReactElement =>
    headerTitle === headerTitles.properties ? (
      <PropertyInvoiceItem
        date={taskAssignment.finishedAt}
        description={taskAssignment.task.description}
        cost={calcCost(taskAssignment).toFixed(2)}
        propertyName={
          headerTitle === headerTitles.technicians
            ? taskAssignment.task.property.name
            : undefined
        }
        onPress={() => {
          navigation.push('TaskAssignmentScreen', { id: taskAssignment.id });
        }}
      />
    ) : (
      <TechnicianInvoiceItem
        date={taskAssignment.finishedAt}
        description={taskAssignment.task.description}
        costOfLabor={taskAssignment.costOfLabor}
        costOfMaterials={taskAssignment.costOfMaterials}
        cost={calcCost(taskAssignment).toFixed(2)}
        propertyName={
          headerTitle === headerTitles.technicians
            ? taskAssignment.task.property.name
            : undefined
        }
        onPress={() => {
          navigation.push('TaskAssignmentScreen', { id: taskAssignment.id });
        }}
      />
    );

  const renderItem = (
    info: ListRenderItemInfo<TaskAssignment | GroupedTaskAssignment>,
  ): React.ReactElement => {
    const { item } = info;

    if (isGroupedTaskAssignment(item)) {
      return (
        <>
          <View style={[styles.row]}>
            <View style={styles.listHeader}>
              <Text style={styles.boldText}>{item.name}</Text>
            </View>
            <View style={styles.listHeader}>
              <Currency
                value={item.sum}
                style={[styles.rightText, styles.boldText]}
              />
            </View>
          </View>
          {item.tasks.map(task => renderListItem(task))}
        </>
      );
    }
    return renderListItem(item);
  };

  const paymentRenderItem = (
    info: ListRenderItemInfo<Payment>,
  ): React.ReactElement => {
    const { item } = info;

    const { date, description, amount } = item;

    return (
      <TechnicianInvoiceItem
        date={date}
        description={description}
        cost={`-${amount}`}
        onPress={() => {
          navigation.navigate('PaymentDetailsScreen', {
            paymentId: item.id,
          });
        }}
      />
    );
  };

  return (
    <View style={styles.container}>
      {headerTitle === headerTitles.technicians && (
        <CustomSelect
          options={availableTechnicianSelectItems}
          selectedItem={selectedTechnician}
          setSelectedItem={setSelectedTechnician}
        />
      )}
      {headerTitle === headerTitles.properties && (
        <CustomSelect
          options={availablePropertySelectItems}
          selectedItem={selectedProperty}
          setSelectedItem={setSelectedProperty}
        />
      )}
      <MonthSelector
        selectedMonth={selectedMonth}
        firstMonth={subjectMonth}
        lastMonth={lastMonth}
        setSelectedMonth={setSelectedMonth}
      />
      {taskAssignmentsIsLoading || paymentsIsLoading ? (
        <Spinner />
      ) : (
        <InvoiceScrollView
          taskAssignments={taskAssignments}
          payments={payments}
          headerTitle={headerTitle}
          headerTitles={headerTitles}
          renderItem={renderItem}
          monthlyTotals={monthlyTotals}
          paymentRenderItem={paymentRenderItem}
          monthlyPaymentTotal={monthlyPaymentTotal}
          selectedTechnician={selectedTechnician}
          navigation={navigation}
          selectedMonth={selectedMonth}
          isProfitMarginEnabled={isProfitMarginEnabled}
          profitMargin={profitMargin}
        />
      )}
      {headerTitle === headerTitles.properties && (
        <View style={styles.footer}>
          <Button appearance="outline" size="giant" onPress={download}>
            Download in PDF
          </Button>
        </View>
      )}
    </View>
  );
};

const headerRightProperties =
  (
    downloadInvoicesPdf: (
      selectedMonth: string,
      propertyId?: number,
      filename?: string,
    ) => Promise<FileSystem.FileSystemDownloadResult>,
    selectedMonth: Date,
    selectedProperty: Option<number>,
  ) =>
  () =>
    (
      <HeaderButtonAction
        onPress={() =>
          onShare(downloadInvoicesPdf, selectedMonth, selectedProperty)
        }
        iconName="ios-share"
        iconPack="material"
      />
    );

const headerRightTechnicians =
  (
    navigation: StackNavigationProp<MoreParamList, 'InvoicesScreen', undefined>,
    selectedTechnician: Option<number>,
    selectedMonth: Date,
  ) =>
  () =>
    (
      <HeaderButtonAction
        onPress={() =>
          navigation.push('LogPaymentScreen', {
            userId: selectedTechnician.value,
            selectedMonth: selectedMonth.toISOString(),
          })
        }
        iconName="plus"
        buttonText="Log Payment"
      />
    );

const isGroupedTaskAssignment = (
  item: TaskAssignment | GroupedTaskAssignment,
): item is GroupedTaskAssignment =>
  (item as GroupedTaskAssignment).name !== undefined;

const styles = StyleSheet.create({
  row: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  rightText: { textAlign: 'right' },
  boldText: { fontWeight: 'bold' },
  container: {
    justifyContent: 'flex-start',
    flex: 1,
  },
  listHeader: { padding: 10, width: '50%' },
  footer: { margin: 20 },
  selectListItemStyle: {
    fontWeight: '600',
    paddingHorizontal: 8,
  },
});

export default InvoiceList;
