import React from 'react';
import { Icon, useTheme } from '@ui-kitten/components';
import { EvaStatus } from '~node_modules/@ui-kitten/components/devsupport';

type Props = {
  name: string;
  status?: EvaStatus;
  size?: number;
};

const ThemedIcon = ({ name, status = 'primary', size = 18 }: Props) => {
  const theme = useTheme();
  const tintColor = theme[`color-${status}-500`];

  return (
    <Icon
      name={name}
      style={{
        width: size,
        height: size,
        tintColor,
      }}
    />
  );
};

export default ThemedIcon;
