import React, { useState } from 'react';
import { View, Pressable } from 'react-native';
import {
  Button,
  Input,
  Layout,
  StyleService,
  useStyleSheet,
  Icon,
  Text,
  IconProps,
} from '@ui-kitten/components';
import { Formik } from 'formik';
import * as Yup from 'yup';
import { useMutation } from 'react-query';
import { useToast } from 'react-native-toast-notifications';
import { StackScreenProps } from '@react-navigation/stack';
import KeyboardAvoidingView from '~components/KeyboardAvoidingView';
import { EmailIcon } from '~components/Icon';
import SpinnerButton from '~components/SpinnerButton';
import InputValidationWrapper from '~components/InputValidationWrapper';
import ValidationMessages from '~constants/ValidationMessages';
import useClient from '~api';
import { RootStackParamList } from '~types';

const schema = Yup.object().shape({
  email: Yup.string()
    .email(ValidationMessages.emailMustBeValid)
    .required(ValidationMessages.required),
  password: Yup.string()
    .min(6, ValidationMessages.passwordMustBe6Characters)
    .required(ValidationMessages.required),
});

const appName = process.env.APP_NAME;

type Props = StackScreenProps<RootStackParamList, 'ResetPassword'>;

const ResetPassword = ({ navigation }: Props) => {
  const [passwordVisible, setPasswordVisible] = useState(false);
  const styles = useStyleSheet(themedStyles);
  const { resetPassword } = useClient();
  const { mutate, isLoading } = useMutation(resetPassword);
  const toast = useToast();

  const handleResetPassword = (values: {
    email: string;
    password: string;
    resetToken: string;
  }) => {
    mutate(values, {
      onSuccess: async () => {
        toast.show(
          'Password has been updated. Please sign in with your new password.',
        );
        navigation.navigate('SignIn');
      },
    });
  };

  const onSignInButtonPress = (): void => {
    navigation.navigate('SignIn');
  };

  const onPasswordIconPress = (): void => {
    setPasswordVisible(!passwordVisible);
  };

  const renderPasswordIcon = (props: IconProps) => (
    <Pressable onPress={onPasswordIconPress}>
      <Icon {...props} name={passwordVisible ? 'eye-off' : 'eye'} />
    </Pressable>
  );

  const [isSubmitButtonPressed, setIsSubmitButtonPressed] = useState(false);

  const urlSearchString = window.location.search;
  const params = new URLSearchParams(urlSearchString);

  return (
    <Formik
      initialValues={{
        email: params.get('email')!,
        password: '',
        resetToken: params.get('reset_token')!,
      }}
      onSubmit={handleResetPassword}
      validationSchema={schema}
      validateOnChange={isSubmitButtonPressed}
      validateOnBlur={false}
    >
      {({
        handleSubmit,
        values,
        setFieldValue,
        errors: { password: passwordError },
        errors,
      }) => (
        <KeyboardAvoidingView style={styles.container}>
          <View style={styles.headerContainer}>
            <Text category="h1" status="control">
              {appName}
            </Text>
            <Text style={styles.label} category="s1" status="control">
              Reset your password
            </Text>
          </View>
          <Layout style={styles.formContainer} level="1">
            <InputValidationWrapper errorMessage={errors.email}>
              <Input
                style={styles.input}
                accessoryRight={EmailIcon}
                value={values.email}
                disabled
              />
            </InputValidationWrapper>
            <InputValidationWrapper errorMessage={errors.password}>
              <Input
                status={passwordError && 'danger'}
                style={styles.input}
                autoCapitalize="none"
                secureTextEntry={!passwordVisible}
                placeholder="Password"
                accessoryRight={renderPasswordIcon}
                value={values.password}
                onChangeText={value => setFieldValue('password', value)}
              />
            </InputValidationWrapper>
          </Layout>
          <SpinnerButton
            text="Reset Password"
            style={styles.resetPasswordButton}
            onPress={() => {
              handleSubmit();
              setIsSubmitButtonPressed(true);
            }}
            isLoading={isLoading}
          />
          <Button
            style={styles.signInButton}
            appearance="ghost"
            status="basic"
            onPress={onSignInButtonPress}
          >
            Sign In
          </Button>
        </KeyboardAvoidingView>
      )}
    </Formik>
  );
};

const themedStyles = StyleService.create({
  container: {
    backgroundColor: 'background-basic-color-1',
  },
  headerContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 216,
    backgroundColor: 'color-primary-default',
  },
  formContainer: {
    flex: 1,
    paddingTop: 32,
    paddingHorizontal: 16,
  },
  label: {
    marginTop: 16,
  },
  input: {
    marginTop: 16,
  },
  resetPasswordButton: {
    marginTop: 12,
    marginHorizontal: 16,
  },
  signInButton: {
    marginVertical: 12,
    marginHorizontal: 16,
  },
});

export default ResetPassword;
