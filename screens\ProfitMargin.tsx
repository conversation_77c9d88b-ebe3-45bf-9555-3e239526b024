import React from 'react';
import {
  Button,
  Input,
  StyleService,
  Toggle,
  useStyleSheet,
  Text,
  Card,
} from '@ui-kitten/components';
import { Formik } from 'formik';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import * as Yup from 'yup';
import { StackScreenProps } from '@react-navigation/stack';
import { View } from 'react-native';
import _ from 'lodash';
import { useToast } from 'react-native-toast-notifications';
import useUser from '~api/useUser';
import SpinnerButton from '~components/SpinnerButton';
import Spinner from '~components/Spinner';
import { invalidateQueries } from '~helpers';
import { MoreParamList } from '~types';

const schema = Yup.object().shape({
  profitMargin: Yup.number().required('Required'),
});

type Props = StackScreenProps<MoreParamList>;

export default ({ navigation }: Props): React.ReactElement => {
  const toast = useToast();
  const styles = useStyleSheet(themedStyle);

  const { getUser, putAccount } = useUser();
  const queryClient = useQueryClient();
  const { data: user } = useQuery('user', () => getUser());
  const { profitMargin, id, isProfitMarginEnabled } = user?.accounts?.[0] || {};

  const { mutate, isLoading } = useMutation(data => putAccount(id, data), {
    onSuccess: async () => {
      await invalidateQueries(queryClient, [
        { key: 'user', options: {} },
        { key: 'completed-tasks', options: {} },
      ]);
      toast.show(`Mark up has been updated successfully.`);
      navigation.goBack();
    },
  });

  if (!user) {
    return <Spinner />;
  }

  const initialData = {
    profitMargin: profitMargin || 0,
    isProfitMarginEnabled,
  };

  const save = async values => {
    await mutate(
      _.omit(values, !values.isProfitMarginEnabled ? ['profitMargin'] : []),
    );
  };

  return (
    <Formik
      initialValues={initialData}
      onSubmit={save}
      enableReinitialize
      validationSchema={schema}
      validateOnMount
    >
      {({ values, setFieldValue, handleSubmit, isValid }) => (
        <>
          <Card style={styles.card}>
            <View style={styles.container}>
              <Text category="s2" style={styles.marginBottom}>
                Please select a percentage to add a mark up on invoices billed
                to property owners.
              </Text>
              <Toggle
                checked={values.isProfitMarginEnabled}
                onChange={value => {
                  setFieldValue('isProfitMarginEnabled', value);
                }}
              >
                {values.isProfitMarginEnabled ? 'Enabled' : 'Disabled'}
              </Toggle>
            </View>
            {values.isProfitMarginEnabled && (
              <Input
                style={styles.input}
                label="Percentage (%)"
                value={values.profitMargin.toString()}
                onChangeText={value => setFieldValue('profitMargin', value)}
                keyboardType="number-pad"
              />
            )}
          </Card>

          <SpinnerButton
            text="Save"
            style={styles.saveButton}
            onPress={handleSubmit}
            isLoading={isLoading}
            disabled={!isValid}
          />
          <Button
            style={styles.saveButton}
            appearance="outline"
            size="giant"
            onPress={() => navigation.goBack()}
          >
            Cancel
          </Button>
        </>
      )}
    </Formik>
  );
};

const themedStyle = StyleService.create({
  profileSetting: {
    padding: 16,
  },
  saveButton: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  heading: {
    fontSize: 22,
    margin: 16,
  },
  input: {
    marginVertical: 16,
  },
  roleContent: {
    paddingTop: 20,
    flexDirection: 'column',
  },
  roleName: {
    fontWeight: 'bold',
  },
  roleDescription: {
    color: 'gray',
  },
  card: {
    margin: 16,
  },
  container: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  marginBottom: {
    marginTop: 16,
    marginBottom: 32,
  },
});
