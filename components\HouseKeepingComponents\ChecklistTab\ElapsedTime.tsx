import { useEffect, useState } from 'react';
import { intervalToDuration, differenceInMilliseconds } from 'date-fns';

type Props = {
  startTime: string;
};

const ElapsedTime = ({ startTime }: Props) => {
  const [elapsed, setElapsed] = useState('');

  useEffect(() => {
    const start = new Date(`${startTime}Z`);

    const updateElapsedTime = () => {
      const now = new Date();

      const diffMs = differenceInMilliseconds(now, start);
      const totalHours = Math.floor(diffMs / (1000 * 60 * 60));

      const duration = intervalToDuration({
        start,
        end: now,
      });

      const minutes = duration.minutes || 0;
      const seconds = duration.seconds || 0;

      if (totalHours > 0) {
        setElapsed(
          `${totalHours}:${minutes.toString().padStart(2, '0')}:${seconds
            .toString()
            .padStart(2, '0')}`,
        );
      } else {
        setElapsed(`${minutes}:${seconds.toString().padStart(2, '0')}`);
      }
    };

    updateElapsedTime();
    const interval = setInterval(updateElapsedTime, 1000);

    return () => clearInterval(interval);
  }, [startTime]);

  return elapsed;
};

export default ElapsedTime;
