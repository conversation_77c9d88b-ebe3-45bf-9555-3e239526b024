import { Button } from '@ui-kitten/components';
import React, { ReactElement, useState } from 'react';
import { View, StyleSheet, Text, Image, Platform } from 'react-native';
import * as Linking from 'expo-linking';
import { getPathFromState } from '@react-navigation/native';
import { DEEP_LINK_PREFIX } from '~constants/AppstoreUrls';
import logo from '../assets/images/icon.png';

const Banner = ({
  children,
  nav,
}: {
  children: ReactElement;
  nav: any;
}): ReactElement => {
  const handleOpenApp = () => {
    const uri = `${DEEP_LINK_PREFIX}${getPathFromState(
      nav?.current?.getRootState(),
    )}`;

    Linking.canOpenURL(uri).then(supported => {
      if (supported) {
        Linking.openURL(uri);
      }
    });
  };

  return (
    <>
      {Platform.OS === 'web' && (
        <View style={styles.banner}>
          <View style={styles.leftContainer}>
            <Image style={styles.logo} source={logo} />
            <View style={styles.textContainer}>
              <Text style={styles.appTitleText}>Airteam</Text>
              <Text style={styles.appSubtitleText}>
                Open in the Airteam App
              </Text>
            </View>
          </View>
          <View>
            <Button
              onPress={handleOpenApp}
              size="small"
              status="info"
              style={styles.button}
            >
              OPEN
            </Button>
          </View>
        </View>
      )}

      <View style={styles.content}>{children}</View>
    </>
  );
};

export default Banner;

const styles = StyleSheet.create({
  banner: {
    zIndex: 1,
    width: '100%',
    backgroundColor: '#f2f0f9',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  content: {
    flex: 1,
  },
  leftContainer: {
    flexDirection: 'row',
  },
  logo: {
    width: 40,
    height: 40,
    borderRadius: 10,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'grey',
  },
  textContainer: {
    marginLeft: 6,
  },
  appTitleText: {
    fontSize: 16,
    color: '#333',
    fontWeight: '600',
  },
  appSubtitleText: {
    fontSize: 13,
    color: '#878787',
    fontWeight: '600',
  },
  button: {
    borderRadius: 20,
  },
});
