import {
  Input,
  Radio,
  StyleService,
  useStyleSheet,
} from '@ui-kitten/components';
import React, { useState } from 'react';
import { View } from 'react-native';

type Props = {
  name: string;
  value: string;
  setFieldValue: (name: string, value: string | null) => void;
};

const RadioAndNumberInput = ({ name, value, setFieldValue }: Props) => {
  const styles = useStyleSheet(themedStyle);
  const [selectedIndex, setSelectedIndex] = useState(value ? 0 : 1);

  const handleRadioChange = (index: number) => {
    setSelectedIndex(index);

    if (index === 1) {
      setFieldValue(name, null);
    }
  };

  return (
    <View style={styles.container}>
      <Radio
        checked={selectedIndex === 0}
        onChange={() => handleRadioChange(0)}
      />
      <Input
        style={styles.input}
        value={value}
        keyboardType="decimal-pad"
        onChangeText={nextValue => setFieldValue(name, nextValue)}
        disabled={selectedIndex === 1}
      />
      <Radio
        checked={selectedIndex === 1}
        onChange={() => handleRadioChange(1)}
      >
        Unlimited
      </Radio>
    </View>
  );
};

export default RadioAndNumberInput;

const themedStyle = StyleService.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 10,
    width: '90%',
  },
  input: {
    width: 100,
    marginHorizontal: 10,
  },
  label: {
    marginLeft: 8,
  },
});
