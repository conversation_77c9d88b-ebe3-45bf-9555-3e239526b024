import React, { ReactElement, ReactNode } from 'react';
import { SafeAreaView, Modal as NativeModal } from 'react-native';

import { CloseIcon } from '~components/Icon';
import {
  StyleService,
  useStyleSheet,
} from '~node_modules/@ui-kitten/components';
import GalleryControlButton from './GalleryControlButton';
import { View } from './Themed';

type Props = {
  visible: boolean;
  onClose: () => void;
  children: ReactNode;
};

const Modal = ({ visible, onClose, children }: Props): ReactElement => {
  const styles = useStyleSheet(themedStyles) as any;

  return (
    <NativeModal visible={visible}>
      <SafeAreaView style={styles.safeAreaView}>
        <GalleryControlButton Icon={CloseIcon} onPress={onClose} />
      </SafeAreaView>
      <View style={styles.innerView}>{children}</View>
    </NativeModal>
  );
};

const themedStyles = StyleService.create({
  safeAreaView: {
    position: 'absolute',
    zIndex: 1,
    width: '100%',
    alignItems: 'center',
  },
  innerView: {
    backgroundColor: 'black',
    height: '100%',
  },
});

export default Modal;
