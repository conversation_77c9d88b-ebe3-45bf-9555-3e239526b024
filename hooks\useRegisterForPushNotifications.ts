import { Platform } from 'react-native';
import usePushNotifications from '~api/usePushNotifications';
import registerForPushNotificationsAsync from '~services/notification';

export enum Modes {
  Subscribe = 'subscribe',
  Unsubscribe = 'unsubscribe',
}
export default () => {
  const { subscribeForPush, unsubscribeFromPush } = usePushNotifications();

  const registerForPush = async (mode: Modes, JWT?: string) => {
    if (Platform.OS === 'web') {
      return;
    }

    const token = await registerForPushNotificationsAsync();

    if (token) {
      if (mode === Modes.Subscribe) {
        await subscribeForPush(token, JWT);
      }

      if (mode === Modes.Unsubscribe) {
        await unsubscribeFromPush(token, JWT);
      }
    }
  };

  return {
    registerForPush,
  };
};
