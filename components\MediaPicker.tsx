import React, { ReactElement, useState } from 'react';
import {
  View,
  Alert,
  Platform,
  Text,
  ImageBackground,
  TouchableOpacity,
} from 'react-native';
import * as ExpoImagePicker from 'expo-image-picker';
import {
  Button,
  ButtonProps,
  StyleService,
  useStyleSheet,
} from '@ui-kitten/components';
import { ImagePickerAsset } from 'expo-image-picker';
import { VideoThumbnailsResult } from 'expo-video-thumbnails';
import * as DocumentPicker from 'expo-document-picker';
import { DocumentPickerAsset } from 'expo-document-picker';
import getThumbnail from '~services/thumbnail';
import { TrashIcon, PhotoIcon, VideoIcon, FolderIcon } from '~components/Icon';

export enum MediaPickerSources {
  Camera = 'camera',
  Gallery = 'gallery',
  Document = 'document',
}

type NullableMediaAsset = ImagePickerAsset | DocumentPickerAsset | null;

type Props = {
  onChange: (result: {
    media: NullableMediaAsset;
    thumbnail: Partial<VideoThumbnailsResult> | null;
  }) => void;
  trigger?: (
    handleMediaPicked: (
      sourceType: MediaPickerSources,
      mediaTypesToUse: ExpoImagePicker.MediaTypeOptions,
    ) => Promise<void>,
    styles: ReturnType<typeof useStyleSheet>,
    media: NullableMediaAsset,
    thumbnail: Partial<VideoThumbnailsResult> | null,
    label: string | undefined,
    description: string | undefined,
    handleMediaDeleted: () => void,
    initImage: string | undefined,
    mediaTypes: ExpoImagePicker.MediaTypeOptions,
  ) => ReactElement;
  mediaTypes?: ExpoImagePicker.MediaTypeOptions;
  label?: string;
  description?: string;
  defaultValue?: string;
  onCancel?: () => void;
  previewResizeMode?: 'contain' | 'cover';
  height?: number;
};

const MediaPicker = ({
  onChange,
  trigger = defaultTrigger,
  mediaTypes = ExpoImagePicker.MediaTypeOptions.All,
  label,
  description,
  defaultValue,
  onCancel,
  previewResizeMode = 'cover',
  height,
}: Props): ReactElement => {
  const [media, setMedia] = useState<NullableMediaAsset>(null);
  const [thumbnail, setThumbnail] =
    useState<Partial<VideoThumbnailsResult> | null>(null);
  const [initImage, setInitImage] = useState<string | undefined>(defaultValue);

  const styles = useStyleSheet(themedStyles);

  const verifyPermissions = async (): Promise<boolean> => {
    if (Platform.OS !== 'web') {
      const { status: mediaLibPermissionStatus } =
        await ExpoImagePicker.requestMediaLibraryPermissionsAsync();
      const { status: cameraPermissionStatus } =
        await ExpoImagePicker.requestCameraPermissionsAsync();

      if (
        mediaLibPermissionStatus !== 'granted' ||
        cameraPermissionStatus !== 'granted'
      ) {
        Alert.alert(
          'Sorry, we need camera roll permissions to make this work!',
        );
        return false;
      }
    }
    return true;
  };

  const handleMediaDeleted = (): void => {
    setMedia(null);
    setThumbnail(null);
    setInitImage(undefined);
    onChange({ media: null, thumbnail: null });
  };

  const handleMediaPicked = async (
    sourceType: MediaPickerSources,
    mediaTypesToUse: ExpoImagePicker.MediaTypeOptions,
  ): Promise<void> => {
    let pickerResult;
    const hasPermission = await verifyPermissions();
    if (!hasPermission) {
      return;
    }

    if (sourceType === MediaPickerSources.Document) {
      // Handle Document Picker
      pickerResult = await DocumentPicker.getDocumentAsync({
        type: 'application/pdf',
      });

      if (pickerResult.canceled) {
        onCancel?.();
        return;
      }

      const pickedAsset = pickerResult.assets[0];

      setMedia(pickedAsset);
      setThumbnail(null); // No thumbnail for PDFs
      onChange({ media: pickedAsset, thumbnail: null });

      return;
    }

    // Handle Image or Video Picker
    pickerResult = await (sourceType === MediaPickerSources.Camera
      ? ExpoImagePicker.launchCameraAsync({ mediaTypes: mediaTypesToUse })
      : ExpoImagePicker.launchImageLibraryAsync({
          mediaTypes: mediaTypesToUse,
        }));

    if (pickerResult.canceled) {
      onCancel?.();
      return;
    }

    if (!pickerResult.assets || pickerResult.assets.length === 0) return;

    const pickedAsset = pickerResult.assets[0];
    let pickedThumbnail: Partial<VideoThumbnailsResult> | null = null;

    if (
      pickedAsset.type === 'video' ||
      pickedAsset.uri.startsWith('data:video')
    ) {
      pickedThumbnail = await getThumbnail(pickedAsset);
    } else {
      pickedThumbnail = { uri: pickedAsset.uri };
    }

    setMedia(pickedAsset);
    setThumbnail(pickedThumbnail);
    onChange({ media: pickedAsset, thumbnail: pickedThumbnail });
  };

  return trigger(
    handleMediaPicked,
    styles,
    media,
    thumbnail,
    label,
    description,
    handleMediaDeleted,
    initImage,
    mediaTypes,
    previewResizeMode,
    height,
  );
};

const defaultTrigger = (
  handleMediaPicked: (
    sourceType: MediaPickerSources,
    mediaTypesToUse: ExpoImagePicker.MediaTypeOptions,
  ) => Promise<void>,
  styles: ReturnType<typeof useStyleSheet>,
  media: NullableMediaAsset,
  thumbnail: Partial<VideoThumbnailsResult> | null,
  label: string | undefined,
  description: string | undefined,
  handleMediaDeleted: () => void,
  initImage: string | undefined,
  mediaTypes: ExpoImagePicker.MediaTypeOptions,
  previewResizeMode: 'contain' | 'cover' = 'cover',
  height = 320,
) => (
  <View style={[styles.picker, { height }]}>
    {media || initImage ? (
      <ImageBackground
        source={{
          uri: initImage || thumbnail?.uri,
        }}
        resizeMode={previewResizeMode}
        style={styles.image}
      />
    ) : (
      <>
        <Text style={styles.label}>{label}</Text>
        {description && <Text style={styles.smallText}>{description}</Text>}
        <View style={styles.buttonContainer}>
          <PickerButtons
            handleMediaPicked={handleMediaPicked}
            styles={styles}
            mediaTypes={mediaTypes}
          />
          <Button
            accessoryLeft={<FolderIcon marginRight={0} />}
            appearance="ghost"
            size="medium"
            style={styles.button}
            onPress={() =>
              handleMediaPicked(MediaPickerSources.Gallery, mediaTypes)
            }
          >
            Select from device
          </Button>
        </View>
      </>
    )}
    {(initImage || thumbnail?.uri) && (
      <View style={styles.iconContainer}>
        <TouchableOpacity onPress={handleMediaDeleted}>
          <DeleteIcon />
        </TouchableOpacity>
      </View>
    )}
  </View>
);

const PickerButtons = ({
  handleMediaPicked,
  styles,
  mediaTypes,
}: {
  handleMediaPicked: (
    sourceType: MediaPickerSources,
    mediaTypesToUse: ExpoImagePicker.MediaTypeOptions,
  ) => Promise<void>;
  styles: ReturnType<typeof useStyleSheet>;
  mediaTypes: ExpoImagePicker.MediaTypeOptions;
}) => {
  const allowPhoto = [
    ExpoImagePicker.MediaTypeOptions.All,
    ExpoImagePicker.MediaTypeOptions.Images,
  ].includes(mediaTypes);

  const allowVideo = [
    ExpoImagePicker.MediaTypeOptions.All,
    ExpoImagePicker.MediaTypeOptions.Videos,
  ].includes(mediaTypes);

  const buttons = [];

  if (allowPhoto) {
    buttons.push(
      <PhotoButton
        key="photo"
        onPress={() =>
          handleMediaPicked(
            MediaPickerSources.Camera,
            ExpoImagePicker.MediaTypeOptions.Images,
          )
        }
        style={styles.button}
      />,
    );
  }

  if (allowVideo) {
    buttons.push(
      <VideoButton
        key="video"
        onPress={() =>
          handleMediaPicked(
            MediaPickerSources.Camera,
            ExpoImagePicker.MediaTypeOptions.Videos,
          )
        }
        style={styles.button}
      />,
    );
  }

  return <View style={styles.buttonGroup}>{buttons}</View>;
};

const PhotoButton = ({ onPress, style }: ButtonProps) => (
  <Button
    accessoryLeft={<PhotoIcon marginRight={0} />}
    style={style}
    onPress={onPress}
    size="medim"
  >
    Take photo
  </Button>
);

const VideoButton = ({ onPress, style }: ButtonProps) => (
  <Button
    accessoryLeft={<VideoIcon marginRight={0} />}
    style={style}
    onPress={onPress}
    size="medium"
  >
    Record video
  </Button>
);

const DeleteIcon = () => {
  const styles = useStyleSheet(themedStyles);
  return <TrashIcon style={styles.icon} fill="gray" />;
};

const themedStyles = StyleService.create({
  picker: {
    marginHorizontal: 12,
    borderWidth: 1,
    borderRadius: 5,
    borderColor: 'border-basic-color-4',
    backgroundColor: 'background-basic-color-2',
    overflow: 'hidden',
    alignContent: 'center',
    justifyContent: 'center',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  iconContainer: {
    position: 'absolute',
    bottom: 10,
    right: 10,
    width: 50,
    height: 50,
    backgroundColor: '#eee',
    borderRadius: 15,
  },
  icon: {
    width: 30,
    height: 30,
    margin: 10,
  },
  label: {
    fontSize: 20,
    textAlign: 'center',
    paddingHorizontal: 32,
    marginTop: 32,
  },
  smallText: {
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 32,
    marginTop: 0,
  },
  buttonContainer: {
    flexDirection: 'column',
    justifyContent: 'space-around',
    alignItems: 'center',
    marginTop: 16,
  },
  buttonGroup: {
    marginTop: 16,
    gap: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  button: {
    height: 90,
  },
});

export default MediaPicker;
