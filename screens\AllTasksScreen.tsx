import {
  IndexPath,
  Input,
  Select,
  SelectItem,
  Text,
} from '@ui-kitten/components';
import React, { ReactElement, useRef, useState } from 'react';
import { StyleSheet } from 'react-native';
import { StackScreenProps } from '@react-navigation/stack';
import { useInfiniteQuery, useQuery } from 'react-query';
import { useDebounce, useDeepCompareEffect } from 'ahooks';
import useTasks from '~api/useTasks';
import useUser from '~api/useUser';
import TasksListContent from '~components/TaskListContent';
import TasksListHeaderRight from '~components/TasksListHeaderRight';
import DynamicHeader from '~components/DynamicFilters';
import { Task } from '~types';
import { View } from '~components/Themed';
import Spinner from '~components/Spinner';

type Props = StackScreenProps;

const AllTasksScreen = ({ navigation }: Props): ReactElement => {
  const { getAllTasks } = useTasks();
  const { getUser } = useUser();
  const { data: user } = useQuery('user', () => getUser());

  const currentUserRole = user?.accounts[0].pivot.role;

  const [propertyIdToFilter, setPropertyIdToFilter] = useState(null);
  const [userIdToFilter, setUserIdToFilter] = useState(null);
  const [statusNameToFilter, setStatusNameToFilter] = useState('available');
  const [dateToFilter, setDateToFilter] = useState(4);
  const [recurringFilter, setRecurringFilter] = useState('all');
  const [textSearch, setTextSearch] = useState('');
  const [compactModeEnabled, setCompactModeEnabled] = useState(false);
  const [blockedFilter, setBlockedFilter] = useState('all');

  const debouncedTextSearchValue = useDebounce(textSearch, { wait: 1000 });

  const [scrollDirection, setScrollDirection] = useState<'up' | 'down'>('up');
  const [inputMode, setInputMode] = useState('none');

  const searchInputRef = useRef(null);

  const flatListRef = useRef(null);

  useDeepCompareEffect(() => {
    flatListRef.current?.scrollToOffset({ animated: true, offset: 0 });
  }, [
    propertyIdToFilter,
    userIdToFilter,
    statusNameToFilter,
    dateToFilter,
    recurringFilter,
    debouncedTextSearchValue,
    blockedFilter,
  ]);

  const {
    data,
    isLoading: tasksAreLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useInfiniteQuery(
    [
      'tasks',
      'all',
      propertyIdToFilter,
      userIdToFilter,
      statusNameToFilter,
      dateToFilter,
      recurringFilter,
      debouncedTextSearchValue,
      blockedFilter,
    ],
    url =>
      getAllTasks(
        propertyIdToFilter,
        userIdToFilter,
        statusNameToFilter,
        dateToFilter,
        recurringFilter,
        debouncedTextSearchValue,
        blockedFilter,
        url.pageParam,
      ),
    {
      keepPreviousData: true,
      getNextPageParam: lastPage => {
        if (lastPage.nextPageUrl !== null) {
          return lastPage.currentPage + 1;
        }

        return null;
      },
    },
  );

  const loadMore = () => {
    if (hasNextPage) {
      fetchNextPage();
    }
  };
  const { properties, recurringFilters, statuses, users, dates, showBlocked } =
    data?.pages[0]?.data || {};

  const tasks: Task[] = data?.pages?.map(page => page.data.tasks).flat();

  if (
    !properties ||
    !recurringFilters ||
    !statuses ||
    !users ||
    !dates ||
    !showBlocked
  ) {
    return null;
  }

  const availablePropertySelectItems = [
    { id: null, name: 'Select Property' },
    ...(properties || []),
  ].map(property => ({ ...property, isDeleted: Boolean(property.deletedAt) }));

  const availableDatesToFilter = dates;

  const recurringFilterOptions = [
    { id: 'all', name: 'Select Task Type' },
    ...(recurringFilters || []),
  ];

  const blockedFilterOptions = [
    { id: 'all', name: 'Select Blocked Status' },
    ...(showBlocked || []),
  ];

  let availableTechnicianSelectItems = [];
  let availableStatusSelectItems = [];

  availableTechnicianSelectItems = [
    { id: null, name: 'Select Technician' },
    ...(users || []),
  ];

  const formattedStatuses = statuses.map(item => ({
    name: `${item.name} Jobs`,
    id: item.name,
    count: item.count,
  }));

  availableStatusSelectItems = [
    { id: 'all', name: 'Select Job Status' },
    ...(formattedStatuses || []),
  ];

  const availableAndAllTasksTemplate = (
    <View style={{ flexDirection: 'column', height: '100%' }}>
      <TasksListHeaderRight
        navigation={navigation}
        currentUserRole={currentUserRole}
        compactModeEnabled={compactModeEnabled}
        setCompactModeEnabled={setCompactModeEnabled}
      />
      <View style={styles.container} level="1">
        <DynamicHeader scrollDirection={scrollDirection}>
          <Input
            style={styles.select}
            value={textSearch}
            onChangeText={value => setTextSearch(value)}
            placeholder="Search"
            onFocus={() => setScrollDirection('up')}
            inputMode={inputMode}
            onPressIn={() => setInputMode('text')}
            ref={searchInputRef}
          />
          <FilterSelect
            availableSelectItems={availablePropertySelectItems || []}
            propertyNameToVisualize="name"
            countName="taskCount"
            keyToSet="id"
            initialValue={propertyIdToFilter}
            setValue={setPropertyIdToFilter}
            onFocus={() => setInputMode('none')}
          />
          <FilterSelect
            availableSelectItems={availableTechnicianSelectItems}
            propertyNameToVisualize="name"
            countName="taskCount"
            keyToSet="id"
            initialValue={userIdToFilter}
            setValue={setUserIdToFilter}
            onFocus={() => setInputMode('none')}
          />
          <FilterSelect
            availableSelectItems={availableStatusSelectItems}
            propertyNameToVisualize="name"
            countName="count"
            keyToSet="id"
            initialValue={statusNameToFilter}
            setValue={setStatusNameToFilter}
            onFocus={() => setInputMode('none')}
          />
          <FilterSelect
            availableSelectItems={availableDatesToFilter}
            propertyNameToVisualize="name"
            countName="count"
            keyToSet="id"
            initialValue={dateToFilter}
            setValue={setDateToFilter}
            onFocus={() => setInputMode('none')}
          />
          <FilterSelect
            availableSelectItems={recurringFilterOptions}
            propertyNameToVisualize="name"
            countName="count"
            keyToSet="id"
            initialValue={recurringFilter}
            setValue={setRecurringFilter}
            onFocus={() => setInputMode('none')}
          />
          <FilterSelect
            availableSelectItems={blockedFilterOptions}
            propertyNameToVisualize="name"
            countName="count"
            keyToSet="id"
            initialValue={blockedFilter}
            setValue={setBlockedFilter}
            onFocus={() => setInputMode('none')}
          />
        </DynamicHeader>
      </View>
      <View
        style={{
          flexGrow: 1,
          position: 'relative',
          backgroundColor: 'red',
          height: 10,
        }}
        onTouchStart={() => {
          setScrollDirection('down');
          searchInputRef.current?.blur();
        }}
      >
        {isFetchingNextPage && (
          <View
            style={{
              position: 'absolute',
              width: '100%',
              height: 50,
              zIndex: 999,
              backgroundColor: 'rgba(255, 255, 255, 0.5)',
              bottom: 0,
            }}
          >
            <Spinner />
          </View>
        )}
        <TasksListContent
          navigation={navigation}
          tasks={tasks}
          currentUserRole={currentUserRole}
          isLoading={tasksAreLoading}
          compactModeEnabled={compactModeEnabled}
          onEndReached={loadMore}
          flatListRef={flatListRef}
        />
      </View>
    </View>
  );

  return (
    <>
      <TasksListHeaderRight
        navigation={navigation}
        currentUserRole={currentUserRole}
        compactModeEnabled={compactModeEnabled}
        setCompactModeEnabled={setCompactModeEnabled}
      />
      {availableAndAllTasksTemplate}
    </>
  );
};

const filterSelectOption = (item, title) => () =>
  (
    <Text
      style={{
        ...styles.selectText,
        textDecorationLine: item.isDeleted ? 'line-through' : 'none',
      }}
    >
      {title}
    </Text>
  );

const capitalize = (string: string) =>
  string.charAt(0).toUpperCase() + string.slice(1);

type FilterSelectProps = {
  availableSelectItems: string[];
  propertyNameToVisualize: string;
  countName: string;
  keyToSet: string;
  initialValue: string | null;
  setValue: () => void;
  postfix?: string | null;
  style?: string | null;
  disabled?: boolean;
  onFocus?: () => void;
};

const FilterSelect = ({
  availableSelectItems,
  propertyNameToVisualize,
  countName,
  keyToSet,
  initialValue,
  setValue,
  postfix,
  style,
  disabled,
  onFocus,
}: FilterSelectProps) => {
  const initialValueIndex = availableSelectItems?.findIndex(
    item => item.id === initialValue,
  );

  const [selectedIndex, setSelectedIndex] = useState(
    initialValueIndex ? new IndexPath(initialValueIndex) : new IndexPath(0),
  );

  const generateItemTitle = (item, titlePostfix) => {
    let title = capitalize(item[propertyNameToVisualize]);

    if (titlePostfix) {
      title += ` ${titlePostfix}`;
    }

    title += item[countName] !== undefined ? ` (${item[countName]})` : '';

    return filterSelectOption(item, title);
  };

  const generateFormattedValue = () => {
    const selectedRow = availableSelectItems?.[selectedIndex.row];

    if (!selectedRow) {
      return '';
    }

    let formattedValue = capitalize(selectedRow?.[propertyNameToVisualize]);

    if (postfix) {
      formattedValue += ` ${postfix}`;
    }

    formattedValue +=
      selectedRow?.[countName] !== undefined
        ? ` (${selectedRow?.[countName]})`
        : '';

    return formattedValue;
  };

  return (
    <Select
      style={{ ...styles.select, ...style }}
      selectedIndex={selectedIndex}
      value={generateFormattedValue()}
      onSelect={index => {
        setValue(availableSelectItems?.[index.row][keyToSet]);
        setSelectedIndex(index);
      }}
      onFocus={onFocus}
      disabled={disabled}
    >
      {availableSelectItems?.map(item => (
        <SelectItem
          key={item[keyToSet]}
          title={generateItemTitle(item, postfix)}
        />
      ))}
    </Select>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingTop: 16,
    paddingHorizontal: 16,
    backgroundColor: 'rgb(247, 249, 252)',
  },
  select: {
    marginBottom: 10,
  },
  propertySelectStyle: {
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 0,
  },
  selectText: {
    fontWeight: '600',
    marginLeft: 10,
  },
});

export default AllTasksScreen;
