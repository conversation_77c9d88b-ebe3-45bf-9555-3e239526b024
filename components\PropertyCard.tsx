import React, { ReactElement } from 'react';
import {
  Button,
  Card,
  Text,
  Layout,
  Divider,
  useStyleSheet,
  StyleService,
} from '@ui-kitten/components';
import { ScrollView, View } from 'react-native';
import { CalendarDate, Property } from '../types';
import { HomeIcon, PinIcon } from '~components/Icon';
import PropertyCalendar from './PropertyCalendar';
import { carrotColor } from '~constants/Colors';

type Props = Partial<Property> & {
  name: string;
  address: string;
  accessInformation?: string;
  notes?: string;
  calendarDates?: CalendarDate[];
  isSuspended?: boolean;
  showDetails?: boolean;
  marginVertical?: number;
  internalNotes?: string;
  onPress?: () => void;
};

const PropertyCard = ({
  name,
  address,
  accessInformation,
  notes,
  calendarDates,
  isSuspended,
  showDetails = false,
  marginVertical = 8,
  internalNotes,
  onPress,
}: Props): ReactElement => {
  const styles = useStyleSheet(themedStyles);

  return (
    <ScrollView>
      <Card style={{ ...styles.item, marginVertical }} onPress={onPress}>
        <View style={styles.itemHeader}>
          <HomeIcon style={styles.headerIcon} />
          <Text category="h4">{name}</Text>
        </View>

        <View style={styles.itemFooter}>
          <Button
            appearance="ghost"
            size="tiny"
            status="danger"
            accessoryLeft={PinIcon}
          >
            {address}
          </Button>
        </View>
      </Card>

      {showDetails && (
        <>
          {isSuspended && (
            <Layout style={styles.contentContainer} level="1">
              <Text style={styles.titleLabel} category="h5">
                Suspended
              </Text>
              <Text>
                While suspended, the Jobs associated with this Property
                won&apos;t appear among available jobs.
              </Text>
            </Layout>
          )}
          <Layout style={styles.contentContainer} level="1">
            <Text style={styles.titleLabel} category="h5">
              Access Information
            </Text>
            <Text>{accessInformation}</Text>
          </Layout>
          <Divider />
          {calendarDates && calendarDates.length > 0 && (
            <>
              <Layout style={styles.contentContainer} level="1">
                <Text style={styles.titleLabel} category="h5">
                  Availability
                </Text>
                <PropertyCalendar
                  calendarDates={calendarDates}
                  highlightColor={carrotColor}
                />
              </Layout>
              <Divider />
            </>
          )}
          <Layout style={styles.contentContainer} level="1">
            <Text style={styles.titleLabel} category="h5">
              Notes
            </Text>
            <Text>{notes}</Text>
          </Layout>
          {internalNotes && (
            <Layout style={styles.contentContainer} level="1">
              <Text style={styles.titleLabel} category="h5">
                Internal notes
              </Text>
              <Text>{internalNotes}</Text>
            </Layout>
          )}
        </>
      )}
    </ScrollView>
  );
};

const themedStyles = StyleService.create({
  item: {
    borderRadius: 0,
  },
  itemHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: 0,
    marginHorizontal: -14,
  },
  itemFooter: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    marginTop: 0,
    marginHorizontal: -18,
  },
  headerIcon: {
    width: 30,
    height: 30,
    tintColor: 'black',
    marginRight: 2,
  },
  titleLabel: {
    marginBottom: 10,
  },
  contentContainer: {
    padding: 16,
  },
});

export default PropertyCard;
