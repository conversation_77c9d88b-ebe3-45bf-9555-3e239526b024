import React from 'react';
import {
  Button,
  Input,
  StyleService,
  useStyleSheet,
} from '@ui-kitten/components';
import { Formik } from 'formik';
import { useQueryClient } from 'react-query';
import { ScrollView } from 'react-native';
import { useToast } from 'react-native-toast-notifications';
import { ProfileSetting } from '~components/ProfileSetting';
import { useMutation, useQuery } from '~node_modules/react-query';
import useUser from '~api/useUser';

export default ({ navigation }): React.ReactElement => {
  const toast = useToast();
  const styles = useStyleSheet(themedStyle);
  const { getUser, updateUser } = useUser();
  const { isLoading, data: user } = useQuery('user', () => getUser());
  const queryClient = useQueryClient();

  const { mutate: updateUserMutate } = useMutation(updateUser, {
    onSuccess: () => {
      queryClient.invalidateQueries('user');
    },
  });

  const save = values => {
    if (values.password.length === 0 || values.confirmation.length === 0) {
      toast.show(
        'You must provide the new password and the password confirmation too',
      );

      return;
    }

    if (values.password !== values.confirmation) {
      toast.show('The new password and the password confirmation must match');

      return;
    }

    if (values.password.length < 6) {
      toast.show('The new password must be at least 6 characters long');

      return;
    }

    updateUserMutate(values);
    navigation.goBack();
  };

  if (isLoading || !user) {
    return null;
  }

  return (
    <Formik initialValues={{ password: '', confirmation: '' }} onSubmit={save}>
      {({ values, setFieldValue, handleSubmit }) => (
        <>
          <ScrollView
            style={styles.container}
            contentContainerStyle={styles.contentContainer}
          >
            <ProfileSetting
              style={[styles.profileSetting]}
              hint="New Password"
              value={
                <Input
                  style={styles.input}
                  value={values.password}
                  placeholder="Password"
                  onChangeText={value => setFieldValue('password', value)}
                  secureTextEntry
                />
              }
            />
            <ProfileSetting
              style={[styles.profileSetting]}
              hint="Confirm Password"
              value={
                <Input
                  style={styles.input}
                  value={values.confirmation}
                  placeholder="Password"
                  onChangeText={value => setFieldValue('confirmation', value)}
                  secureTextEntry
                />
              }
            />
          </ScrollView>
          <Button style={styles.saveButton} size="giant" onPress={handleSubmit}>
            Save New Password
          </Button>
          <Button
            style={styles.saveButton}
            appearance="outline"
            size="giant"
            onPress={() => navigation.goBack()}
          >
            Cancel
          </Button>
        </>
      )}
    </Formik>
  );
};

const themedStyle = StyleService.create({
  container: {
    flex: 1,
    backgroundColor: 'background-basic-color-2',
  },
  contentContainer: {
    paddingVertical: 24,
  },
  profileAvatar: {
    aspectRatio: 1.0,
    height: 124,
    alignSelf: 'center',
  },
  editAvatarButton: {
    aspectRatio: 1.0,
    height: 48,
    borderRadius: 24,
  },
  profileSetting: {
    padding: 16,
  },
  section: {
    marginTop: 24,
  },
  saveButton: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
});
