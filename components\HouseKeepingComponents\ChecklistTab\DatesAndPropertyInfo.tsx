import React from 'react';
import { StyleService, Text, useStyleSheet } from '@ui-kitten/components';
import Stack from '~components/HouseKeepingComponents/UI/Stack';
import { HKExtendedJob } from '~/types';

type Props = {
  job: HKExtendedJob;
};

const DatesAndPropertyInfo = ({ job: { property } }: Props) => {
  const styles = useStyleSheet(themedStyles);
  return (
    <Stack style={styles.container}>
      <Text>
        Property: {property?.name}, {property?.address}
      </Text>
    </Stack>
  );
};

export default DatesAndPropertyInfo;

const themedStyles = StyleService.create({
  container: {
    gap: 4,
  },
});
