import React from 'react';
import { View } from 'react-native';
import { StyleService, Text, useStyleSheet } from '@ui-kitten/components';
import SkillForm from '~components/SkillForm';

const appName = process.env.APP_NAME;

export default (): React.ReactElement => {
  const styles = useStyleSheet(themedStyles);

  return (
    <>
      <View style={styles.headerContainer}>
        <Text category="h1" status="control">
          {appName}
        </Text>
        <Text style={styles.signUpLabel} category="s1" status="control">
          Select your skills
        </Text>
      </View>
      <SkillForm
        description="Please select at least 1 skill from the list below. The app will suggest jobs for you based on this selection."
        navigateTo="Available Jobs"
      />
    </>
  );
};

const themedStyles = StyleService.create({
  headerContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 216,
    backgroundColor: 'color-primary-default',
  },
  signUpLabel: {
    marginTop: 16,
  },
});
