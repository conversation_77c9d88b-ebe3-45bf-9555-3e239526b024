diff --git a/node_modules/@ui-kitten/components/devsupport/components/measure/measure.component.js b/node_modules/@ui-kitten/components/devsupport/components/measure/measure.component.js
index 02180f9..62f06ac 100644
--- a/node_modules/@ui-kitten/components/devsupport/components/measure/measure.component.js
+++ b/node_modules/@ui-kitten/components/devsupport/components/measure/measure.component.js
@@ -1,71 +1,79 @@
 "use strict";
-/**
- * @license
- * Copyright Akveo. All Rights Reserved.
- * Licensed under the MIT License. See License.txt in the project root for license information.
- */
 var __importDefault = (this && this.__importDefault) || function (mod) {
-    return (mod && mod.__esModule) ? mod : { "default": mod };
+  return (mod && mod.__esModule) ? mod : { "default": mod };
 };
 Object.defineProperty(exports, "__esModule", { value: true });
 exports.MeasureElement = void 0;
+
 const react_1 = __importDefault(require("react"));
 const react_native_1 = require("react-native");
 const type_1 = require("./type");
-/**
- * Measures child element size and it's screen position asynchronously.
- * Returns measure result in `onMeasure` callback.
- *
- * Usage:
- *
- * ```tsx
- * const onMeasure = (frame: Frame): void => {
- *   const { x, y } = frame.origin;
- *   const { width, height } = frame.size;
- *   ...
- * };
- *
- * <MeasureElement
- *   shouldUseTopInsets={ModalService.getShouldUseTopInsets}
- *   onMeasure={onMeasure}>
- *   <ElementToMeasure />
- * </MeasureElement>
- * ```
- *
- * By default, it measures each time onLayout is called,
- * but `force` property may be used to measure any time it's needed.
- * DON'T USE THIS FLAG IF THE COMPONENT RENDERS FIRST TIME OR YOU KNOW `onLayout` WILL BE CALLED.
- */
+
 const MeasureElement = (props) => {
-    const ref = react_1.default.useRef();
-    const bindToWindow = (frame, window) => {
-        if (frame.origin.x < window.size.width) {
-            return frame;
-        }
-        const boundFrame = new type_1.Frame(frame.origin.x - window.size.width, frame.origin.y, frame.size.width, frame.size.height);
-        return bindToWindow(boundFrame, window);
-    };
-    const onUIManagerMeasure = (x, y, w, h) => {
-        if (!w && !h) {
-            measureSelf();
-        }
-        else {
-            const originY = props.shouldUseTopInsets ? y + react_native_1.StatusBar.currentHeight || 0 : y;
-            const frame = bindToWindow(new type_1.Frame(x, originY, w, h), type_1.Frame.window());
-            props.onMeasure(frame);
-        }
-    };
-    const measureSelf = () => {
-        const node = (0, react_native_1.findNodeHandle)(ref.current);
-        react_native_1.UIManager.measureInWindow(node, onUIManagerMeasure);
-    };
-    if (props.force) {
-        measureSelf();
+  const ref = react_1.default.useRef(null);
+
+  const bindToWindow = (frame, window) => {
+    if (frame.origin.x < window.size.width) {
+      return frame;
     }
-    return react_1.default.cloneElement(props.children, { ref, onLayout: measureSelf });
+    const boundFrame = new type_1.Frame(
+      frame.origin.x - window.size.width,
+      frame.origin.y,
+      Math.floor(frame.size.width),
+      Math.floor(frame.size.height),
+    );
+    return bindToWindow(boundFrame, window);
+  };
+
+  const onUIManagerMeasure = (x, y, w, h) => {
+    if (!w && !h) {
+      measureSelf();
+    } else {
+      const originY = props.shouldUseTopInsets ? y + react_native_1.StatusBar.currentHeight || 0 : y;
+      const frame = bindToWindow(
+        new type_1.Frame(x, originY, Math.floor(w), Math.floor(h)),
+        type_1.Frame.window(),
+      );
+      props.onMeasure(frame);
+    }
+  };
+
+  const measureSelf = () => {
+    if (react_native_1.Platform.OS === 'web') {
+      const domNode = ref.current;
+      if (domNode instanceof HTMLElement && domNode.getBoundingClientRect) {
+        const rect = domNode.getBoundingClientRect();
+        const frame = new type_1.Frame(
+          rect.x,
+          rect.y,
+          Math.floor(rect.width),
+          Math.floor(rect.height),
+        );
+        const boundFrame = bindToWindow(frame, type_1.Frame.window());
+        props.onMeasure(boundFrame);
+      }
+      return;
+    }
+
+    const node = (0, react_native_1.findNodeHandle)(ref.current);
+    if (node) {
+      react_native_1.UIManager.measureInWindow(node, onUIManagerMeasure);
+    }
+  };
+
+  if (props.force) {
+    measureSelf();
+  }
+
+  return (
+    <react_native_1.View ref={ref} onLayout={measureSelf}>
+      {props.children}
+    </react_native_1.View>
+  );
 };
+
 exports.MeasureElement = MeasureElement;
+
 exports.MeasureElement.defaultProps = {
-    shouldUseTopInsets: false,
+  shouldUseTopInsets: false,
 };
-//# sourceMappingURL=measure.component.js.map
\ No newline at end of file
