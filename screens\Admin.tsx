import React from 'react';
import { View } from 'react-native';
import {
  Menu,
  MenuItem,
  StyleService,
  useStyleSheet,
} from '@ui-kitten/components';
import { StackNavigationProp } from '@react-navigation/stack';
import {
  CalendarIcon,
  ListOutlineIcon,
  PeopleIcon,
  PinIcon,
} from '~components/Icon';
import { MoreParamList } from '~types';

type Props = {
  navigation: StackNavigationProp<MoreParamList, 'AdminScreen'>;
};

const Admin = ({ navigation }: Props): React.ReactElement => {
  const styles = useStyleSheet(themedStyle);

  const menuItems = [
    <MenuItem
      key="team"
      style={styles.menuItem}
      title="Team"
      accessoryLeft={PeopleIcon}
      onPress={() => navigation.navigate('TeamScreen')}
    />,
    <MenuItem
      key="properties"
      style={styles.menuItem}
      title="Properties"
      accessoryLeft={PinIcon}
      onPress={() => navigation.navigate('PropertiesScreen')}
    />,
    <MenuItem
      key="calendar"
      style={styles.menuItem}
      title="Calendar"
      accessoryLeft={CalendarIcon}
      onPress={() => navigation.navigate('CalendarScreen')}
    />,
    <MenuItem
      key="invoices"
      style={styles.menuItem}
      title="Invoices"
      accessoryLeft={ListOutlineIcon}
      onPress={() => navigation.navigate('InvoicesMenuScreen')}
    />,
    <MenuItem
      key="skills"
      style={styles.menuItem}
      title="Skills"
      accessoryLeft={ListOutlineIcon}
      onPress={() => navigation.navigate('AdminSkillsScreen')}
    />,
  ];

  return (
    <View>
      <Menu contentContainerStyle={styles.menu}>{menuItems}</Menu>
    </View>
  );
};

const themedStyle = StyleService.create({
  menu: {
    height: '100%',
  },
  menuItem: {
    flexGrow: 1,
    flexBasis: '20%',
  },
});

export default Admin;
