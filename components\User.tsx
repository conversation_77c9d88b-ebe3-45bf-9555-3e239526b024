import React, { ReactElement } from 'react';
import { Text } from '@ui-kitten/components';
import { StyleProp, TextStyle } from 'react-native';
import { User as UserType } from '~types';

type Props = {
  user: UserType;
  style?: StyleProp<TextStyle>;
};

const User = ({ user, style }: Props): ReactElement => {
  const { name, isDeleted } = user;

  const strikeThroughStyle = {
    textDecorationLine: isDeleted ? 'line-through' : 'none',
  };

  return (
    <Text style={[style, strikeThroughStyle as StyleProp<TextStyle>]}>
      {name}
    </Text>
  );
};

export default User;
