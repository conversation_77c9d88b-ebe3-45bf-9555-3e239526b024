import {
  Icon,
  IconProps,
  Text,
  TopNavigationAction,
} from '@ui-kitten/components';
import * as React from 'react';
import { StyleSheet, View } from 'react-native';
import { StackNavigationProp, StackScreenProps } from '@react-navigation/stack';
import { useLayoutEffect } from 'react';
import { ScrollView } from 'react-native-gesture-handler';
import { MoreParamList, Property } from '~types';
import PropertyCard from '~components/PropertyCard';
import useProperties from '~queries/useProperties';
import Spinner from '~components/Spinner';

type NavigationParam = StackNavigationProp<MoreParamList, 'PropertyScreen'>;

type PropertyListProps = {
  title: string;
  items: Property[];
  navigation: NavigationParam;
};

const PropertyList = ({
  title,
  items,
  navigation,
}: PropertyListProps): React.ReactElement => (
  <View style={styles.list}>
    <View>
      <Text style={styles.heading}>{title}</Text>
    </View>
    {items.map(property => (
      <PropertyCard
        {...property}
        key={property.id}
        onPress={() => navigation.push('PropertyScreen', { id: property.id })}
      />
    ))}
  </View>
);

const AddTaskButton = ({ style }: IconProps) => (
  <View style={styles.addButton}>
    <Icon style={style} name="plus" />
    <Text style={styles.addButtonText}>Add Property</Text>
  </View>
);

const AddPropertyAction = ({ onPress }: { onPress: () => void }) => (
  <TopNavigationAction icon={AddTaskButton} onPress={onPress} />
);

type PropertiesListProps = StackScreenProps<MoreParamList, 'PropertyScreen'>;

const PropertiesList = ({
  navigation,
}: PropertiesListProps): React.ReactElement => {
  const { data: properties, isLoading } = useProperties({ showDeleted: true });

  useLayoutEffect(() => {
    navigation.setOptions({ headerRight: headerRight(navigation) });
  }, [navigation]);

  if (isLoading) {
    return <Spinner />;
  }

  const activeProperties =
    properties?.filter(property => property.deletedAt === null) || [];

  const deletedProperties =
    properties?.filter(property => property.deletedAt !== null) || [];

  return (
    <ScrollView>
      <PropertyList
        title="Active Properties"
        items={activeProperties}
        navigation={navigation}
      />
      <PropertyList
        title="Deleted Properties"
        items={deletedProperties}
        navigation={navigation}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  list: { flex: 1, paddingHorizontal: 16 },
  addButtonText: { marginLeft: 10 }, // todo: these are the same between this and tasks
  addButton: { flexDirection: 'row', alignItems: 'center' }, // todo: same
  heading: {
    fontSize: 20,
    fontWeight: '800',
    marginBottom: 8,
    color: '#333',
    marginTop: 32,
  },
});

const headerRight = (navigation: NavigationParam) => () =>
  <AddPropertyAction onPress={() => navigation.push('PropertyFormScreen')} />;

export default PropertiesList;
