import useClient from '~api';
import useRegisterForPushNotifications, {
  Modes,
} from './useRegisterForPushNotifications';
import useRemoveAuth from './useRemoveAuth';

const useSignOut = () => {
  const { registerForPush } = useRegisterForPushNotifications();
  const { signOut } = useClient();
  const { removeAuth } = useRemoveAuth();

  const handleSignOut = async () => {
    try {
      await registerForPush(Modes.Unsubscribe);
      await signOut();
      // eslint-disable-next-line no-empty
    } catch (e) {}

    removeAuth();
  };

  return { handleSignOut };
};

export default useSignOut;
