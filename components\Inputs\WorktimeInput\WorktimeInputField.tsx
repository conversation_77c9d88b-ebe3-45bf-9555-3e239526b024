import React from 'react';
import { Field, FieldProps } from 'formik';
import { StyleProp, ViewStyle } from 'react-native';
import WorktimeInput from '~components/Inputs/WorktimeInput/WorktimeInput';

type Props = {
  name: string;
  style: StyleProp<ViewStyle>;
};

const WorktimeInputField = ({ name, style }: Props) => (
  <Field name={name}>
    {({ form: { values, setFieldValue } }: FieldProps) => (
      <WorktimeInput
        value={values[name]}
        onChange={value => setFieldValue(name, value)}
        style={style}
      />
    )}
  </Field>
);

export default WorktimeInputField;
