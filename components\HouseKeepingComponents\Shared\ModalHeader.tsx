import React from 'react';
import {
  Button,
  StyleService,
  Text,
  useStyleSheet,
} from '@ui-kitten/components';
import Group from '~components/HouseKeepingComponents/UI/Group';
import { CloseIcon } from '~components/Icon';

type Props = {
  title?: string;
  onClose?: () => void;
};

const ModalHeader = ({ title, onClose }: Props) => {
  const styles = useStyleSheet(themedStyles);

  return (
    <Group style={styles.header}>
      <Text category="s1" style={styles.title}>
        {title}
      </Text>
      {onClose && (
        <Button
          appearance="ghost"
          status="basic"
          onPress={onClose}
          style={styles.closeButton}
          accessoryLeft={CloseIcon}
        />
      )}
    </Group>
  );
};

export default ModalHeader;

const themedStyles = StyleService.create({
  header: {
    justifyContent: 'space-between',
    paddingVertical: 16,
    backgroundColor: 'white',
  },
  title: {
    fontSize: 18,
  },
  closeButton: {
    minWidth: 0,
    minHeight: 0,
    paddingVertical: 0,
    paddingHorizontal: 0,
    width: 28,
    height: 28,
  },
});
