import React, { ReactNode, useCallback, useEffect, useState } from 'react';
import {
  Button,
  StyleService,
  useStyleSheet,
  Text,
} from '@ui-kitten/components';
import { Linking, Image, View } from 'react-native';
import * as Updates from 'expo-updates';
import logo from '~assets/images/icon.png';
import useVersionInfo from '~hooks/useVersionInfo';

type Props = {
  children: ReactNode;
};

const ForceUpdate = ({ children }: Props) => {
  const styles = useStyleSheet(themedStyles);
  const { needsUpdate, appStoreUrl } = useVersionInfo();
  const [expoIsUpdating, setExpoIsUpdating] = useState(false);

  const executeExpoUpdate = useCallback(async () => {
    try {
      const update = await Updates.checkForUpdateAsync();
      if (update.isAvailable) {
        setExpoIsUpdating(true);
        await Updates.fetchUpdateAsync();
        await Updates.reloadAsync();
        setExpoIsUpdating(false);
      }
    } catch (e) {
      setExpoIsUpdating(false);
    }
  }, [setExpoIsUpdating]);

  useEffect(() => {
    executeExpoUpdate().then(() => {
      setExpoIsUpdating(false);
    });
  }, [executeExpoUpdate]);

  if (needsUpdate) {
    return (
      <UpdateScreen>
        <Text category="h3" style={styles.heading}>
          Update Required
        </Text>
        <Text style={styles.description}>
          A new version of Airteam App is available.{'\n'}
          Please update to the latest version.
        </Text>
        <Button onPress={() => Linking.openURL(appStoreUrl)} size="large">
          Update
        </Button>
      </UpdateScreen>
    );
  }

  if (expoIsUpdating) {
    return (
      <View style={styles.container}>
        <Text category="h3" style={styles.heading}>
          Updating
        </Text>
        <Text style={styles.description}>
          A new version of Airteam App is available.{'\n'}
          Please wait while updating.
        </Text>
      </View>
    );
  }

  return children;
};

type UpdateScreenProps = {
  children: ReactNode;
};

const UpdateScreen = ({ children }: UpdateScreenProps) => {
  const styles = useStyleSheet(themedStyles);

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Image style={{ width: 100, height: 60 }} source={logo} />
        <Text category="h5" style={styles.appName}>
          Airteam App
        </Text>
      </View>
      <View style={styles.content}>{children}</View>
    </View>
  );
};

const themedStyles = StyleService.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    gap: 120,
  },
  header: {
    alignItems: 'center',
  },
  appName: {
    color: 'gray',
  },
  content: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    gap: 16,
    marginBottom: 200,
  },
  heading: {
    color: 'grey',
  },
  description: {
    textAlign: 'center',
    fontSize: 16,
    color: 'gray',
  },
});

export default ForceUpdate;
