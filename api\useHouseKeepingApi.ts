import useClient from './useClient';
import {
  HKAddJobResponse,
  HKChecklist,
  HKExtendedJob,
  HKJob,
  HKJobSection,
  HKProperty,
  HKSection,
  HKTask,
} from '~/types';

const useHouseKeepingApi = () => {
  const client = useClient();

  const postWithToken = async (
    url: string,
    data: Record<string, unknown>,
    checklistToken?: string,
  ) => {
    const payload = checklistToken ? { ...data, checklistToken } : data;
    return client.post(url, payload);
  };

  const getWithToken = async (
    url: string,
    checklistToken?: string,
    additionalParams?: Record<string, unknown>,
  ) => {
    const params = checklistToken
      ? { checklistToken, ...additionalParams }
      : additionalParams;
    return client.get(url, { params });
  };

  const addJob = async ({
    job: { id, leaderPerforms, leaderName, helperName, performType },
    propertyId,
    taskAssignmentId,
  }: {
    job: HKJob;
    propertyId: HKProperty['id'];
    taskAssignmentId: number;
  }) => {
    const apiJob = {
      jobId: id,
      leaderPerforms,
      leaderName,
      helperName,
      performType,
      taskAssignmentId,
      propertyId,
    };
    const newJob = await client
      .post('/checklist/jobs', apiJob)
      .then(res => res.data as HKAddJobResponse);
    return newJob;
  };

  const startChecklist = async ({
    jobId,
    checklist,
    checklistToken,
  }: {
    jobId: HKJob['id'];
    checklist: HKChecklist;
    checklistToken?: string;
  }) => {
    const startedJob = await postWithToken(
      `/checklist/jobs/${jobId}/start`,
      { checklist },
      checklistToken,
    ).then(res => res.data as HKExtendedJob);
    return startedJob;
  };

  const completeJobSection = async ({
    jobSection: { id, items, location },
    checklistToken,
  }: {
    jobSection: HKJobSection;
    checklistToken?: string;
  }) => {
    const { coordinates, address, timezone } = location || {};
    const apiJobSection = {
      id,
      items,
      location: location ? { coordinates, address, timezone } : null,
    };

    const result = await postWithToken(
      '/checklist/complete-section',
      apiJobSection,
      checklistToken,
    ).then(res => res.data as HKExtendedJob);

    return result;
  };

  const fetchJob = async (jobId: HKJob['id'], checklistToken?: string) => {
    const job = await getWithToken(
      `/checklist/jobs/${jobId}`,
      checklistToken,
    ).then(res => res.data as HKExtendedJob);
    return job;
  };

  const fetchItems = async (checklistToken?: string) => {
    const items = await getWithToken('/checklist/items', checklistToken).then(
      res => res.data as HKTask[],
    );
    return items;
  };

  const fetchSections = async (checklistToken?: string) => {
    const sections = await getWithToken(
      '/checklist/sections',
      checklistToken,
    ).then(res => res.data as HKSection[]);
    return sections;
  };

  const finishJob = async ({
    jobId,
    checklistToken,
  }: {
    jobId: HKJob['id'];
    checklistToken?: string;
  }) => {
    const result = await postWithToken(
      `/checklist/jobs/${jobId}/finish`,
      {},
      checklistToken,
    ).then(res => res.data as HKExtendedJob);
    return result;
  };

  return {
    addJob,
    startChecklist,
    completeJobSection,
    fetchJob,
    fetchSections,
    fetchItems,
    finishJob,
  };
};

export default useHouseKeepingApi;
