import _ from 'lodash';
import { format } from 'date-fns';
import * as Sharing from 'expo-sharing';
import * as FileSystem from 'expo-file-system';
import { TaskAssignment } from '~types';
import { Option } from '~components/CustomSelect';

export const calcCost = (taskAssignment: TaskAssignment): number =>
  (Number(taskAssignment.costOfLabor) || 0) +
  (Number(taskAssignment.costOfMaterials) || 0);

export const generateFilename = (
  property: Option<number>,
  month: Date,
): string => {
  let message = 'Report of expenses';
  if (property.value) {
    message += ` ${property.label}`;
  }
  message += ` (${format(month, 'LLL, y')})`;

  return _.kebabCase(message);
};

export const onShare = async (
  downloadInvoices: (
    selectedMonth: string,
    propertyId?: number,
    filename?: string,
  ) => Promise<FileSystem.FileSystemDownloadResult>,
  month: Date,
  property: Option<number>,
) => {
  const message = generateFilename(property, month);

  const { uri } = await downloadInvoices(
    month.toISOString(),
    property.value as number,
    message,
  );

  await Sharing.shareAsync(uri, {
    dialogTitle: message,
    UTI: 'com.adobe.pdf',
  });
};

export default null;
