import React, { useState } from 'react';
import { StackScreenProps } from '@react-navigation/stack';
import { StyleSheet, View } from 'react-native';
import { useQuery } from 'react-query';
import { IndexPath, Select, SelectItem } from '@ui-kitten/components';
import useTasks, { TaskAssignmentType } from '~api/useTasks';
import MyTasksListContent from '~components/MyTasksListContent';
import TasksListContent from '~components/TaskListContent';
import useUser from '~api/useUser';
import TabBar from '~components/TabBar';
import TasksListHeaderRight from '~components/TasksListHeaderRight';

type CustomSelectProps = {
  dataset: Array<any>;
  onSelect?: any;
  selected: any;
};

const CustomSelect = ({ dataset, onSelect, selected }: CustomSelectProps) => (
  <Select
    style={styles.filter}
    selectedIndex={
      new IndexPath(dataset.findIndex(x => x.value === selected.value))
    }
    value={dataset.find(x => x.value === selected.value)?.label}
    onSelect={onSelect}
  >
    {dataset.map(({ value, label }) => (
      <SelectItem key={value} title={label} />
    ))}
  </Select>
);

type Props = StackScreenProps<any>;

enum Filters {
  All = 0,
  Completed = 1,
  Notcompleted = 2,
}

const MyTasksList = ({ navigation }: Props): React.ReactElement => {
  const { getTaskAssignments, getTasksCreatedByMe } = useTasks();
  const { getUser } = useUser();
  const { data: user } = useQuery('user', () => getUser());
  const currentUserRole = user?.accounts[0].pivot.role;

  const [selectedTabIndex, setSelectedTabIndex] = useState(0);
  const [compactModeEnabled, setCompactModeEnabled] = useState(false);

  const { data: taskAssignments, isLoading } = useQuery(
    'task-assignments',
    () => getTaskAssignments(TaskAssignmentType.My),
    {
      enabled: selectedTabIndex === 0,
    },
  );

  const { data: tasksCreatedByMe, isLoading: tasksCreatedByMeIsLoading } =
    useQuery('tasks-created-by-me', () => getTasksCreatedByMe(), {
      enabled: selectedTabIndex === 1,
    });

  const counts = {
    all: tasksCreatedByMe?.length,
    completed: tasksCreatedByMe?.filter(item => item.hasFinishedAssignment)
      .length,
    notcompleted: tasksCreatedByMe?.filter(item => !item.hasFinishedAssignment)
      .length,
  };

  const datasetForSelect = [
    { label: `All (${counts.all})`, value: Filters.All },
    { label: `Completed (${counts.completed})`, value: Filters.Completed },
    {
      label: `Not completed (${counts.notcompleted})`,
      value: Filters.Notcompleted,
    },
  ];

  const [selectedFilter, setSelectedFilter] = useState(datasetForSelect[0]);

  let filteredTasksCreatedByMe = tasksCreatedByMe;
  if (selectedFilter.value !== Filters.All) {
    filteredTasksCreatedByMe = filteredTasksCreatedByMe.filter(item =>
      selectedFilter.value === Filters.Completed
        ? item.hasFinishedAssignment
        : !item.hasFinishedAssignment,
    );
  }

  const firstTabContent = (
    <MyTasksListContent
      navigation={navigation}
      taskAssignments={taskAssignments}
      isLoading={isLoading}
      compactModeEnabled={compactModeEnabled}
    />
  );

  const secondTabContent = (
    <View style={styles.taskListContainer}>
      <CustomSelect
        onSelect={selectedValue => {
          setSelectedFilter(datasetForSelect[selectedValue.row]);
        }}
        dataset={datasetForSelect}
        selected={selectedFilter}
      />
      <TasksListContent
        navigation={navigation}
        tasks={filteredTasksCreatedByMe}
        currentUserRole={currentUserRole}
        isLoading={tasksCreatedByMeIsLoading}
        compactModeEnabled={compactModeEnabled}
      />
    </View>
  );

  return (
    <>
      <TasksListHeaderRight
        navigation={navigation}
        currentUserRole={currentUserRole}
        compactModeEnabled={compactModeEnabled}
        setCompactModeEnabled={setCompactModeEnabled}
      />
      <TabBar
        selectedTabIndex={selectedTabIndex}
        setSelectedTabIndex={setSelectedTabIndex}
        firstTabTitle="Accepted by me"
        firstTabContent={firstTabContent}
        secondTabTitle="Created by me"
        secondTabContent={secondTabContent}
      />
    </>
  );
};

const styles = StyleSheet.create({
  taskListContainer: {
    flex: 1,
    marginTop: 10,
  },
  filter: {
    marginHorizontal: 16,
    marginBottom: 8,
  },
});

export default MyTasksList;
