import React from 'react';
import { Text } from '@ui-kitten/components';
import AddTaskAction from '~components/AddTaskAction';
import { View } from '~components/Themed';

export const MyTasksDescription = ({
  onPressAddTask,
}: {
  onPressAddTask: () => void;
}) => (
  <Text style={{ lineHeight: 30 }}>
    <Text>
      {MY_TASKS_DESCRIPTION}
      {'\n\n'}
      Once a Job is completed it is moved to My Invoices.
      {'\n\n'}
      You can create new jobs with the
      <View style={{ marginTop: -5 }}>
        <AddTaskAction onPress={onPressAddTask} />
      </View>
      icon in the top-right corner.
    </Text>
  </Text>
);

export const AVAILABLE_TASKS_DESCRIPTION =
  'Jobs that match your skill set appear here. Look around and explore. Then accept the jobs that you’re available to do on the requested date. Once you accept a job, it disappears from this view and moves to the My Jobs view, so you can focus on and prepare for those jobs. Only accept jobs that you can and will do on the assigned date.';

export const MY_TASKS_DESCRIPTION =
  'Jobs that you accept from the Available Job list are moved here. If you don’t complete them by the deadline they are moved back to the Available Jobs list for anyone else to accept.';

export const SKILLS_DESCRIPTION =
  'When someone creates a job that matches your skill or profession you will have the opportunity to accept that jobs. Only select skills that you are really good at!';

export const TODAY_DESCRIPTION =
  'This job was originally scheduled for the check-out day. However you can complete this job today because the property is unoccupied.';
