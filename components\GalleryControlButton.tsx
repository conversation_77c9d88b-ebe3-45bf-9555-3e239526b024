import { StyleService, useStyleSheet } from '@ui-kitten/components';
import React, { JSXElementConstructor, ReactElement } from 'react';
import { TouchableOpacity, View } from 'react-native';

type ButtonProps = {
  Icon: JSXElementConstructor<any>;
  onPress: () => void;
};

const GalleryControlButton = ({ Icon, onPress }: ButtonProps): ReactElement => {
  const styles = useStyleSheet(themedStyles) as any;

  return (
    <TouchableOpacity onPress={onPress} style={styles.button}>
      <View style={styles.iconWrapper}>
        <Icon fill="white" />
      </View>
    </TouchableOpacity>
  );
};

const themedStyles = StyleService.create({
  button: {
    padding: 10,
    width: 60,
    height: 60,
    zIndex: 1,
  },
  iconWrapper: {
    backgroundColor: 'gray',
    borderRadius: 10,
  },
});

export default GalleryControlButton;
