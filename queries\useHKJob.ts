import { useQuery } from 'react-query';
import useHouseKeepingApi from '~api/useHouseKeepingApi';
import { HKExtendedJob } from '~types';

type Props = {
  jobId: number;
  checklistToken?: string;
  onSuccess?: (data: HKExtendedJob) => void;
};

export default ({ jobId, checklistToken }: Props) => {
  const { fetchJob } = useHouseKeepingApi();
  const {
    data: job,
    isLoading,
    refetch,
  } = useQuery(
    ['job', jobId, checklistToken],
    () => fetchJob(jobId, checklistToken),
    { enabled: !!jobId || !!checklistToken },
  );

  return { job, isLoading, refetch };
};
