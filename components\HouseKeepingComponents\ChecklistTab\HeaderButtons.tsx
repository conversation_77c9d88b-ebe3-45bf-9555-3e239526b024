import React from 'react';
import { StyleService, useStyleSheet } from '@ui-kitten/components';
import Flex from '~components/HouseKeepingComponents/UI/Flex';
import { UnlockIcon, PeopleOutlineIcon, ShareIcon } from '~/components/Icon';
import HeaderButton from '~components/HouseKeepingComponents/ChecklistTab/HeaderButton';

type Props = {
  onClickRoles: (() => void) | null;
  onClickReport: (() => void) | null;
  onClickPropertyAccess: (() => void) | null;
};

const HeaderButtons = ({
  onClickPropertyAccess,
  onClickRoles,
  onClickReport,
}: Props) => {
  const styles = useStyleSheet(themedStyles);

  return (
    <Flex style={styles.container}>
      {onClickPropertyAccess && (
        <HeaderButton
          onClick={onClickPropertyAccess}
          icon={UnlockIcon}
          label="Access"
        />
      )}
      {onClickRoles && (
        <HeaderButton
          onClick={onClickRoles}
          icon={PeopleOutlineIcon}
          label="Roles"
        />
      )}
      {onClickReport && (
        <HeaderButton onClick={onClickReport} icon={ShareIcon} label="Report" />
      )}
    </Flex>
  );
};

export default HeaderButtons;

const themedStyles = StyleService.create({
  container: {
    gap: 10,
  },
});
