import * as React from 'react';
import { Select, SelectItem } from '@ui-kitten/components';
import spacetime from 'spacetime';
import soft from 'timezone-soft';

import type { Props as ReactSelectProps } from 'react-select';

export { allTimezones };

const TimezoneSelect = ({
  value,
  onBlur,
  onSelect,
  labelStyle = 'original',
  timezones,
  ...props
}: Props) => {
  let localTimezones = allTimezones;
  if (timezones) {
    localTimezones = timezones;
  }

  const getOptions = React.useMemo(
    () =>
      Object.entries(localTimezones)
        .reduce<ITimezoneOption[]>((selectOptions, zone) => {
          const now = spacetime.now(zone[0]);
          const tz = now.timezone();
          const tzStrings = soft(zone[0]);

          let label = '';
          const abbr = now.isDST()
            ? tzStrings[0].daylight?.abbr
            : tzStrings[0].standard?.abbr;
          const altName = now.isDST()
            ? tzStrings[0].daylight?.name
            : tzStrings[0].standard?.name;

          const min = tz.current.offset * 60;
          /* eslint no-bitwise: ["error", { "allow": ["^"] }] */
          const hr = `${(min / 60) ^ 0}:${
            min % 60 === 0 ? '00' : Math.abs(min % 60)
          }`;
          const prefix = `(GMT${hr.includes('-') ? hr : `+${hr}`}) ${zone[1]}`;

          switch (labelStyle) {
            case 'original':
              label = prefix;
              break;
            case 'altName':
              label = `${prefix} ${altName?.length ? `(${altName})` : ''}`;
              break;
            case 'abbrev':
              label = `${prefix} ${abbr?.length < 5 ? `(${abbr})` : ''}`;
              break;
            default:
              label = `${prefix}`;
          }

          selectOptions.push({
            value: tz.name,
            label,
            offset: tz.current.offset,
            abbrev: abbr,
            altName,
          });

          return selectOptions;
        }, [])
        .sort((a: ITimezoneOption, b: ITimezoneOption) => a.offset - b.offset),
    [labelStyle, localTimezones],
  );

  const handleChange = (tz: ITimezoneOption): void => onSelect && onSelect(tz);

  const findFuzzyTz = (zone: string): ITimezoneOption => {
    let currentTime = spacetime.now('GMT');
    try {
      currentTime = spacetime.now(zone);
    } catch (err) {
      return;
    }
    // eslint-disable-next-line consistent-return
    return getOptions
      .filter(
        (tz: ITimezoneOption) =>
          tz.offset === currentTime.timezone().current.offset,
      )
      .map((tz: ITimezoneOption) => {
        let score = 0;
        if (
          currentTime.timezones[tz.value.toLowerCase()] &&
          !!currentTime.timezones[tz.value.toLowerCase()].dst ===
            currentTime.timezone().hasDst
        ) {
          if (
            tz.value
              .toLowerCase()
              .indexOf(
                currentTime.tz.substring(currentTime.tz.indexOf('/') + 1),
              ) !== -1
          ) {
            score += 8;
          }
          if (
            tz.label
              .toLowerCase()
              .indexOf(
                currentTime.tz.substring(currentTime.tz.indexOf('/') + 1),
              ) !== -1
          ) {
            score += 4;
          }
          if (
            tz.value
              .toLowerCase()
              .indexOf(currentTime.tz.substring(0, currentTime.tz.indexOf('/')))
          ) {
            score += 2;
          }
          score += 1;
        } else if (tz.value === 'GMT') {
          score += 1;
        }
        return { tz, score };
      })
      .sort((a, b) => b.score - a.score)
      .map(({ tz }) => tz)[0];
  };

  // eslint-disable-next-line consistent-return
  const parseTimezone = (zone: ITimezone) => {
    if (typeof zone === 'object' && zone.value && zone.label) return zone;
    if (typeof zone === 'string') {
      return (
        getOptions.find(tz => tz.value === zone) ||
        (zone.indexOf('/') !== -1 && findFuzzyTz(zone))
      );
    }
    if (zone.value && !zone.label) {
      return getOptions.find(tz => tz.value === zone.value);
    }
  };

  return (
    <Select
      value={parseTimezone(value)?.label}
      onSelect={index => handleChange(getOptions[index.row])}
      onChange={handleChange}
      onBlur={onBlur}
      {...props}
    >
      {getOptions.map(({ value: itemValue, label }) => (
        <SelectItem key={itemValue} title={label} />
      ))}
    </Select>
  );
};

export default TimezoneSelect;

const allTimezones = {
  'Pacific/Midway': 'Midway Island, Samoa',
  'Pacific/Honolulu': 'Hawaii',
  'America/Juneau': 'Alaska',
  'America/Boise': 'Mountain Time',
  'America/Dawson': 'Dawson, Yukon',
  'America/Chihuahua': 'Chihuahua, La Paz, Mazatlan',
  'America/Phoenix': 'Arizona',
  'America/Chicago': 'Central Time',
  'America/Regina': 'Saskatchewan',
  'America/Mexico_City': 'Guadalajara, Mexico City, Monterrey',
  'America/Belize': 'Central America',
  'America/Detroit': 'Eastern Time',
  'America/Bogota': 'Bogota, Lima, Quito',
  'America/Caracas': 'Caracas, La Paz',
  'America/Santiago': 'Santiago',
  'America/St_Johns': 'Newfoundland and Labrador',
  'America/Sao_Paulo': 'Brasilia',
  'America/Tijuana': 'Tijuana',
  'America/Montevideo': 'Montevideo',
  'America/Argentina/Buenos_Aires': 'Buenos Aires, Georgetown',
  'America/Godthab': 'Greenland',
  'America/Los_Angeles': 'Pacific Time',
  'Atlantic/Azores': 'Azores',
  'Atlantic/Cape_Verde': 'Cape Verde Islands',
  GMT: 'UTC',
  'Europe/London': 'Edinburgh, London',
  'Europe/Dublin': 'Dublin',
  'Europe/Lisbon': 'Lisbon',
  'Africa/Casablanca': 'Casablanca, Monrovia',
  'Atlantic/Canary': 'Canary Islands',
  'Europe/Belgrade': 'Belgrade, Bratislava, Budapest, Ljubljana, Prague',
  'Europe/Sarajevo': 'Sarajevo, Skopje, Warsaw, Zagreb',
  'Europe/Brussels': 'Brussels, Copenhagen, Madrid, Paris',
  'Europe/Amsterdam': 'Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna',
  'Africa/Algiers': 'West Central Africa',
  'Europe/Bucharest': 'Bucharest',
  'Africa/Cairo': 'Cairo',
  'Europe/Helsinki': 'Helsinki, Kiev, Riga, Sofia, Tallinn, Vilnius',
  'Europe/Athens': 'Athens, Minsk',
  'Asia/Jerusalem': 'Jerusalem',
  'Africa/Harare': 'Harare, Pretoria',
  'Europe/Moscow': 'Istanbul, Moscow, St. Petersburg, Volgograd',
  'Asia/Kuwait': 'Kuwait, Riyadh',
  'Africa/Nairobi': 'Nairobi',
  'Asia/Baghdad': 'Baghdad',
  'Asia/Tehran': 'Tehran',
  'Asia/Dubai': 'Abu Dhabi, Muscat',
  'Asia/Baku': 'Baku, Tbilisi, Yerevan',
  'Asia/Kabul': 'Kabul',
  'Asia/Yekaterinburg': 'Ekaterinburg',
  'Asia/Karachi': 'Islamabad, Karachi, Tashkent',
  'Asia/Kolkata': 'Chennai, Kolkata, Mumbai, New Delhi',
  'Asia/Kathmandu': 'Kathmandu',
  'Asia/Dhaka': 'Astana, Dhaka',
  'Asia/Colombo': 'Sri Jayawardenepura',
  'Asia/Almaty': 'Almaty, Novosibirsk',
  'Asia/Rangoon': 'Yangon Rangoon',
  'Asia/Bangkok': 'Bangkok, Hanoi, Jakarta',
  'Asia/Krasnoyarsk': 'Krasnoyarsk',
  'Asia/Shanghai': 'Beijing, Chongqing, Hong Kong SAR, Urumqi',
  'Asia/Kuala_Lumpur': 'Kuala Lumpur, Singapore',
  'Asia/Taipei': 'Taipei',
  'Australia/Perth': 'Perth',
  'Asia/Irkutsk': 'Irkutsk, Ulaanbaatar',
  'Asia/Seoul': 'Seoul',
  'Asia/Tokyo': 'Osaka, Sapporo, Tokyo',
  'Asia/Yakutsk': 'Yakutsk',
  'Australia/Darwin': 'Darwin',
  'Australia/Adelaide': 'Adelaide',
  'Australia/Sydney': 'Canberra, Melbourne, Sydney',
  'Australia/Brisbane': 'Brisbane',
  'Australia/Hobart': 'Hobart',
  'Asia/Vladivostok': 'Vladivostok',
  'Pacific/Guam': 'Guam, Port Moresby',
  'Asia/Magadan': 'Magadan, Solomon Islands, New Caledonia',
  'Asia/Kamchatka': 'Kamchatka, Marshall Islands',
  'Pacific/Fiji': 'Fiji Islands',
  'Pacific/Auckland': 'Auckland, Wellington',
  'Pacific/Tongatapu': "Nuku'alofa",
};

export type ICustomTimezone = {
  [key: string]: string;
};

export type ILabelStyle = 'original' | 'altName' | 'abbrev';

export interface ITimezoneOption {
  value: string;
  label: string;
  abbrev?: string;
  altName?: string;
  offset?: number;
}

export type ITimezone = ITimezoneOption | string;

export interface Props extends Omit<ReactSelectProps, 'onChange'> {
  value: ITimezone;
  labelStyle?: ILabelStyle;
  onChange?: (timezone: ITimezoneOption) => void;
  timezones?: ICustomTimezone;
}
