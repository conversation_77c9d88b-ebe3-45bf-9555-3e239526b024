import { AxiosPromise } from 'axios';
import { Comment } from 'types';
import useClient from './useClient';

const useCommentsApi = () => {
  const client = useClient();

  const postComment = (taskId: number, text: string): AxiosPromise<Comment> =>
    client.post(`comments`, { taskId, text });

  const deleteComment = (
    taskId: number,
    id: Comment['id'],
  ): AxiosPromise<void> => client.delete(`tasks/${taskId}/comments/${id}`);

  return {
    postComment,
    deleteComment,
  };
};

export default useCommentsApi;
