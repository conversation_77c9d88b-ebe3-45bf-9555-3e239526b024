import useClient from './useClient';
import { Skill, User } from '~types';

export default () => {
  const client = useClient();

  const getSkills = (): Promise<Skill[]> =>
    client.get('/skillsets').then<User[]>(res => res.data.data);

  const postSkill = (data: { name: string }) =>
    client.post(`/skillsets`, data).then(res => res.data.data);

  const deleteSkill = (id: number): Promise<Skill[]> =>
    client.delete(`/skillsets/${id}`).then<User[]>(res => res.data.data);

  return { getSkills, postSkill, deleteSkill };
};
