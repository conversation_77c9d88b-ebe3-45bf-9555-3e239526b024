import * as React from 'react';
import { StackScreenProps } from '@react-navigation/stack';
import PDFReader from 'rn-pdf-reader-js';
import { useLayoutEffect } from 'react';
import { MoreParamList } from '~types';
import HeaderButtonAction from '~components/HeaderButton';
import * as Sharing from '~node_modules/expo-sharing';

type Props = StackScreenProps<MoreParamList, 'InvoicePDFScreen'>;

const InvoicePDFScreen = ({
  navigation,
  route: {
    params: { downloadedPdfUri },
  },
}: Props): React.ReactElement => {
  useLayoutEffect(() => {
    navigation.setOptions({
      headerRight: headerRight(downloadedPdfUri),
    });
  }, [downloadedPdfUri, navigation]);

  return (
    <PDFReader
      source={{
        uri: downloadedPdfUri,
      }}
    />
  );
};

const headerRight = (downloadedPdfUri: string) => () =>
  (
    <HeaderButtonAction
      onPress={() => {
        Sharing.shareAsync(downloadedPdfUri, {
          dialogTitle: downloadedPdfUri,
          UTI: 'com.adobe.pdf',
        });
      }}
      iconName="ios-share"
      iconPack="material"
    />
  );

export default InvoicePDFScreen;
