import React, { ReactElement, useEffect, useState } from 'react';
import { Input } from '@ui-kitten/components';
import { StyleProp, Text, View, ViewStyle } from 'react-native';
import {
  decimalTimeToHoursAndMinutes,
  hoursAndMinutesToDecimalTime,
} from '~utilities/dateAndTime';
import {
  StyleService,
  useStyleSheet,
} from '~node_modules/@ui-kitten/components';

type Props = {
  value: number;
  onChange: (value: number) => void;
  style: StyleProp<ViewStyle>;
};

const WorktimeInput = ({ value, onChange, style }: Props): ReactElement => {
  const styles = useStyleSheet(themedStyles);

  const { hours: initialHours, minutes: initialMinutes } =
    decimalTimeToHoursAndMinutes(value);

  const initialHoursString = initialHours ? initialHours.toString() : '';
  const initialMinutesString = initialMinutes ? initialMinutes.toString() : '';

  const [hours, setHours] = useState<string>(initialHoursString);
  const [minutes, setMinutes] = useState<string>(initialMinutesString);

  useEffect(() => {
    setHours(initialHoursString);
  }, [setHours, initialHours, initialHoursString]);

  useEffect(() => {
    setMinutes(initialMinutesString);
  }, [setMinutes, initialMinutes, initialMinutesString]);

  const handleChangeHours = (h: string) => {
    setHours(h);
    const decimalTime = hoursAndMinutesToDecimalTime({
      hours: Number(h),
      minutes: Number(minutes),
    });
    onChange(Number(decimalTime));
  };

  const handleChangeMinutes = (m: string) => {
    setMinutes(m);
    const decimalTime = hoursAndMinutesToDecimalTime({
      hours: Number(hours),
      minutes: Number(m),
    });
    onChange(Number(decimalTime));
  };

  return (
    <View style={style}>
      <Text style={styles.label}>Time spent {'\n'}(hours : mins)</Text>
      <View style={styles.container}>
        <Input
          value={hours}
          onChangeText={handleChangeHours}
          placeholder="00"
          selectTextOnFocus
          keyboardType="decimal-pad"
          style={styles.input}
        />
        <Text style={styles.colon}>:</Text>
        <Input
          value={minutes}
          onChangeText={handleChangeMinutes}
          placeholder="00"
          selectTextOnFocus
          keyboardType="decimal-pad"
          style={styles.input}
        />
      </View>
    </View>
  );
};

const themedStyles = StyleService.create({
  input: {
    flex: 1,
  },
  textInput: {
    marginHorizontal: 0,
  },
  label: {
    fontSize: 13,
    color: 'rgb(143, 155, 179)',
    fontWeight: '800',
    marginBottom: 4,
  },
  colon: {
    marginHorizontal: 6,
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default WorktimeInput;
