import React, { ReactElement, useContext } from 'react';
import { View, Pressable } from 'react-native';
import {
  Button,
  Input,
  Layout,
  StyleService,
  Text,
  useStyleSheet,
  Icon,
} from '@ui-kitten/components';
import { Formik } from 'formik';
import _ from 'lodash';
import * as Yup from 'yup';
import { useMutation } from 'react-query';
import { PersonIcon } from '~components/Icon';
import KeyboardAvoidingView from '~components/KeyboardAvoidingView';
import AuthContext from '~context/AuthContext';
import { authenticate } from '~services/auth';
import useClient from '~api';
import SpinnerButton from '~components/SpinnerButton';
import useRegisterForPushNotifications, {
  Modes,
} from '~hooks/useRegisterForPushNotifications';

const appName = process.env.APP_NAME;

const schema = Yup.object().shape({
  email: Yup.string().email().required('Required'),
  password: Yup.string().min(6).required('Required'),
});

export default ({ navigation }): React.ReactElement => {
  const styles = useStyleSheet(themedStyles);
  const [passwordVisible, setPasswordVisible] = React.useState<boolean>(false);
  const { setAuthToken } = useContext(AuthContext);
  const { signIn } = useClient();
  const { registerForPush } = useRegisterForPushNotifications();
  const { mutate, isLoading } = useMutation(signIn);

  const handleSignIn = values => {
    mutate(_.pickBy(values), {
      onSuccess: async data => {
        const { token, user } = data.data;
        const account = user.accounts[0];

        await authenticate(token);
        setAuthToken(token);
        // TODO! investigate the missing auth context
        registerForPush(Modes.Subscribe, token);

        if (!account.isActive) {
          navigation.navigate('ActivateAccountScreen');
        }
      },
    });
  };

  const onSignUpButtonPress = (): void => {
    if (navigation) {
      navigation.navigate('SignUp');
    }
  };

  const onPasswordIconPress = (): void => {
    setPasswordVisible(!passwordVisible);
  };

  const renderPasswordIcon = (props): ReactElement => (
    <Pressable onPress={onPasswordIconPress}>
      <Icon {...props} name={passwordVisible ? 'eye-off' : 'eye'} />
    </Pressable>
  );

  return (
    <KeyboardAvoidingView style={styles.container}>
      <View style={styles.headerContainer}>
        <Text category="h1" status="control">
          {appName}
        </Text>
        <Text style={styles.signInLabel} category="s1" status="control">
          Sign in to your account
        </Text>
      </View>
      <Formik
        initialValues={{ email: '', password: '' }}
        onSubmit={handleSignIn}
        validationSchema={schema}
        validateOnMount
      >
        {({
          values: { email, password },
          setFieldValue,
          submitForm,
          isValid,
        }) => (
          <>
            <Layout style={styles.formContainer} level="1">
              <Input
                placeholder="Email"
                accessoryRight={PersonIcon}
                value={email}
                onChangeText={value => setFieldValue('email', value)}
                autoCapitalize="none"
                keyboardType="email-address"
              />
              <Input
                style={styles.passwordInput}
                placeholder="Password"
                accessoryRight={renderPasswordIcon}
                value={password}
                secureTextEntry={!passwordVisible}
                onChangeText={value => setFieldValue('password', value)}
              />
              <View style={styles.forgotPasswordContainer}>
                <Button
                  style={styles.forgotPasswordButton}
                  appearance="ghost"
                  status="basic"
                  onPress={() => navigation.navigate('ForgotPassword')}
                >
                  Forgot your password?
                </Button>
              </View>
            </Layout>
            <SpinnerButton
              text="SIGN IN"
              style={styles.signInButton}
              onPress={submitForm}
              isLoading={isLoading}
              disabled={!isValid}
            />
          </>
        )}
      </Formik>
      <Button
        style={styles.signUpButton}
        appearance="ghost"
        status="basic"
        onPress={onSignUpButtonPress}
      >
        Sign Up
      </Button>
    </KeyboardAvoidingView>
  );
};

const themedStyles = StyleService.create({
  container: {
    backgroundColor: 'background-basic-color-1',
  },
  headerContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 216,
    backgroundColor: 'color-primary-default',
  },
  formContainer: {
    flex: 1,
    paddingTop: 32,
    paddingHorizontal: 16,
  },
  signInLabel: {
    marginTop: 16,
  },
  signInButton: {
    marginHorizontal: 16,
  },
  signUpButton: {
    marginVertical: 12,
    marginHorizontal: 16,
  },
  forgotPasswordContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  passwordInput: {
    marginTop: 16,
  },
  forgotPasswordButton: {
    paddingHorizontal: 0,
  },
});
