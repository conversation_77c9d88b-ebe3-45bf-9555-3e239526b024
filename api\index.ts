import { AxiosPromise } from 'axios';
import { SignUp, User } from '~types';
import useClient from './useClient';

export default () => {
  const client = useClient();

  const signUp = ({
    name,
    email,
    password,
    invitationCode,
    accountName,
    phone,
  }: SignUp): AxiosPromise<{ token: string }> =>
    client.post('/auth/signup', {
      name,
      email,
      password,
      invitationCode,
      accountName,
      phone,
    });

  const signIn = ({
    email,
    password,
  }: SignUp): AxiosPromise<{ token: string; user: User }> =>
    client.post('/auth/login', {
      email,
      password,
    });

  const signOut = () => client.post('/auth/logout');

  const forgotPassword = ({
    email,
  }: {
    email: string;
  }): AxiosPromise<{ token: string; user: User }> =>
    client.post('/auth/forgot-password', { email });

  const resetPassword = ({
    email,
    password,
    resetToken,
  }: {
    email: string;
    password: string;
    resetToken: string;
  }): AxiosPromise<{ token: string; user: User }> =>
    client.post('/auth/reset-password', {
      email,
      password,
      resetToken,
    });

  return { signUp, signOut, signIn, forgotPassword, resetPassword };
};
