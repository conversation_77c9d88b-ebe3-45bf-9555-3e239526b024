import React, { ReactElement } from 'react';
import { StyleService, useStyleSheet, Text } from '@ui-kitten/components';
import { View } from 'react-native';
import User from './User';
import { User as UserType } from '~types';
import useTimezone from '~hooks/useTimezone';
import { carrotColor } from '~constants/Colors';

type Props = {
  acceptedByUser: UserType;
  acceptedOn: Date;
  finishedAt?: Date;
};

const TaskCardAcceptedAndCompletedDates = ({
  acceptedByUser,
  acceptedOn,
  finishedAt,
}: Props): ReactElement => {
  const styles = useStyleSheet(themedStyles);
  const { formatInTimeZone } = useTimezone();

  return (
    <View>
      {acceptedByUser && (
        <Text style={styles.text}>
          Accepted by <User user={acceptedByUser} style={styles.text} />{' '}
          {formatInTimeZone(acceptedOn, 'MMM d, y h:mm aaa')}
        </Text>
      )}
      {finishedAt && (
        <Text style={styles.text}>
          Completed on {formatInTimeZone(finishedAt, 'MMM d, y h:mm aaa')}
        </Text>
      )}
    </View>
  );
};

const themedStyles = StyleService.create({
  text: {
    paddingTop: 5,
    fontSize: 12,
    fontWeight: 'bold',
    color: carrotColor,
  },
});

export default TaskCardAcceptedAndCompletedDates;
