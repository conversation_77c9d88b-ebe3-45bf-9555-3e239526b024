import React, { ReactElement, useState } from 'react';
import { Formik } from 'formik';
import {
  Divider,
  Layout,
  useStyleSheet,
  StyleService,
  Text,
  IndexPath,
} from '@ui-kitten/components';
import { StackNavigationProp, StackScreenProps } from '@react-navigation/stack';
import { ScrollView, Pressable, View } from 'react-native';
import { QueryClient, useMutation, useQueryClient } from 'react-query';
import * as Yup from 'yup';
import { recurringIntervals, priorities, TaskPriorities } from '../dummyData';
import useTasks from '~api/useTasks';
import useProperties from '~queries/useProperties';
import SpinnerButton from '~components/SpinnerButton';
import SelectInput from '~components/SelectInput';
import CheckboxesInput from '~components/CheckboxesInput';
import { invalidateQueries } from '~helpers';
import { carrotColor } from '~constants/Colors';
import PriorityRadioItem from '~components/PriorityRadioItem';
import RadioGroupInput from '~components/RadioGroupInput';
import Spinner from '~components/Spinner';
import PreferredDateEditor from '~components/PreferredDateEditor';
import { formatTaskDatetimeFields } from '~utilities/dateAndTime';
import useSkills from '~queries/useSkills';

const schema = Yup.object().shape({
  propertyId: Yup.number().required('Required'),
  priority: Yup.number().required('Required'),
  recurring: Yup.number().when('priority', {
    is: (value: TaskPriorities) =>
      value === TaskPriorities.OnSetDate ||
      value === TaskPriorities.OnCheckoutDate,
    then: Yup.number().required('Required'),
  }),
  preferredDate: Yup.string().when('priority', {
    is: TaskPriorities.OnSetDate,
    then: Yup.string().required('Required'),
    otherwise: Yup.string(),
  }),
  preferredStartTime: Yup.string().when('priority', {
    is: TaskPriorities.OnSetDate,
    then: Yup.string().required('Required'),
    otherwise: Yup.string(),
  }),
  preferredEndTime: Yup.string().when('priority', {
    is: TaskPriorities.OnSetDate,
    then: Yup.string().required('Required'),
    otherwise: Yup.string(),
  }),
  requiredSkillIds: Yup.array().min(1),
});

type Props = StackScreenProps;

export const invalidateTasksAfterAddOrUpdate = (
  queryClient: QueryClient,
  taskId: number,
): Promise<void> =>
  invalidateQueries(queryClient, [
    { key: 'tasks', options: {} },
    { key: ['task', taskId], options: { exact: true } },
    { key: 'task-assignments', options: {} },
    { key: 'tasks-created-by-me', options: {} },
  ]);

const updateAndNavigate = async (
  addOrUpdatePromise: Promise,
  navigation: StackNavigationProp,
): Promise<void> => {
  const { id } = await addOrUpdatePromise.then(data => data);

  navigation.popToTop();
  navigation.navigate('TaskScreen', { id });
};

const TaskSettings = ({ navigation, route }: Props): ReactElement => {
  const styles = useStyleSheet(themedStyles);

  const { updateTask, addTask } = useTasks();
  const { data: skills, isLoading: skillsAreLoading } = useSkills();
  const queryClient = useQueryClient();

  const [
    unavailablePropertyPrioritySelected,
    setUnavailablePropertyPrioritySelected,
  ] = useState(false);

  const [onCheckoutDayDisabled, setOnCheckoutDayDisabled] = useState(false);

  const { mutateAsync: updateTaskMutate, isLoading: updateIsLoading } =
    useMutation(updateTask, {
      onSuccess: data => {
        invalidateTasksAfterAddOrUpdate(queryClient, data.id);
      },
    });

  const { mutateAsync: addTaskMutate, isLoading: addIsLoading } = useMutation(
    addTask,
    {
      onSuccess: data => {
        invalidateTasksAfterAddOrUpdate(queryClient, data.id);
      },
    },
  );

  const { data: properties, isLoading: propertiesIsLoading } = useProperties();

  if (!properties || propertiesIsLoading || skillsAreLoading) {
    return <Spinner />;
  }

  const formattedPriorities = priorities.map(({ name, id: value, info }) => ({
    label: (
      <PriorityRadioItem
        label={name}
        info={info}
        color={
          value === TaskPriorities.OnCheckoutDate && onCheckoutDayDisabled
            ? 'grey'
            : 'black'
        }
      />
    ),
    value,
  }));

  const handleTaskSubmit = async values => {
    const transformedValues = formatTaskDatetimeFields(values);

    if (transformedValues.id) {
      updateAndNavigate(updateTaskMutate(transformedValues), navigation);
    } else {
      updateAndNavigate(addTaskMutate(transformedValues), navigation);
    }
  };

  let initValues = {};

  if (!route.params.values.propertyId && properties?.length) {
    initValues = { ...route.params.values };
  } else {
    initValues = route.params.values;
  }

  const handlePropertySelect = (selected: IndexPath) => {
    if (selected.row > 0) {
      setOnCheckoutDayDisabled(
        properties[selected.row - 1].calendarLink === null,
      );
    } else {
      // The please select one is selected
      setUnavailablePropertyPrioritySelected(false);
      setOnCheckoutDayDisabled(false);
    }
  };

  const handlePrioritySelect = (selected: number) => {
    const show =
      formattedPriorities[selected].value === TaskPriorities.OnCheckoutDate &&
      onCheckoutDayDisabled;

    setUnavailablePropertyPrioritySelected(show);
  };

  return (
    <ScrollView>
      <Formik
        initialValues={initValues}
        onSubmit={handleTaskSubmit}
        enableReinitialize
        validationSchema={schema}
        validateOnMount
      >
        {({ handleSubmit, values, setFieldValue, isValid }) => (
          <View style={styles.container}>
            <Layout style={styles.form} level="1">
              <SelectInput
                formLabel="Property address"
                name="propertyId"
                showEmpty
                style={styles.input}
                options={properties.map(item => ({
                  label: item.name,
                  value: item.id,
                }))}
                onSelect={selected => {
                  handlePropertySelect(selected, values);
                }}
              />
              <RadioGroupInput
                name="priority"
                formLabel="Priority"
                options={formattedPriorities}
                onSelect={selected => {
                  handlePrioritySelect(selected);
                }}
              />
              {values.priority === TaskPriorities.OnSetDate && (
                <PreferredDateEditor
                  style={styles.input}
                  values={values}
                  setFieldValue={setFieldValue}
                />
              )}
              {[1, 2].includes(values.priority) &&
                !unavailablePropertyPrioritySelected && (
                  <SelectInput
                    formLabel="How often"
                    name="recurring"
                    style={styles.input}
                    showEmpty
                    options={recurringIntervals.map(item => ({
                      label: item.name,
                      value: item.id,
                    }))}
                    disabled={
                      initValues.id &&
                      initValues.recurring !== recurringIntervals[0].id
                    }
                  />
                )}
              {unavailablePropertyPrioritySelected && (
                <Text style={[styles.warningMessage, styles.red]}>
                  <Text style={styles.red}>
                    For this option to work you need to add a{' '}
                  </Text>
                  <Pressable
                    onPress={() => {
                      navigation.navigate('More', {
                        screen: 'PropertyScreen',
                        params: {
                          id: values.propertyId,
                          backnavTo: {
                            stack: 'Available Jobs',
                            screen: 'AddTaskScreen',
                          },
                        },
                      });
                    }}
                  >
                    <Text style={[styles.link, styles.red]}>calendar link</Text>
                  </Pressable>{' '}
                  <Text style={styles.red}>for this property.</Text>
                </Text>
              )}

              <CheckboxesInput
                formLabel="Required Skills"
                propertyName="requiredSkillIds"
                dataset={skills}
              />
            </Layout>
            <Divider />
            <SpinnerButton
              text="Save Job"
              style={styles.addButton}
              onPress={handleSubmit}
              isLoading={updateIsLoading || addIsLoading}
              disabled={!isValid}
            />
          </View>
        )}
      </Formik>
    </ScrollView>
  );
};

const themedStyles = StyleService.create({
  container: {
    flex: 1,
    backgroundColor: 'background-basic-color-2',
  },
  form: {
    flex: 1,
    paddingHorizontal: 4,
    paddingVertical: 24,
  },
  input: {
    marginHorizontal: 12,
    marginVertical: 8,
  },
  multilineInput: {
    height: 120,
    marginHorizontal: 12,
    marginVertical: 8,
  },
  middleInput: {
    width: 128,
  },
  addButton: {
    marginHorizontal: 16,
    marginVertical: 24,
  },
  warningMessage: {
    marginHorizontal: 12,
    marginBottom: 12,
  },
  link: { textDecorationLine: 'underline' },
  red: { color: carrotColor },
  customSelectContent: {
    marginLeft: 12,
    fontSize: 14,
  },
});

export default TaskSettings;
