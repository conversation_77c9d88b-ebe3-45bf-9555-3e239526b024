import * as React from 'react';
import { useQuery } from 'react-query';
import { StackScreenProps } from '@react-navigation/stack';
import { Button, StyleService, useStyleSheet } from '@ui-kitten/components';
import { ScrollView } from 'react-native';
import useUser from '~api/useUser';
import Spinner from '~components/Spinner';
import usePayments from '~api/usePayments';
import PaymentCard from '~components/PaymentCard';
import { MoreParamList } from '~types';

type Props = StackScreenProps<MoreParamList, 'PaymentDetailsScreen'>;

const PaymentDetails = ({ route, navigation }: Props): React.ReactElement => {
  const styles = useStyleSheet(themedStyles);

  const { paymentId } = route.params;
  const { getPayment } = usePayments();
  const { getUser } = useUser();
  const { data: currentUser, isLoading: curentUserIsLoading } = useQuery(
    'user',
    () => getUser(),
  );
  const { data: payment, isLoading } = useQuery(['payment', paymentId], () =>
    getPayment(paymentId),
  );

  const currentUserRole = currentUser?.accounts[0].pivot.role;

  if (isLoading || curentUserIsLoading || !payment) {
    return <Spinner />;
  }

  const { id, description, subjectMonth, amount, date, media } = payment;

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <PaymentCard
        id={id}
        description={description}
        subjectMonth={subjectMonth}
        amount={amount}
        date={date}
        media={media}
      />
      {currentUserRole === 0 && (
        <Button
          style={styles.button}
          onPress={() =>
            navigation.navigate('LogPaymentScreen', { paymentId: id })
          }
          appearance="outline"
        >
          Edit Payment
        </Button>
      )}
    </ScrollView>
  );
};

const themedStyles = StyleService.create({
  container: {
    paddingVertical: 16,
  },
  button: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 22,
  },
  buttonContainer: {
    width: 200,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 15,
  },
});

export default PaymentDetails;
