import React from 'react';
import { Button } from '@ui-kitten/components';
import { RightArrowIcon } from '~components/Icon';
import LinkForOtherPerson from '~components/HouseKeepingComponents/ChecklistTab/LinkForOtherPerson';
import { HKChecklist, HKExtendedJob } from '~/types';
import Stack from '~components/HouseKeepingComponents/UI/Stack';
import Modal from '~components/HouseKeepingComponents/Shared/Modal';

type Props = {
  job: HKExtendedJob;
  checklists: HKChecklist[] | null;
  opened: boolean;
  onClose: () => void;
};

const LinkForOtherPersonModal = ({
  job,
  checklists,
  opened,
  onClose,
}: Props) => {
  if (!checklists) {
    return null;
  }

  return (
    <Modal
      visible={opened}
      onClose={onClose}
      title="Share Checklist"
      style={{ flexGrow: 0, flexBasis: 'auto' }}
    >
      <Stack>
        {checklists.map(checklist => (
          <LinkForOtherPerson key={checklist} job={job} checklist={checklist} />
        ))}
        <Button
          status="info"
          size="large"
          accessoryRight={RightArrowIcon}
          onPress={onClose}
        >
          Continue
        </Button>
      </Stack>
    </Modal>
  );
};

export default LinkForOtherPersonModal;
