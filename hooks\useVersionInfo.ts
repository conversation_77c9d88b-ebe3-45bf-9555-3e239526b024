import { useEffect, useState } from 'react';
import semver from 'semver';
import * as Application from '~node_modules/expo-application';

const useVersionInfo = () => {
  const [versionInfo, setVersionInfo] = useState({
    needsUpdate: false,
    latestVersion: '',
    installedVersion: '',
    appStoreUrl: '',
  });
  useEffect(() => {
    fetch('https://itunes.apple.com/lookup?bundleId=com.airteam').then(
      async response => {
        const data = await response.json();
        const installedVersion =
          Application.nativeApplicationVersion || '0.0.0';
        const latestVersion = data.results[0].version;

        setVersionInfo({
          needsUpdate: semver.gt(latestVersion, installedVersion),
          latestVersion,
          installedVersion,
          appStoreUrl: data.results[0].trackViewUrl,
        });
      },
    );
  }, []);

  return versionInfo;
};

export default useVersionInfo;
