import React from 'react';
import { KeyboardAvoidingViewProps, ScrollViewProps } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';

/**
 * https://github.com/APSL/react-native-keyboard-aware-scroll-view
 */
const KeyboardAvoidingView = (
  props: KeyboardAvoidingViewProps,
): React.ReactElement => {
  const defaultProps = {
    style: { flex: 1 },
    contentContainerStyle: { flexGrow: 1 },
    bounces: false,
    bouncesZoom: false,
    alwaysBounceVertical: false,
    alwaysBounceHorizontal: false,
    extraScrollHeight: 60,
  };

  return React.createElement(KeyboardAwareScrollView, {
    ...defaultProps,
    ...props,
  });
};

export default KeyboardAvoidingView;
