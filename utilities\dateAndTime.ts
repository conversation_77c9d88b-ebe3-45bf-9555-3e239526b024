import { set } from 'date-fns';
import { utcToZonedTime } from 'date-fns-tz';
import { TaskPriorities } from '~dummyData';
import { Task } from '~types';

export const decimalTimeToHoursAndMinutes = (decimalTime: number) => ({
  hours: Math.trunc(Number(decimalTime)),
  minutes: Math.round((Number(decimalTime) % 1) * 60),
});

export const hoursAndMinutesToDecimalTime = ({
  hours,
  minutes,
}: {
  hours: number;
  minutes: number;
}) => (hours + minutes / 60).toFixed(2);

export const formatTaskDatetimeFields = (task: Task) => {
  const convertToISOString = (date: string | Date): string => {
    if (typeof date === 'string') {
      return date;
    }

    return date.toISOString();
  };

  const transformedValues = { ...task };

  if (transformedValues.priority === TaskPriorities.OnSetDate) {
    // Place of the time transformation of preferred date
    const hours = new Date(transformedValues.preferredStartTime).getHours();
    const minutes = new Date(transformedValues.preferredStartTime).getMinutes();
    const seconds = new Date(transformedValues.preferredStartTime).getSeconds();

    let { preferredDate } = transformedValues;

    if (preferredDate) {
      if (typeof preferredDate === 'string') {
        preferredDate = new Date(preferredDate);
      }

      const zonedDate = utcToZonedTime(preferredDate, 'UTC');

      transformedValues.preferredDate = set(zonedDate, {
        hours,
        minutes,
        seconds,
      });
    }
  }

  return {
    ...transformedValues,
    preferredStartTime: convertToISOString(
      transformedValues.preferredStartTime,
    ),
    preferredEndTime: convertToISOString(transformedValues.preferredEndTime),
    preferredDate: convertToISOString(transformedValues.preferredDate),
  };
};
