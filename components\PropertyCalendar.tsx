import { eachDayOfInterval, format, parseISO, isEqual } from 'date-fns';
import React, { ReactElement } from 'react';
import { Calendar } from 'react-native-calendars';
import { CalendarDate } from '~types';
import useTimezone from '~hooks/useTimezone';
import { View, Text } from '~components/Themed';
import { DayState } from '~node_modules/react-native-calendars/src/types';
import {
  StyleService,
  useStyleSheet,
} from '~node_modules/@ui-kitten/components';
import { carrotColor } from '~constants/Colors';

type Props = {
  calendarDates: Array<CalendarDate>;
  highlightColor: string;
};

type markedDate = {
  startingDay?: boolean;
  endingDay?: boolean;
  color: string;
};

type MarkedDates = {
  [key: string]: markedDate;
};

const PropertyCalendar = ({
  calendarDates,
  highlightColor,
}: Props): ReactElement => {
  const { zonedTimeToUtc } = useTimezone();

  const markedDates: MarkedDates = {};
  calendarDates.forEach(calendarDate => {
    const startDate = parseISO(calendarDate.checkInDate);
    const endDate = parseISO(calendarDate.date);

    try {
      eachDayOfInterval({
        start: startDate,
        end: endDate,
      }).forEach(date => {
        const dateString = format(date, 'yyyy-MM-dd');
        const utcDate = zonedTimeToUtc(date);

        markedDates[dateString] = {
          color: highlightColor,
          startingDay:
            markedDates[dateString]?.startingDay || isEqual(utcDate, startDate),
          endingDay:
            markedDates[dateString]?.endingDay || isEqual(utcDate, endDate),
        };
      });
    } catch {
      // skip interval if the range is not valid
    }
  });

  return (
    <Calendar
      markingType="period"
      markedDates={markedDates}
      dayComponent={DayComponent}
    />
  );
};

const DayComponent = ({
  date,
  state,
  marking: { startingDay, endingDay } = {},
}) => {
  const styles = useStyleSheet(themedStyles);
  const middleDay = startingDay === false && endingDay === false;

  return (
    <View style={styles.dayContainer}>
      {startingDay && <View style={[styles.day, styles.startingDay]} />}
      {endingDay && <View style={[styles.day, styles.endingDay]} />}
      {middleDay && <View style={[styles.day, styles.middleDay]} />}
      <Text
        style={{
          ...styles.dayLabel,
          color: dayLabelColor(state, startingDay, middleDay),
        }}
      >
        {date?.day}
      </Text>
      <View style={styles.dayBorders} />
    </View>
  );
};

const themedStyles = StyleService.create({
  dayContainer: {
    position: 'relative',
    width: 54,
    height: 30,
    overflow: 'hidden',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dayBorders: {
    position: 'absolute',
    left: 0,
    borderRightWidth: 1,
    borderColor: 'white',
    height: '100%',
  },
  day: {
    position: 'absolute',
    top: 0,
    backgroundColor: carrotColor,
    height: 30,
  },
  startingDay: {
    left: 15,
    width: 40,
    borderTopLeftRadius: 15,
    borderBottomLeftRadius: 15,
  },
  endingDay: {
    left: -12,
    width: 25,
    borderTopRightRadius: 15,
    borderBottomRightRadius: 15,
  },
  middleDay: {
    left: 0,
    width: '100%',
  },
  dayLabel: {
    fontWeight: '500',
    fontSize: 14,
    textAlign: 'center',
    verticalAlign: 'middle',
  },
});

const dayLabelColor = (
  state: DayState | undefined,
  isStartingDay: boolean | undefined,
  isMiddleDay: boolean | undefined,
) => {
  if (state === 'disabled') {
    return 'lightgray';
  }
  if (isStartingDay || isMiddleDay) {
    return 'white';
  }

  return 'black';
};

export default PropertyCalendar;
