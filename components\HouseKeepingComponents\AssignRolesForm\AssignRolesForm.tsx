import React, { useState } from 'react';
import * as Yup from 'yup';
import { StyleService, useStyleSheet } from '@ui-kitten/components';
import { Formik } from 'formik';
import { RightArrowIcon } from '~/components/Icon';
import { HKJob, HKChecklist, HKSection, HKTask, HKPerformType } from '~/types';
import useHousekeepingPerformOptions from '~hooks/useHousekeepingPerformOptions';
import SpinnerButton from '~components/SpinnerButton';
import RadioCardGroup from '~components/HouseKeepingComponents/Shared/RadioCardGroup';
import AssignRoleInput from '~components/HouseKeepingComponents/AssignRolesForm/AssignRoleInput';
import KeyboardAvoidingView from '~components/KeyboardAvoidingView';
import PreviewButton from '~components/HouseKeepingComponents/AssignRolesForm/PreviewButton';
import ChecklistSectionsModal from '~components/HouseKeepingComponents/AssignRolesForm/ChecklistSectionsModal';
import CHECKLIST_OPTIONS from '~constants/ChecklistOptions';

const schema = Yup.object().shape({
  performType: Yup.string().required('This field is required'),
  leaderPerforms: Yup.string().required('This field is required'),
  leaderName: Yup.string().required('This field is required'),
  helperName: Yup.string().when('leaderPerforms', {
    is: 'all',
    then: Yup.string(),
    otherwise: Yup.string().required('Required'),
  }),
});

type Props = {
  onSave: (values: HKJob) => Promise<void>;
  values?: HKJob | null;
  sections?: HKSection[];
  tasks?: HKTask[];
};

const AssignRolesForm = ({ values, sections, tasks, onSave }: Props) => {
  const styles = useStyleSheet(themedStyle);
  const [performPersonally, setPerformPersonally] = useState<boolean>(
    values ? values.performType === HKPerformType.Personally : true,
  );
  const [perform, setPerform] = useState<HKChecklist | null>(
    values?.leaderPerforms ?? null,
  );
  const [checklistPreview, setChecklistPreview] = useState<HKChecklist | null>(
    null,
  );
  const performOptions = useHousekeepingPerformOptions({ performPersonally });

  const onSubmit = async (submitValues: HKJob) => {
    await onSave(submitValues);
  };

  const handleClickPreview = (checklist: HKChecklist) => {
    setChecklistPreview(checklist);
  };
  const handleCloseChecklistPreview = () => {
    setChecklistPreview(null);
  };

  const showPreview = tasks && sections;

  const spinnerText = values || !performPersonally ? 'Save' : 'Start';

  return (
    <>
      <Formik
        initialValues={
          values ?? {
            id: null,
            performType: HKPerformType.Personally,
            leaderPerforms: 'person1',
            leaderName: '',
            helperName: '',
          }
        }
        onSubmit={onSubmit}
        validationSchema={schema}
        enableReinitialize
        validateOnMount
      >
        {({
          values: formValues,
          setFieldValue,
          handleSubmit,
          isValid,
          isSubmitting,
        }) => (
          <KeyboardAvoidingView contentContainerStyle={styles.container}>
            <RadioCardGroup
              name="performType"
              formLabel="Will you be the one performing the checklist personally?"
              options={CHECKLIST_OPTIONS}
              onSelect={index => {
                setPerformPersonally(index === 0);
                setFieldValue(
                  'performType',
                  index === 0
                    ? HKPerformType.Personally
                    : HKPerformType.Assigned,
                );
              }}
            />
            <RadioCardGroup
              name="leaderPerforms"
              formLabel={
                performPersonally
                  ? 'Which checklist are you going to follow?'
                  : 'Which checklist is the lead housekeeper going to follow?'
              }
              options={performOptions.map(performOption => ({
                ...performOption,
                optionChildren: showPreview && (
                  <PreviewButton
                    onPress={() => handleClickPreview(performOption.value)}
                  />
                ),
              }))}
              onSelect={index => {
                setPerform(performOptions[index].value);
                setFieldValue('leaderPerforms', performOptions[index].value);
              }}
            />
            <AssignRoleInput
              label={
                performPersonally
                  ? "What's your name?"
                  : "What's the lead housekeeper's name?"
              }
              value={formValues.leaderName}
              onChangeText={value => setFieldValue('leaderName', value)}
              placeholder={
                performPersonally
                  ? 'Enter your name'
                  : "Enter the lead housekeeper's name"
              }
            />
            {perform !== 'all' && (
              <AssignRoleInput
                label="What's the name of the other person?"
                value={formValues.helperName}
                onChangeText={value => setFieldValue('helperName', value)}
                placeholder="Enter the name of the other person"
              />
            )}
            <SpinnerButton
              text={!isSubmitting ? spinnerText : ''}
              accessoryRight={!isSubmitting ? RightArrowIcon : undefined}
              onPress={() => handleSubmit()}
              isLoading={isSubmitting}
              disabled={!isValid}
              status="info"
            />
          </KeyboardAvoidingView>
        )}
      </Formik>
      {showPreview && !!checklistPreview && (
        <ChecklistSectionsModal
          sections={sections}
          tasks={tasks}
          checklist={checklistPreview}
          onClose={handleCloseChecklistPreview}
        />
      )}
    </>
  );
};

export default AssignRolesForm;

const themedStyle = StyleService.create({
  container: {
    gap: 24,
  },
});
