import React from 'react';
import { ScrollView } from 'react-native';
import { StyleService, useStyleSheet, List } from '@ui-kitten/components';
import { useMutation, useQueryClient } from 'react-query';
import { StackScreenProps } from '@react-navigation/stack';
import { MoreParamList, Skill } from '~types';
import Spinner from '~components/Spinner';
import AdminSkillsDeleteButton from '~components/AdminSkillsDeleteButton';
import useSkillsQuery from '~queries/useSkills';
import useSkills from '~api/useSkills';
import { Button, Text } from '~node_modules/@ui-kitten/components';
import ListItem from '~components/ListItem';

type Props = StackScreenProps<MoreParamList>;

const AdminSkillsScreen = ({ navigation }: Props) => {
  const styles = useStyleSheet(themedStyles);
  const { deleteSkill } = useSkills();
  const { data: skills, isLoading: skillsAreLoading } = useSkillsQuery();
  const queryClient = useQueryClient();
  const { mutate: deleteSkillMutate } = useMutation(deleteSkill, {
    onSuccess: () => {
      queryClient.invalidateQueries('skills');
    },
  });

  if (skillsAreLoading) {
    return <Spinner />;
  }

  const handleDeleteSkill = (id: number) => {
    deleteSkillMutate(id);
  };

  const renderItem = ({
    item,
  }: {
    item: Skill;
    index: number;
  }): React.ReactElement => {
    const { id, name, accountId } = item;
    return (
      <ListItem
        name={<Text>{name}</Text>}
        button={
          accountId ? (
            <AdminSkillsDeleteButton
              id={id}
              onConfirm={() => handleDeleteSkill(id)}
            />
          ) : undefined
        }
      />
    );
  };

  return (
    <ScrollView>
      <Text style={styles.heading}>Account Skills</Text>
      <List
        data={skills?.filter(({ accountId }) => accountId)}
        renderItem={renderItem}
      />
      <Button
        style={styles.addButton}
        appearance="outline"
        size="giant"
        onPress={() => navigation.push('AddSkillScreen')}
      >
        Add New Skill
      </Button>
      <Text style={styles.heading}>Default Skills</Text>
      <List
        data={skills?.filter(({ accountId }) => !accountId)}
        renderItem={renderItem}
      />
    </ScrollView>
  );
};

export default AdminSkillsScreen;

const themedStyles = StyleService.create({
  addButton: {
    margin: 16,
  },
  heading: {
    fontSize: 18,
    margin: 16,
  },
});
