import React from 'react';
import { StyleService, Button } from '@ui-kitten/components';
import { HKExtendedJob } from '~/types';
import Modal from '~components/HouseKeepingComponents/Shared/Modal';
import PropertyCard from '~components/PropertyCard';

type Props = {
  job: HKExtendedJob | null;
  opened: boolean;
  onClose: () => void;
};

const AccessInfoListModal = ({ job, opened, onClose }: Props) => {
  if (!job || !job.id) return null;

  const {
    id,
    property: {
      name,
      address,
      accessInformation,
      notes,
      deletedAt,
      isSuspended,
    },
  } = job;

  return (
    <Modal visible={opened} style={styles.modal} contentStyle={styles.content}>
      <PropertyCard
        id={id}
        name={name}
        address={address}
        accessInformation={accessInformation}
        notes={notes}
        deletedAt={new Date(deletedAt || '')}
        isSuspended={isSuspended}
        showDetails
      />
      <Button size="large" onPress={onClose} style={styles.button}>
        Close
      </Button>
    </Modal>
  );
};

export default AccessInfoListModal;

const styles = StyleService.create({
  modal: {
    flex: 1,
    margin: 0,
    width: '100%',
  },
  content: {
    flex: 1,
    paddingTop: 42,
    paddingBottom: 42,
    paddingHorizontal: 0,
  },
  button: {
    marginTop: 'auto',
  },
});
