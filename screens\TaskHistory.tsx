import { Card, Text } from '@ui-kitten/components';
import React, { ReactElement } from 'react';
import { useQuery } from 'react-query';
import { ScrollView, StyleSheet } from 'react-native';
import useTasks from '~api/useTasks';
import CardLineItem from '~components/CardLineItem';
import Spinner from '~components/Spinner';
import { taskLogActions } from '~dummyData';
import useTimezone from '~hooks/useTimezone';

type Props = {
  route: any;
};

const TaskHistory = ({ route }: Props): ReactElement => {
  const taskId = route?.params?.taskId;
  const taskAssignmentId = route?.params?.taskAssignmentId;
  const description = route?.params?.description;

  const { getTaskLogs } = useTasks();
  const { data: taskLogs, isLoading } = useQuery(
    ['task', taskId, taskAssignmentId],
    () => getTaskLogs(taskId, taskAssignmentId),
  );

  const { formatInTimeZone } = useTimezone();

  if (isLoading) {
    return <Spinner />;
  }

  return (
    <ScrollView>
      <Card style={styles.container} header={<Text>{description}</Text>}>
        {taskLogs.map(taskLog => {
          const action = taskLogActions.find(
            taskLogAction => taskLogAction.id === taskLog.actionType,
          );

          const text = `${taskLog.user.name} ${action?.name.replace(
            '{{job}}',
            'this Job',
          )} on ${formatInTimeZone(taskLog.createdAt, 'MMM d, y h:mm aaa')}`;

          return (
            <CardLineItem
              key={taskLog.id}
              text={text}
              iconName={action?.icon}
            />
          );
        })}
      </Card>
    </ScrollView>
  );
};

export default TaskHistory;

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginVertical: 16,
  },
});
