import React from 'react';
import { View } from 'react-native';
import {
  Menu,
  MenuItem,
  StyleService,
  useStyleSheet,
} from '@ui-kitten/components';
import { StackNavigationProp } from '@react-navigation/stack';
import { MoneyIcon, PeopleIcon, PinIcon } from '~components/Icon';
import { MoreParamList } from '~types';

export const headerTitles = {
  technicians: 'Technician Invoices',
  properties: 'Property Invoices',
};

type Props = {
  navigation: StackNavigationProp<MoreParamList, 'InvoicesMenuScreen'>;
};

export default ({ navigation }: Props): React.ReactElement => {
  const styles = useStyleSheet(themedStyle);

  const menuItems = [
    <MenuItem
      key={headerTitles.technicians}
      style={styles.menuItem}
      title={headerTitles.technicians}
      accessoryLeft={PeopleIcon}
      onPress={() =>
        navigation.navigate('InvoicesScreen', {
          headerTitle: headerTitles.technicians,
        })
      }
    />,
    <MenuItem
      key={headerTitles.properties}
      style={styles.menuItem}
      title={headerTitles.properties}
      accessoryLeft={PinIcon}
      onPress={() =>
        navigation.navigate('InvoicesScreen', {
          headerTitle: headerTitles.properties,
        })
      }
    />,
    <MenuItem
      key="profitmargin"
      style={styles.menuItem}
      title="Mark Up"
      accessoryLeft={MoneyIcon}
      onPress={() => navigation.navigate('ProfitMarginScreen')}
    />,
    /*
    <MenuItem
      key="unpaidinvoices"
      style={styles.menuItem}
      title="Unpaid Invices"
      accessoryLeft={MoneyIcon}
      onPress={() => navigation.navigate('UnpaidInvoicesScreen')}
    />, */
  ];

  return (
    <View>
      <Menu contentContainerStyle={styles.menu}>{menuItems}</Menu>
    </View>
  );
};

const themedStyle = StyleService.create({
  menu: {
    height: '100%',
  },
  menuItem: {
    flexGrow: 1,
    flexBasis: '20%',
  },
});
