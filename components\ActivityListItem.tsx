import React, { ReactElement } from 'react';
import {
  StyleService,
  useStyleSheet,
  Text,
  Icon,
  Card,
} from '@ui-kitten/components';
import { View } from 'react-native';
import { carrotColor } from '~constants/Colors';

type Props = {
  text: string;
  date: string;
  onPress: () => void;
  iconName: string;
  additionalInfo?: string;
};

const ActivityListItem = ({
  text,
  date,
  onPress,
  iconName,
  additionalInfo,
}: Props): ReactElement => {
  const styles = useStyleSheet(themedStyles);

  return (
    <Card onPress={onPress}>
      <View style={styles.container}>
        <View style={styles.iconContainer}>
          <Icon style={styles.icon} fill="white" name={iconName} />
        </View>
        <View style={styles.textContainer}>
          <Text style={styles.text}>{text}</Text>
          <Text style={styles.date}>{date}</Text>
          {additionalInfo && (
            <Text style={[styles.text, styles.additionalInfo]}>
              {additionalInfo}
            </Text>
          )}
        </View>
      </View>
    </Card>
  );
};

const themedStyles = StyleService.create({
  container: {
    flexDirection: 'row',
    marginHorizontal: 0,
    marginBottom: 10,
    width: '100%',
    padding: 5,
  },
  textContainer: {
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    backgroundColor: carrotColor,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  icon: {
    width: 25,
    height: 25,
  },
  text: {
    paddingTop: 2,
    marginLeft: 16,
    fontSize: 12,
    fontWeight: 'bold',
    color: 'dimgray',
  },
  date: {
    paddingTop: 2,
    marginLeft: 16,
    fontSize: 12,
    fontWeight: 'bold',
    color: 'gray',
  },
  additionalInfo: {
    marginTop: 16,
  },
});

export default ActivityListItem;
