import React, { ReactElement } from 'react';
import { ListRenderItemInfo, StyleSheet, View } from 'react-native';
import { Comment, Task, TaskAssignment } from 'types';
import {
  Input,
  Layout,
  List,
  useStyleSheet,
  Text,
  ListItem,
  ListItemProps,
} from '@ui-kitten/components';
import { Formik, FormikHelpers } from 'formik';
import { useQueryClient } from 'react-query';
import Hyperlink from 'react-native-hyperlink';
import useTimezone from '~hooks/useTimezone';
import SpinnerButton from '~components/SpinnerButton';
import useCommentsApi from '~api/useCommentsApi';
import User from '~components/User';
import { linkColor } from '~constants/Colors';

type Props = {
  taskId: Task['id'];
  comments: Comment[];
  taskAssignmentId?: TaskAssignment['id'];
};

// todo: strike through deleted users

const CommentsCard = ({
  taskId,
  comments,
  taskAssignmentId,
}: Props): ReactElement => {
  const styles = useStyleSheet(themedStyles);

  const renderItem = (
    info: ListRenderItemInfo<Comment>,
  ): React.ReactElement => (
    <CommentListItem
      style={styles.item}
      comment={info.item}
      onPress={() => {}}
    />
  );

  return (
    <List
      style={styles.list}
      data={comments}
      renderItem={renderItem}
      ListHeaderComponent={<CommentsHeader />}
      ListFooterComponent={
        <CommentForm taskId={taskId} taskAssignmentId={taskAssignmentId} />
      }
    />
  );
};

export const CommentListItem = (
  props: ListItemProps & { comment: Comment },
): ReactElement => {
  const styles = useStyleSheet(themedStyles);
  const { comment, ...listItemProps } = props;
  const { createdAt } = comment;
  const { formatInTimeZone } = useTimezone();

  const renderMessageDate = (): ReactElement => (
    <View style={styles.dateContainer}>
      <Text style={styles.dateText} appearance="hint" category="c1">
        {formatInTimeZone(new Date(createdAt), 'PP')}
      </Text>
    </View>
  );

  const renderTitle = () => (
    <Text style={styles.userName} category="s1">
      <User user={comment.user} />
    </Text>
  );

  const renderDescription = () => (
    <Hyperlink linkDefault linkStyle={{ color: linkColor }}>
      <Text style={styles.commentText}>{comment.text}</Text>
    </Hyperlink>
  );

  return (
    <ListItem
      {...listItemProps}
      title={renderTitle}
      description={renderDescription}
      accessoryRight={renderMessageDate}
    />
  );
};

const CommentsHeader = (): ReactElement => {
  const styles = useStyleSheet(themedStyles);

  return (
    <Layout style={styles.headerLayout}>
      <Text category="h5">Comments</Text>
    </Layout>
  );
};

type CommentFormProps = {
  taskId: number;
  taskAssignmentId?: number;
};
const CommentForm = ({
  taskId,
  taskAssignmentId,
}: CommentFormProps): ReactElement => {
  const styles = useStyleSheet(themedStyles);
  const { postComment } = useCommentsApi();
  const queryClient = useQueryClient();
  type FormValues = { text: string };
  const onSubmit = async (
    { text }: FormValues,
    { resetForm }: FormikHelpers<FormValues>,
  ) => {
    await postComment(taskId, text);
    await queryClient.invalidateQueries(['task', taskId]);
    await queryClient.invalidateQueries(['task-assignments', taskAssignmentId]);
    resetForm();
  };

  return (
    <Formik onSubmit={onSubmit} initialValues={{ text: '' }}>
      {({ submitForm, setFieldValue, values, isSubmitting }) => (
        <Layout style={styles.formLayout}>
          <Input
            style={styles.textInput}
            placeholder="Add your comment"
            value={values.text}
            onChangeText={value => setFieldValue('text', value)}
          />
          <SpinnerButton
            size="medium"
            text="Add"
            onPress={submitForm}
            isLoading={isSubmitting}
          />
        </Layout>
      )}
    </Formik>
  );
};

const themedStyles = StyleSheet.create({
  container: {},
  list: {
    flex: 1,
    marginHorizontal: 18,
  },
  headerLayout: {
    padding: 16,
    paddingBottom: 0,
  },
  formLayout: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  textInput: {
    flex: 1,
    marginRight: 16,
  },
  item: {
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'background-basic-color-3',
  },
  userName: {
    fontSize: 14,
  },
  commentText: {
    fontSize: 20,
  },
  dateContainer: {
    alignSelf: 'flex-start',
  },
  dateText: {
    textAlign: 'right',
    minWidth: 64,
  },
});

export default CommentsCard;
