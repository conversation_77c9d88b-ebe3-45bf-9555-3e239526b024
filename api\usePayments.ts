import { Platform } from 'react-native';
import { useContext } from 'react';
import _ from 'lodash';
import useClient, { formData } from './useClient';
import { Payment } from '~types';
import { handleBackgroundUploadStatus } from './backgroundUpload';
import AuthContext from '~context/AuthContext';
import BackgroundUploadProgressContext from '~context/BackgroundUploadProgressContext';
import { useQueryClient } from '~node_modules/react-query';

const apiBaseUrl = process.env.API_BASE_URL;

const formatPaymentData = (payment: Partial<Payment>) => {
  const { media } = payment;

  const { data, config } = formData(
    Platform.OS !== 'web' ? _.omit(payment, 'media') : payment,
  );
  return { media, data, config };
};

export default () => {
  const { setUploads } = useContext(BackgroundUploadProgressContext);
  const client = useClient();
  const queryClient = useQueryClient();
  const { authToken } = useContext(AuthContext);

  const sendData = (
    media: string,
    data: FormData,
    config: {
      headers: {
        Accept: string;
        'Content-Type': string;
      };
    },
    url: string,
  ) =>
    client.post(url, data, config).then(response => {
      const { id } = response.data.data;
      const uploadUrl = `${apiBaseUrl}/payments/${id}/media`;

      if (Platform.OS === 'web' && media) {
        return response.data.data;
      }

      if (!media) {
        return [];
      }

      return {
        promises: handleBackgroundUploadStatus(
          [{ uploadUrl, localUrl: media.uri, type: 'media' }],
          authToken,
          setUploads,
          queryClient,
        ),
        id,
      };
    });

  const createPayment = (payment): Promise<unknown> => {
    const { media, data, config } = formatPaymentData(payment);

    return sendData(media, data, config, '/payments');
  };

  const getPayments = (
    month: string | null = null,
    userId: number | null = null,
  ): Promise<Payment[]> =>
    client
      .get('/payments', {
        params: {
          subject_month: month,
          user_id: userId,
        },
      })
      .then(res => res.data.data);

  const getPayment = (id: number): Promise<Payment> =>
    client.get(`/payments/${id}`).then(res => res.data.data);

  const updatePayment = ({
    id,
    ...payment
  }): Promise<Payment & { promises: Promise<unknown[]>; id: number }> => {
    const { media, data, config } = formatPaymentData(payment);
    data.append('_method', 'PUT');

    return sendData(media, data, config, `/payments/${id}`);
  };

  return {
    createPayment,
    getPayments,
    getPayment,
    updatePayment,
  };
};
