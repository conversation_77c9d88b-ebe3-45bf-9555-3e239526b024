import { ImagePickerAsset } from 'expo-image-picker';
import { VideoThumbnailsResult } from 'expo-video-thumbnails';
import { fileToAppend } from '~api/useClient';

export default (data: ImagePickerAsset): VideoThumbnailsResult => {
  const file = fileToAppend(data, 'video/mp4');
  const time = 0.0;

  return new Promise(resolve => {
    const videoPlayer = document.createElement('video');
    videoPlayer.setAttribute('src', URL.createObjectURL(file));
    videoPlayer.load();

    videoPlayer.addEventListener('loadedmetadata', () => {
      setTimeout(() => {
        videoPlayer.currentTime = time;
      }, 200);

      videoPlayer.addEventListener('seeked', () => {
        const canvas = document.createElement('canvas');
        canvas.width = videoPlayer.videoWidth;
        canvas.height = videoPlayer.videoHeight;

        const ctx = canvas.getContext('2d');
        ctx?.drawImage(videoPlayer, 0, 0, canvas.width, canvas.height);

        resolve({
          type: 'image/jpeg',
          uri: ctx?.canvas.toDataURL('image/jpeg', 0.8),
        });
      });
    });
  });
};
