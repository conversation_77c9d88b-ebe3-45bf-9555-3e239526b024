import React, { ReactElement } from 'react';
import {
  Collapse,
  CollapseHeader,
  CollapseBody,
} from 'accordion-collapse-react-native';
import CollapsedTaskCardHeader from './CollapsedTaskCardHeader';

type Props = {
  title: string;
  enabled: boolean;
  children: ReactElement;
};

const Collapsible = ({ title, enabled, children }: Props): ReactElement => {
  if (!enabled) {
    return <>{children}</>;
  }

  return (
    <Collapse>
      <CollapseHeader>
        <CollapsedTaskCardHeader title={title} />
      </CollapseHeader>
      <CollapseBody>{children}</CollapseBody>
    </Collapse>
  );
};

export default Collapsible;
