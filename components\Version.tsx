import React from 'react';
import { Text, View } from 'react-native';
import useVersionInfo from '~hooks/useVersionInfo';

const VersionWeb = () => {
  const { needsUpdate, installedVersion, latestVersion, appStoreUrl } =
    useVersionInfo();

  return (
    <View style={{ marginTop: 32 }}>
      <Text>Needs update: {needsUpdate ? 'yes' : 'no'}</Text>
      <Text>Installed version: {installedVersion}</Text>
      <Text>Latest version: {latestVersion}</Text>
      <Text>AppStore URL: {appStoreUrl}</Text>
    </View>
  );
};

export default VersionWeb;
