import {
  <PERSON><PERSON>,
  Spinner,
  StyleService,
  useStyleSheet,
} from '@ui-kitten/components';
import React, { ReactNode, useContext, useEffect, useRef } from 'react';
import { Text } from 'react-native';
import Animated, { FadeInDown, FadeOutUp } from 'react-native-reanimated';
import pluralize from 'pluralize';
import {
  backgroundUploadColorBlue,
  backgroundUploadColorGreen,
  backgroundUploadColorRed,
} from '~constants/Colors';
import BackgroundUploadProgressContext, {
  BackgroundUploadStatuses,
  UploadWithStatus,
} from '~context/BackgroundUploadProgressContext';
import { View } from '~components/Themed';
import { Icon } from '~node_modules/@ui-kitten/components';
import { CloseIcon } from '~components/Icon';
import { handleBackgroundUploadStatus } from '~api/backgroundUpload';
import AuthContext from '~context/AuthContext';
import { useQueryClient } from '~node_modules/react-query';

const displayTimeInMS = 15000;

const uploadStatus = (
  uploads: UploadWithStatus[],
): {
  aggregateStatus: BackgroundUploadStatuses;
  backgroundColor: string;
  text: string;
  icon?: ReactNode;
} => {
  const startedFilesCount = uploads.filter(
    ({ status }) => status === BackgroundUploadStatuses.Started,
  ).length;

  if (startedFilesCount > 0) {
    return {
      aggregateStatus: BackgroundUploadStatuses.Started,
      backgroundColor: backgroundUploadColorBlue,
      text: `Uploading ${startedFilesCount} ${pluralize(
        'file',
        startedFilesCount,
      )}.`,
      icon: <Spinner status="basic" />,
    };
  }

  if (
    uploads.some(({ status }) => status === BackgroundUploadStatuses.Failed)
  ) {
    return {
      aggregateStatus: BackgroundUploadStatuses.Failed,
      backgroundColor: backgroundUploadColorRed,
      text: 'File upload failed.',
    };
  }

  return {
    aggregateStatus: BackgroundUploadStatuses.Finished,
    backgroundColor: backgroundUploadColorGreen,
    text: 'Upload completed.',
    icon: <Icon fill="#FFFFFF" style={themedStyle.icon} name="checkmark" />,
  };
};

const BackgroundUploadOverlay = (): ReactElement | null => {
  const styles = useStyleSheet(themedStyle);
  const { authToken } = useContext(AuthContext);
  const queryClient = useQueryClient();

  const { uploads, setUploads } = useContext(BackgroundUploadProgressContext);
  const backgroundUploadStatus = uploadStatus(uploads);

  const timeout = useRef<NodeJS.Timeout>();

  useEffect(() => {
    const allUploadsAreFinished =
      uploads.length > 0 &&
      uploadStatus(uploads).aggregateStatus ===
        BackgroundUploadStatuses.Finished;

    if (allUploadsAreFinished) {
      timeout.current = setTimeout(() => setUploads([]), displayTimeInMS);
    } else {
      clearTimeout(timeout.current);
    }
  }, [setUploads, uploads]);

  if (!uploads.length || !backgroundUploadStatus) {
    return null;
  }

  const { aggregateStatus, backgroundColor, text, icon } =
    backgroundUploadStatus;

  const handlePressRetry = () => {
    const failedUploads = uploads
      .filter(({ status }) => status === BackgroundUploadStatuses.Failed)
      .map(({ status, ...upload }) => upload.backgroundUpload);
    handleBackgroundUploadStatus(
      failedUploads,
      authToken,
      setUploads,
      queryClient,
    );
  };

  return (
    <Animated.View
      entering={FadeInDown}
      exiting={FadeOutUp}
      style={[styles.container, { backgroundColor }]}
    >
      {icon && <View style={styles.iconContainer}>{icon}</View>}
      <Text style={styles.text}>{text}</Text>
      {aggregateStatus === BackgroundUploadStatuses.Finished && (
        <Button
          style={styles.closeButton}
          size="medium"
          appearance="ghost"
          status="control"
          accessoryLeft={CloseIcon}
          onPress={() => setUploads([])}
        />
      )}
      {aggregateStatus === BackgroundUploadStatuses.Failed && (
        <Button
          style={styles.retryButton}
          size="tiny"
          appearance="outline"
          status="control"
          onPress={handlePressRetry}
        >
          Retry
        </Button>
      )}
    </Animated.View>
  );
};

export default BackgroundUploadOverlay;

const themedStyle = StyleService.create({
  container: {
    zIndex: 3, // works on ios
    elevation: 3, // works on android
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    width: '100%',
    height: 50,
    paddingBottom: 10,
    top: 0,
  },
  text: {
    fontSize: 16,
    color: '#fff',
  },
  views: {
    height: 400,
  },
  iconContainer: {
    marginRight: 10,
    backgroundColor: 'transparent',
  },
  icon: {
    width: 20,
    height: 20,
  },
  closeButton: {
    position: 'absolute',
    right: 10,
    paddingBottom: 23,
    margin: 0,
  },
  retryButton: {
    marginLeft: 10,
  },
});
