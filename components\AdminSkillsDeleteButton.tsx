import React, { useState } from 'react';
import { Button } from '@ui-kitten/components';
import Confirm from './Confirm';

type Props = {
  id: number;
  onConfirm: (id: number) => void;
};

const AdminSkillsDeleteButton = ({ id, onConfirm }: Props) => {
  const [showConfirmDelete, setShowConfirmDelete] = useState(false);

  return (
    <>
      <Button size="tiny" onPress={() => setShowConfirmDelete(true)}>
        Delete
      </Button>
      <Confirm
        show={showConfirmDelete}
        onCancel={() => setShowConfirmDelete(false)}
        onConfirm={() => {
          onConfirm(id);
          setShowConfirmDelete(false);
        }}
        text="Are you sure you want to delete this skill from your team?"
      />
    </>
  );
};

export default AdminSkillsDeleteButton;
