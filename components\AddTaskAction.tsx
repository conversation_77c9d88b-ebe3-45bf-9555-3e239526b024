import { Button } from '@ui-kitten/components';
import React from 'react';
import { StyleSheet } from 'react-native';
import { PlusIcon } from '~components/Icon';

const AddTaskAction = ({ onPress }: { onPress: () => void }) => (
  <Button
    style={styles.button}
    status="primary"
    accessoryLeft={PlusIcon}
    onPress={onPress}
    size="small"
  />
);

const styles = StyleSheet.create({
  button: {
    marginTop: -5,
    marginHorizontal: 15,
  },
});

export default AddTaskAction;
