import React from 'react';
import {
  Button,
  Input,
  StyleService,
  useStyleSheet,
} from '@ui-kitten/components';
import { Formik } from 'formik';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import * as Yup from 'yup';
import { useToast } from 'react-native-toast-notifications';
import useUser from '~api/useUser';
import SpinnerButton from '~components/SpinnerButton';

const schema = Yup.object().shape({
  name: Yup.string().required('Required'),
});

export default ({ navigation, route }): React.ReactElement => {
  const toast = useToast();
  const styles = useStyleSheet(themedStyle);

  const queryClient = useQueryClient();
  const { getUser, putAccount } = useUser();
  const accountId = route?.params?.accountId;
  const { data: user } = useQuery('user', () => getUser());
  const name = user?.accounts?.[0]?.name;

  const { mutate, isLoading } = useMutation(
    data => putAccount(accountId, data),
    {
      onSuccess: async () => {
        toast.show(`You have succesfully updated the name of your account`);
        await queryClient.invalidateQueries('user');
        navigation.goBack();
      },
    },
  );

  if (!user) {
    return null;
  }

  const initialData = { name };

  const save = async values => {
    await mutate(values);
  };

  return (
    <Formik
      initialValues={initialData}
      onSubmit={save}
      enableReinitialize
      validationSchema={schema}
      validateOnMount
    >
      {({ values, setFieldValue, handleSubmit, isValid }) => (
        <>
          <Input
            style={styles.input}
            label="Account Name"
            value={values.name}
            onChangeText={value => setFieldValue('name', value)}
          />
          <SpinnerButton
            text="Save"
            style={styles.saveButton}
            onPress={handleSubmit}
            isLoading={isLoading}
            disabled={!isValid}
          />
          <Button
            style={styles.saveButton}
            appearance="outline"
            size="giant"
            onPress={() => navigation.goBack()}
          >
            Cancel
          </Button>
        </>
      )}
    </Formik>
  );
};

const themedStyle = StyleService.create({
  profileSetting: {
    padding: 16,
  },
  saveButton: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  heading: {
    fontSize: 22,
    margin: 16,
  },
  input: {
    margin: 16,
  },
  roleContent: {
    paddingTop: 20,
    flexDirection: 'column',
  },
  roleName: {
    fontWeight: 'bold',
  },
  roleDescription: {
    color: 'gray',
  },
});
