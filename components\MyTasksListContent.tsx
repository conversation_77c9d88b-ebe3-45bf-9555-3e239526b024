import React, { ReactElement } from 'react';
import { ListRenderItemInfo } from 'react-native';
import { List, StyleService } from '@ui-kitten/components';
import PlaceholderCard from './PlaceholderCard';
import Spinner from './Spinner';
import { Task, TaskAssignment } from '../types';
import { MyTasksDescription } from '~constants/Descriptions';
import Collapsible from './Collapsible';
import TaskAssignmentCard from './TaskAssignmentCard';

type Props = {
  navigation: any;
  taskAssignments: any;
  isLoading: boolean;
  compactModeEnabled: boolean;
};

const MyTasksListContent = ({
  navigation,
  taskAssignments,
  isLoading,
  compactModeEnabled,
}: Props): ReactElement => {
  const renderItem = (info: ListRenderItemInfo<Task>): React.ReactElement => {
    const taskAssignment: TaskAssignment = info.item;

    return (
      <Collapsible
        title={taskAssignment.task.description}
        enabled={compactModeEnabled}
      >
        <TaskAssignmentCard
          taskAssignment={taskAssignment}
          onPress={() =>
            navigation.push('TaskAssignmentScreen', { id: taskAssignment.id })
          }
        />
      </Collapsible>
    );
  };

  if (isLoading) {
    return <Spinner />;
  }

  if (!taskAssignments?.length) {
    return (
      <PlaceholderCard
        text={
          <MyTasksDescription
            onPressAddTask={() => navigation.navigate('AddTaskScreen')}
          />
        }
        cta="Go to Available Jobs"
        onPress={() =>
          navigation.navigate('Available Jobs', { screen: 'Available Jobs' })
        }
        icon="list"
      />
    );
  }

  return (
    <List
      contentContainerStyle={styles.list}
      data={taskAssignments}
      renderItem={renderItem}
    />
  );
};

const styles = StyleService.create({
  addButtonText: { marginLeft: 10 },
  addButton: { flexDirection: 'row', alignItems: 'center' },
  list: { marginTop: 16 },
});

export default MyTasksListContent;
