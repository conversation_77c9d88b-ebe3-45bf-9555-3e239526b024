import React, { ReactElement } from 'react';
import { List, StyleService, Text, useStyleSheet } from '@ui-kitten/components';
import { ListRenderItemInfo, View } from 'react-native';
import { isEqual, startOfDay } from 'date-fns';
import useTimezone from '~hooks/useTimezone';
import { PropertyTask, TaskGroup } from '~types';

type Props = {
  taskGroup: TaskGroup;
  renderPropertyItem: (
    info: ListRenderItemInfo<PropertyTask>,
    date: string | number,
  ) => React.ReactElement;
};

const DayBlock = ({ taskGroup, renderPropertyItem }: Props): ReactElement => {
  const styles = useStyleSheet(themedStyles);
  const { formatInTimeZone, zonedTimeToUtc } = useTimezone();
  const { date, properties } = taskGroup;

  return (
    <View style={styles.blockContainer}>
      <Text style={styles.heading}>
        Available jobs for{' '}
        {isEqual(zonedTimeToUtc(startOfDay(new Date())), new Date(date))
          ? 'Today'
          : formatInTimeZone(date, 'PP')}
      </Text>
      <List
        data={properties}
        renderItem={propertyItem => renderPropertyItem(propertyItem, date)}
      />
    </View>
  );
};

const themedStyles = StyleService.create({
  blockContainer: { marginTop: 8, marginVertical: 32 },
  heading: {
    fontSize: 20,
    fontWeight: '800',
    marginBottom: 8,
    color: '#333',
  },
});

export default DayBlock;
