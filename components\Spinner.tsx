import { StyleService, useStyleSheet } from '@ui-kitten/components';
import React from 'react';
import { ActivityIndicator, View } from 'react-native';

const Spinner = () => {
  const localStyles = useStyleSheet(themedStyle);

  return (
    <View style={localStyles.container}>
      <ActivityIndicator size="large" color="#999999" />
    </View>
  );
};

export default Spinner;

const themedStyle = StyleService.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignSelf: 'center',
    width: '100%',
    height: 200,
  },
});
