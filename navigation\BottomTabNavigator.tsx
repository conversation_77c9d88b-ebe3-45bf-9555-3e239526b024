/**
 * Learn more about createBottomTabNavigator:
 * https://reactnavigation.org/docs/bottom-tab-navigator
 */

import { Ionicons } from '@expo/vector-icons';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import * as React from 'react';

import Colors from '../constants/Colors';
import useColorScheme from '../hooks/useColorScheme';
import TasksList from '../screens/TasksList';

import {
  BottomTabParamList,
  MyTasksParamList,
  AvailableTasksParamList,
  MyInvoicesParamList,
  ProfileParamList,
  MoreParamList,
  HouseKeepingParamList,
} from '../types';
import TaskDetails from '../screens/TaskDetails';
import AddTask from '../screens/AddTask';
import TaskSettings from '~screens/TaskSettings';
import PropertiesList from '~screens/PropertiesList';
import PropertyDetails from '~screens/PropertyDetails';
import PropertyForm from '~screens/PropertyForm';
import Profile from '~screens/Profile';
import FinishTask from '~screens/FinishTask';
import InvoiceList from '~screens/InvoiceList/InvoiceList';
import MyTasksList from '~screens/MyTasksList';
import TaskAssignmentDetails from '~screens/TaskAssignmentDetails';
import PersonalInformation from '~screens/PersonalInformation';
import Skills from '~screens/Skills';
import Team from '~screens/Team';
import ChangePassword from '~screens/ChangePassword';
import MemberRoleForm from '~screens/MemberRoleForm';
import { useQuery } from '~node_modules/react-query';
import useUser from '~api/useUser';
import AccountForm from '~screens/AccountForm';
import Admin from '~screens/Admin';
import InvoicesMenuScreen from '~screens/InvoicesMenuScreen';
import LogPayment from '~screens/LogPayment';
import PaymentDetails from '~screens/PaymentDetails';
import ProfitMargin from '~screens/ProfitMargin';
import UnpaidInvoices from '~screens/UnpaidInvoices';
import RecommendedTaskListing from '~screens/RecommendedTaskListing';
import TitleWithHint from '~components/TitleWithHint';
import {
  AVAILABLE_TASKS_DESCRIPTION,
  MY_TASKS_DESCRIPTION,
  SKILLS_DESCRIPTION,
} from '~constants/Descriptions';
import Support from '~screens/Support';
import CalendarScreen from '~screens/CalendarScreen';
import TaskHistory from '~screens/TaskHistory';
import More from '~screens/More';
import Activities from '~screens/Activities';
import InvoicePDFScreen from '~screens/InvoicePDFScreen';
import AdminSkillsScreen from '~screens/AdminSkillsScreen';
import AddSkillScreen from '~screens/AddSkillScreen';
import AllTasksScreen from '~screens/AllTasksScreen';
import UnblockTask from '~screens/UnblockTask';
import HouseKeepingScreen from '~screens/HouseKeeping';

const BottomTab = createBottomTabNavigator<BottomTabParamList>();

function MyJobsTabBarIcon({ color }) {
  return <TabBarIcon name="list-circle" color={color} />;
}

function AvailableJobsTabBarIcon({ color }) {
  return <TabBarIcon name="list-circle-outline" color={color} />;
}

function MyInvoicesTabBarIcon({ color }) {
  return <TabBarIcon name="document-text-outline" color={color} />;
}

function ProfileTabBarIcon({ color }) {
  return <TabBarIcon name="person-outline" color={color} />;
}

function HousekeepingTabBarIcon({ color }) {
  return <TabBarIcon name="checkmark-circle-outline" color={color} />;
}

function MoreTabBarIcon({ color }) {
  return <TabBarIcon name="menu-outline" color={color} />;
}

export default function BottomTabNavigator() {
  const colorScheme = useColorScheme();

  return (
    <BottomTab.Navigator
      initialRouteName="My Jobs"
      tabBarOptions={{ activeTintColor: Colors[colorScheme].tint }}
      screenOptions={{
        headerShown: false,
      }}
    >
      <BottomTab.Screen
        name="My Jobs"
        component={MyTasksNavigator}
        options={{
          tabBarIcon: MyJobsTabBarIcon,
        }}
      />
      <BottomTab.Screen
        name="Available Jobs"
        component={AvailableTasksNavigator}
        options={{
          tabBarIcon: AvailableJobsTabBarIcon,
        }}
      />
      <BottomTab.Screen
        name="My Invoices"
        component={MyInvoicesNavigator}
        options={{
          tabBarIcon: MyInvoicesTabBarIcon,
        }}
      />
      <BottomTab.Screen
        name="House Keeping"
        component={HouseKeepingNavigator}
        options={{
          tabBarIcon: HousekeepingTabBarIcon,
        }}
      />
      <BottomTab.Screen
        name="Profile"
        component={ProfileNavigator}
        options={{
          tabBarIcon: ProfileTabBarIcon,
        }}
      />
      <BottomTab.Screen
        name="More"
        component={MoreNavigator}
        options={{
          tabBarIcon: MoreTabBarIcon,
        }}
      />
    </BottomTab.Navigator>
  );
}

// You can explore the built-in icon families and icons on the web at:
// https://icons.expo.fyi/
function TabBarIcon(props: {
  name: React.ComponentProps<typeof Ionicons>['name'];
  color: string;
}) {
  return <Ionicons size={30} style={{ marginBottom: -3 }} {...props} />;
}

const renderAddOrEditTaskBlock = (stack, ElementToRender, screenName) => (
  <stack.Screen
    name={screenName}
    options={({ route }) => ({
      title: route.params?.taskId ? 'Edit Job' : 'Add Job',
    })}
  >
    {props => <ElementToRender {...props} />}
  </stack.Screen>
);

// Each tab has its own navigation stack, you can read more about this pattern here:
// https://reactnavigation.org/docs/tab-based-navigation#a-stack-navigator-for-each-tab
const MyTasksStack = createStackNavigator<MyTasksParamList>();

function MyTasksHeaderTitle() {
  return <TitleWithHint text="My Jobs" hint={MY_TASKS_DESCRIPTION} />;
}

function AvailableTasksHeaderTitle() {
  return (
    <TitleWithHint text="Available Jobs" hint={AVAILABLE_TASKS_DESCRIPTION} />
  );
}

function SkillsHeaderTitle() {
  return <TitleWithHint text="Skills" hint={SKILLS_DESCRIPTION} />;
}

function MyTasksNavigator() {
  const { getUser } = useUser();
  const { data: user } = useQuery('user', () => getUser());
  const currentUserRole = user?.accounts[0].pivot.role;

  return (
    <MyTasksStack.Navigator>
      <MyTasksStack.Screen
        name="MyTasksScreen"
        options={{
          headerTitle: MyTasksHeaderTitle,
        }}
      >
        {props => <MyTasksList {...props} />}
      </MyTasksStack.Screen>
      <MyTasksStack.Screen
        name="TaskAssignmentScreen"
        component={TaskAssignmentDetails}
        options={{ headerTitle: 'Job Details' }}
      />
      <MyTasksStack.Screen
        name="TaskScreen"
        component={TaskDetails}
        options={{ headerTitle: 'Job Details' }}
      />
      <MyTasksStack.Screen
        name="FinishTaskScreen"
        component={FinishTask}
        options={{ headerTitle: 'Finish Job' }}
      />
      <MyTasksStack.Screen
        name="TaskHistoryScreen"
        component={TaskHistory}
        options={{ headerTitle: 'Task History' }}
      />
      {currentUserRole !== 2 && (
        <>
          {renderAddOrEditTaskBlock(MyTasksStack, AddTask, 'AddTaskScreen')}
          {renderAddOrEditTaskBlock(
            MyTasksStack,
            TaskSettings,
            'TaskSettingsScreen',
          )}
        </>
      )}
    </MyTasksStack.Navigator>
  );
}

const AvailableTasksStack = createStackNavigator<AvailableTasksParamList>();

function AvailableTasksNavigator() {
  const { getUser } = useUser();
  const { data: user } = useQuery('user', () => getUser());

  const currentUserRole = user?.accounts[0].pivot.role;

  return (
    <AvailableTasksStack.Navigator initialRouteName="AvailableTasksScreen">
      <AvailableTasksStack.Screen
        name="AvailableTasksScreen"
        options={{
          headerTitle: AvailableTasksHeaderTitle,
        }}
      >
        {props => <TasksList {...props} filter="available" />}
      </AvailableTasksStack.Screen>
      <AvailableTasksStack.Screen
        name="TaskScreen"
        component={TaskDetails}
        options={{ headerTitle: 'Job Details' }}
        initialParams={{ id: 0, copiedForToday: false }}
      />
      {currentUserRole !== 2 && (
        <>
          {renderAddOrEditTaskBlock(
            AvailableTasksStack,
            AddTask,
            'AddTaskScreen',
          )}
          {renderAddOrEditTaskBlock(
            AvailableTasksStack,
            TaskSettings,
            'TaskSettingsScreen',
          )}
        </>
      )}
      <AvailableTasksStack.Screen
        name="TaskHistoryScreen"
        component={TaskHistory}
        options={{ headerTitle: 'Task History' }}
      />
      <AvailableTasksStack.Screen
        name="RecommendedTaskListingScreen"
        component={RecommendedTaskListing}
        options={{ headerTitle: 'Recommended Jobs' }}
        initialParams={{
          headerTitle: 'Recommended Jobs',
          compactModeEnabled: false,
        }}
      />
      <AvailableTasksStack.Screen
        name="UnblockTaskScreen"
        component={UnblockTask}
        options={{ headerTitle: 'Unblock Job' }}
      />
    </AvailableTasksStack.Navigator>
  );
}

const MyInvoicesStack = createStackNavigator<MyInvoicesParamList>();

function MyInvoicesNavigator() {
  return (
    <MyInvoicesStack.Navigator>
      <MyInvoicesStack.Screen
        name="MyInvoicesScreen"
        options={{
          headerTitle: 'My Invoices',
        }}
      >
        {props => <InvoiceList {...props} />}
      </MyInvoicesStack.Screen>
      <MyInvoicesStack.Screen
        name="TaskAssignmentScreen"
        component={TaskAssignmentDetails}
        options={{ headerTitle: 'Job Details' }}
      />
      <MyInvoicesStack.Screen
        name="FinishTaskScreen"
        component={FinishTask}
        options={{ headerTitle: 'Finish Job' }}
      />
      <MyInvoicesStack.Screen
        name="PaymentDetailsScreen"
        component={PaymentDetails}
        options={{ headerTitle: 'Payment Details' }}
      />
      <MyInvoicesStack.Screen
        name="LogPaymentScreen"
        component={LogPayment}
        options={{ headerTitle: 'Log Payment' }}
      />
    </MyInvoicesStack.Navigator>
  );
}

const ProfileStack = createStackNavigator<ProfileParamList>();

function ProfileNavigator() {
  return (
    <ProfileStack.Navigator>
      <ProfileStack.Screen
        name="ProfileScreen"
        component={Profile}
        options={{ headerTitle: 'Profile' }}
      />
      <ProfileStack.Screen
        name="PersonalInformationScreen"
        component={PersonalInformation}
        options={{ headerTitle: 'Personal Information' }}
      />
      <ProfileStack.Screen
        name="SkillsScreen"
        component={Skills}
        options={{
          headerTitle: SkillsHeaderTitle,
        }}
      />
      <ProfileStack.Screen
        name="PasswordScreen"
        component={ChangePassword}
        options={{ headerTitle: 'Change Password' }}
      />
    </ProfileStack.Navigator>
  );
}

const HouseKeepingStack = createStackNavigator<HouseKeepingParamList>();

function HouseKeepingNavigator() {
  return (
    <HouseKeepingStack.Navigator>
      <HouseKeepingStack.Screen
        name="HouseKeepingScreen"
        component={HouseKeepingScreen}
        options={{ headerTitle: 'House Keeping' }}
      />
    </HouseKeepingStack.Navigator>
  );
}

const MoreStack = createStackNavigator<MoreParamList>();

function MoreNavigator() {
  return (
    <MoreStack.Navigator>
      <MoreStack.Screen
        name="MoreScreen"
        component={More}
        options={{ headerTitle: 'More' }}
      />
      <MoreStack.Screen
        name="AdminScreen"
        component={Admin}
        options={{ headerTitle: 'Admin' }}
      />
      <MoreStack.Screen
        name="TeamScreen"
        component={Team}
        options={{ headerTitle: 'Team' }}
      />
      <MoreStack.Screen
        name="MemberScreen"
        component={MemberRoleForm}
        options={{ headerTitle: 'Invite new member' }}
      />
      <MoreStack.Screen
        name="AccountEditScreen"
        component={AccountForm}
        options={{ headerTitle: 'Edit Account Name' }}
      />
      <MoreStack.Screen
        name="AllTasksScreen"
        component={AllTasksScreen}
        options={{
          headerTitle: 'All Jobs',
        }}
      />
      <MoreStack.Screen
        name="TaskScreen"
        component={TaskDetails}
        options={{ headerTitle: 'Job Details' }}
      />
      {renderAddOrEditTaskBlock(MoreStack, AddTask, 'AddTaskScreen')}
      {renderAddOrEditTaskBlock(MoreStack, TaskSettings, 'TaskSettingsScreen')}
      <MoreStack.Screen
        name="PropertiesScreen"
        options={{
          headerTitle: 'Properties',
        }}
      >
        {props => <PropertiesList {...props} />}
      </MoreStack.Screen>
      <MoreStack.Screen
        name="PropertyScreen"
        component={PropertyDetails}
        options={{ headerTitle: 'Property Details' }}
      />
      <MoreStack.Screen
        name="CalendarScreen"
        component={CalendarScreen}
        options={{ headerTitle: 'Calendar' }}
      />
      <MoreStack.Screen
        name="TaskAssignmentScreen"
        component={TaskAssignmentDetails}
        options={{ headerTitle: 'Job Details' }}
      />
      <MoreStack.Screen
        name="FinishTaskScreen"
        component={FinishTask}
        options={{ headerTitle: 'Finish Job' }}
      />
      <MoreStack.Screen
        name="InvoicesMenuScreen"
        component={InvoicesMenuScreen}
        options={{ headerTitle: 'Invoices' }}
      />
      <MoreStack.Screen
        name="InvoicesScreen"
        options={{
          headerTitle: 'Invoices',
        }}
      >
        {props => <InvoiceList {...props} />}
      </MoreStack.Screen>
      <MoreStack.Screen name="PropertyFormScreen" component={PropertyForm} />
      <MoreStack.Screen
        name="LogPaymentScreen"
        component={LogPayment}
        options={{ headerTitle: 'Log Payment' }}
      />
      <MoreStack.Screen
        name="PaymentDetailsScreen"
        component={PaymentDetails}
        options={{ headerTitle: 'Payment Details' }}
      />
      <MoreStack.Screen
        name="ProfitMarginScreen"
        component={ProfitMargin}
        options={{ headerTitle: 'Mark Up' }}
      />
      <MoreStack.Screen
        name="UnpaidInvoicesScreen"
        component={UnpaidInvoices}
        options={{ headerTitle: 'Unpaid Invoices' }}
      />
      <MoreStack.Screen
        name="TaskHistoryScreen"
        component={TaskHistory}
        options={{ headerTitle: 'Job History' }}
      />
      <MoreStack.Screen
        name="ActivitiesScreen"
        component={Activities}
        options={{ headerTitle: 'Activity' }}
      />
      <MoreStack.Screen
        name="InvoicePDFScreen"
        component={InvoicePDFScreen}
        options={{ headerTitle: 'PDF' }}
      />
      <MoreStack.Screen
        name="AdminSkillsScreen"
        component={AdminSkillsScreen}
        options={{ headerTitle: 'Skills' }}
      />
      <MoreStack.Screen
        name="AddSkillScreen"
        component={AddSkillScreen}
        options={{ headerTitle: 'Add Skill' }}
      />
      <MoreStack.Screen
        name="UnblockTaskScreen"
        component={UnblockTask}
        options={{ headerTitle: 'Unblock Job' }}
      />
      <MoreStack.Screen
        name="SupportScreen"
        component={Support}
        options={{ headerTitle: 'Support' }}
      />
    </MoreStack.Navigator>
  );
}
