import React from 'react';
import { ScrollView } from 'react-native';
import {
  Button,
  Input,
  StyleService,
  useStyleSheet,
} from '@ui-kitten/components';
import { Formik } from 'formik';
import _ from 'lodash';
import { useQueryClient } from 'react-query';
import * as Yup from 'yup';
import { ProfileSetting } from '~components/ProfileSetting';
import { useMutation, useQuery } from '~node_modules/react-query';
import useUser from '~api/useUser';
import SpinnerButton from '~components/SpinnerButton';
import CustomPhoneInput from '~components/CustomPhoneInput';

const phoneRegExp =
  /^\s*(?:\+?(\d{1,3}))?[-. (]*(\d{3})[-. )]*(\d{3})[-. ]*(\d{4})(?: *x(\d+))?\s*$/;

const schema = Yup.object().shape({
  name: Yup.string().required('Required'),
  email: Yup.string().email().required('Required'),
  phone: Yup.string()
    .matches(phoneRegExp, 'Phone number is not valid')
    .required('Required'),
});

export default ({ navigation }): React.ReactElement => {
  const styles = useStyleSheet(themedStyle);
  const { getUser, updateUser } = useUser();
  const { isLoading, data: user } = useQuery('user', () => getUser());
  const queryClient = useQueryClient();

  const { mutate: updateUserMutate, isLoading: saveUserIsLoading } =
    useMutation(updateUser, {
      onSuccess: () => {
        queryClient.invalidateQueries('user');
        navigation.goBack();
      },
    });

  const save = values => {
    updateUserMutate(values);
  };

  if (isLoading || !user) {
    return null;
  }

  const initialValues = _.pick(user, [
    'name',
    'email',
    'phone',
    'address',
    'taxId',
    'phoneCountryCode',
  ]);

  return (
    <Formik
      initialValues={initialValues}
      onSubmit={save}
      validationSchema={schema}
      validateOnMount
    >
      {({ values, setFieldValue, handleSubmit, isValid }) => (
        <>
          <ScrollView
            style={styles.container}
            contentContainerStyle={styles.contentContainer}
          >
            <ProfileSetting
              style={[styles.profileSetting]}
              hint="Name"
              value={
                <Input
                  style={styles.input}
                  placeholder="Name"
                  value={values.name}
                  onChangeText={value => setFieldValue('name', value)}
                />
              }
            />
            <ProfileSetting
              style={[styles.profileSetting]}
              hint="Email"
              value={
                <Input
                  style={styles.input}
                  placeholder="Email"
                  value={values.email}
                  onChangeText={value => setFieldValue('email', value)}
                />
              }
            />
            <ProfileSetting
              style={styles.profileSetting}
              hint="Phone Number"
              value={
                <CustomPhoneInput
                  name="phone"
                  initialPhoneValue={values.phone}
                />
              }
            />
            <ProfileSetting
              style={styles.profileSetting}
              hint="Address"
              value={
                <Input
                  multiline
                  numberOfLines={5}
                  style={[styles.input, styles.addressInput]}
                  placeholder="Address"
                  value={values.address}
                  onChangeText={value => setFieldValue('address', value)}
                />
              }
            />
            <ProfileSetting
              style={styles.profileSetting}
              hint="Tax ID"
              value={
                <Input
                  style={styles.input}
                  placeholder="Tax ID"
                  value={values.taxId}
                  onChangeText={value => setFieldValue('taxId', value)}
                />
              }
            />
          </ScrollView>
          <SpinnerButton
            style={styles.saveButton}
            text="Save Personal Information"
            onPress={handleSubmit}
            isLoading={saveUserIsLoading}
            disabled={!isValid}
          />
          <Button
            style={styles.saveButton}
            appearance="outline"
            size="giant"
            onPress={() => navigation.goBack()}
          >
            Cancel
          </Button>
        </>
      )}
    </Formik>
  );
};

const themedStyle = StyleService.create({
  container: {
    flex: 1,
    backgroundColor: 'background-basic-color-2',
  },
  contentContainer: {
    paddingVertical: 24,
  },
  profileAvatar: {
    aspectRatio: 1.0,
    height: 124,
    alignSelf: 'center',
  },
  editAvatarButton: {
    aspectRatio: 1.0,
    height: 48,
    borderRadius: 24,
  },
  profileSetting: {
    padding: 16,
  },
  section: {
    marginTop: 24,
  },
  saveButton: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  addressInput: {
    maxHeight: 120,
  },
});
