import { UserRoles } from '~dummyData';
import useUser from '~queries/useUser';

const useCurrentUser = () => {
  const { data: currentUser } = useUser();

  const currentUserRole = currentUser?.accounts[0].pivot.role;

  const isAdmin = currentUserRole === UserRoles.Admin;
  const isManager = currentUserRole === UserRoles.Manager;
  const isTechnician = currentUserRole === UserRoles.Worker;

  return { currentUser, isAdmin, isManager, isTechnician };
};

export default useCurrentUser;
