import React, { ReactElement } from 'react';
import {
  Text,
  useStyleSheet,
  StyleService,
  RadioGroup,
  Radio,
} from '@ui-kitten/components';
import { View } from 'react-native';
import { Field } from 'formik';

type Props = {
  options: Array<{
    value: string | number;
    label: JSX.Element;
  }>;
  name: string;
  formLabel: string;
  onSelect: (index: number) => void;
};

const RadioGroupInput = ({
  options,
  name,
  formLabel,
  onSelect,
}: Props): ReactElement => {
  const styles = useStyleSheet(themedStyles);

  return (
    <Field>
      {({ form: { values, setFieldValue } }) => {
        const selectedIndex = options.findIndex(
          field => field.value === values[name],
        );

        return (
          <View style={styles.container}>
            <Text style={styles.label}>{formLabel}</Text>
            <RadioGroup
              selectedIndex={selectedIndex}
              onChange={(index: number) => {
                const { value } = options[index];
                if (onSelect) {
                  onSelect(index);
                }
                setFieldValue(name, value);
              }}
            >
              {options.map(({ value, label }) => (
                <Radio key={value}>{label}</Radio>
              ))}
            </RadioGroup>
          </View>
        );
      }}
    </Field>
  );
};

const themedStyles = StyleService.create({
  container: {
    marginHorizontal: 12,
  },
  label: {
    marginTop: 8,
    fontWeight: '800',
    color: '#8F9BB3',
    fontSize: 12,
    fontFamily: 'System',
  },
  tooltip: {
    backgroundColor: 'white',
    borderColor: '#999',
  },
  tooltipText: {
    paddingHorizontal: 5,
    paddingVertical: 10,
    fontSize: 15,
    fontWeight: 'normal',
    color: '#666',
    lineHeight: 20,
  },
  button: {
    width: 0,
  },
});

export default RadioGroupInput;
