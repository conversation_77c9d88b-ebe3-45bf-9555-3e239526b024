import React, { ReactElement, useContext, useState } from 'react';
import { View, Pressable } from 'react-native';
import {
  Button,
  Input,
  Layout,
  StyleService,
  useStyleSheet,
  Icon,
  Text,
} from '@ui-kitten/components';
import { Formik } from 'formik';
import _ from 'lodash';
import * as Yup from 'yup';
import { useMutation } from 'react-query';
import KeyboardAvoidingView from '~components/KeyboardAvoidingView';
import { EmailIcon, PersonIcon } from '~components/Icon';
import { accountRoles } from '~dummyData';
import { authenticate } from '~services/auth';
import AuthContext from '~context/AuthContext';
import useClient from '~api';
import SelectInput from '~components/SelectInput';
import SpinnerButton from '~components/SpinnerButton';
import CustomPhoneInput from '~components/CustomPhoneInput';
import useRegisterForPushNotifications, {
  Modes,
} from '~hooks/useRegisterForPushNotifications';
import TitleWithHint from '~components/TitleWithHint';
import InputValidationWrapper from '~components/InputValidationWrapper';
import ValidationMessages from '~constants/ValidationMessages';

const appName = process.env.APP_NAME;

const phoneRegExp =
  /^\s*(?:\+?(\d{1,3}))?[-. (]*(\d{3})[-. )]*(\d{3})[-. ]*(\d{4})(?: *x(\d+))?\s*$/;

const schema = Yup.object().shape({
  name: Yup.string().required(ValidationMessages.required),
  email: Yup.string()
    .email(ValidationMessages.emailMustBeValid)
    .required(ValidationMessages.required),
  phone: Yup.string()
    .matches(phoneRegExp, ValidationMessages.phoneNumberIsNotValid)
    .required(ValidationMessages.required),
  password: Yup.string()
    .min(6, ValidationMessages.passwordMustBe6Characters)
    .required(ValidationMessages.required),
  invitationCode: Yup.string().when('role', {
    is: 0,
    then: Yup.string().required(ValidationMessages.invitationCodeRequired),
    otherwise: Yup.string(),
  }),
  accountName: Yup.string().when('role', {
    is: 1,
    then: Yup.string().required(ValidationMessages.required),
    otherwise: Yup.string(),
  }),
});

export default ({ navigation, route }): React.ReactElement => {
  const [passwordVisible, setPasswordVisible] = React.useState<boolean>(false);
  const { setAuthToken } = useContext(AuthContext);
  const { signUp } = useClient();
  const { registerForPush } = useRegisterForPushNotifications();

  const styles = useStyleSheet(themedStyles);

  const { mutate, isLoading } = useMutation(signUp);

  const handleSignUp = values => {
    mutate(_.pickBy(values), {
      onSuccess: async data => {
        const { token } = data.data;
        await authenticate(token);
        setAuthToken(token);
        registerForPush(Modes.Subscribe, token);
        if (values.invitationCode) {
          navigation.navigate('SkillsAfterRegistrationScreen');
        } else {
          navigation.navigate('ActivateAccountScreen');
        }
      },
    });
  };

  const onSignInButtonPress = (): void => {
    if (navigation) {
      navigation.navigate('SignIn');
    }
  };

  const onPasswordIconPress = (): void => {
    setPasswordVisible(!passwordVisible);
  };

  const renderPasswordIcon = (props): ReactElement => (
    <Pressable onPress={onPasswordIconPress}>
      <Icon {...props} name={passwordVisible ? 'eye-off' : 'eye'} />
    </Pressable>
  );

  const [isAccountNameEdited, setIsAccountNameEdited] = useState(false);
  const [isSubmitButtonPressed, setIsSubmitButtonPressed] = useState(false);

  return (
    <Formik
      initialValues={{
        name: '',
        email: '',
        password: '',
        role: 0,
        invitationCode: route?.params?.token || '',
        accountName: '',
      }}
      onSubmit={handleSignUp}
      validationSchema={schema}
      validateOnChange={isSubmitButtonPressed}
      validateOnBlur={false}
    >
      {({
        handleSubmit,
        values,
        setFieldValue,
        errors: {
          email: emailError,
          invitationCode: invitationCodeError,
          name: nameError,
          password: passwordError,
          accountName: accountNameError,
          phone: phoneError,
        },
        errors,
      }) => (
        <KeyboardAvoidingView style={styles.container}>
          <View style={styles.headerContainer}>
            <Text category="h1" status="control">
              {appName}
            </Text>
            <Text style={styles.signUpLabel} category="s1" status="control">
              Create a new Account
            </Text>
          </View>
          <Layout style={styles.formContainer} level="1">
            <InputValidationWrapper errorMessage={errors.name}>
              <Input
                status={nameError && 'danger'}
                style={styles.input}
                placeholder="Name"
                autoCapitalize="words"
                accessoryRight={PersonIcon}
                value={values.name}
                onChangeText={value => {
                  setFieldValue('name', value);
                  if (!isAccountNameEdited) {
                    setFieldValue('accountName', `${value}'s team`);
                  }
                }}
              />
            </InputValidationWrapper>
            <InputValidationWrapper errorMessage={errors.email}>
              <Input
                status={emailError && 'danger'}
                style={styles.input}
                autoCapitalize="none"
                placeholder="Email"
                accessoryRight={EmailIcon}
                value={values.email}
                onChangeText={value => setFieldValue('email', value)}
              />
            </InputValidationWrapper>
            <InputValidationWrapper errorMessage={errors.phone}>
              <View style={styles.phoneInputContainer}>
                <CustomPhoneInput
                  name="phone"
                  status={phoneError && 'danger'}
                />
              </View>
            </InputValidationWrapper>
            <InputValidationWrapper errorMessage={errors.password}>
              <Input
                status={passwordError && 'danger'}
                style={styles.input}
                autoCapitalize="none"
                secureTextEntry={!passwordVisible}
                placeholder="Password"
                accessoryRight={renderPasswordIcon}
                value={values.password}
                onChangeText={value => setFieldValue('password', value)}
              />
            </InputValidationWrapper>
            <View style={styles.teamTitle}>
              <TitleWithHint
                text="Team"
                hint="Currently you can only be part of one team. Either you create one or join an existing team. Create a team if you are the property manager and will be inviting others to join your team, such as handymen, painters etc. If you join a team, then you will be getting jobs from a property manager. To join, you either must have received an email link or an invitation code."
              />
            </View>
            <SelectInput
              name="role"
              style={styles.input}
              onSelect={selectedValue => {
                if (selectedValue !== 0) {
                  setFieldValue('invitationCode', '');
                }
              }}
              options={accountRoles.map(item => ({
                label: item.name,
                value: item.id,
              }))}
            />
            {values.role === 0 ? (
              <InputValidationWrapper errorMessage={errors.invitationCode}>
                <Input
                  status={invitationCodeError && 'danger'}
                  style={styles.input}
                  autoCapitalize="none"
                  placeholder="Invitation Code"
                  value={values.invitationCode}
                  onChangeText={value => setFieldValue('invitationCode', value)}
                />
              </InputValidationWrapper>
            ) : (
              <InputValidationWrapper errorMessage={errors.accountName}>
                <Input
                  status={accountNameError && 'danger'}
                  style={styles.input}
                  autoCapitalize="none"
                  label="Team Name"
                  placeholder="e.g. My Team"
                  value={values.accountName}
                  onChangeText={value => setFieldValue('accountName', value)}
                  onBlur={() => setIsAccountNameEdited(true)}
                />
              </InputValidationWrapper>
            )}
          </Layout>
          <SpinnerButton
            text="SIGN UP"
            style={styles.signUpButton}
            onPress={() => {
              handleSubmit();
              setIsSubmitButtonPressed(true);
            }}
            isLoading={isLoading}
          />
          <Button
            style={styles.signInButton}
            appearance="ghost"
            status="basic"
            onPress={onSignInButtonPress}
          >
            Sign In
          </Button>
        </KeyboardAvoidingView>
      )}
    </Formik>
  );
};

const themedStyles = StyleService.create({
  container: {
    backgroundColor: 'background-basic-color-1',
  },
  headerContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 216,
    backgroundColor: 'color-primary-default',
  },
  profileAvatar: {
    width: 116,
    height: 116,
    borderRadius: 58,
    alignSelf: 'center',
    backgroundColor: 'background-basic-color-1',
    tintColor: 'color-primary-default',
  },
  editAvatarButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  formContainer: {
    flex: 1,
    paddingTop: 32,
    paddingHorizontal: 16,
  },
  signUpLabel: {
    marginTop: 16,
  },
  input: {
    marginTop: 16,
  },
  termsCheckBox: {
    marginTop: 24,
  },
  termsCheckBoxText: {
    color: 'text-hint-color',
    marginLeft: 10,
  },
  signUpButton: {
    marginTop: 12,
    marginHorizontal: 16,
  },
  signInButton: {
    marginVertical: 12,
    marginHorizontal: 16,
  },
  phoneInputContainer: {
    marginTop: 15,
  },
  teamTitle: {
    marginTop: 20,
    marginBottom: -20,
  },
});
