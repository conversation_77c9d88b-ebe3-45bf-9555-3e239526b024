import { CheckBox, StyleService } from '@ui-kitten/components';
import { Field } from 'formik';
import React, { ReactElement } from 'react';
import { View, Text } from 'react-native';

type Props = {
  formLabel?: string;
  propertyName: string;
  dataset: Array<any>;
  disabled?: boolean;
};

const CheckboxesInput = ({
  formLabel,
  propertyName,
  dataset,
  disabled = false,
}: Props): ReactElement => (
  <Field>
    {({ form: { values, setFieldValue } }) => {
      const localValues = values[propertyName];
      return (
        <View>
          {formLabel && <Text style={styles.label}>{formLabel}</Text>}
          {dataset.map(({ id, name }, index) => (
            <CheckBox
              key={id}
              style={styles.input}
              checked={localValues.includes(id)}
              onChange={() => {
                if (localValues.indexOf(dataset[index].id) === -1) {
                  localValues.push(dataset[index].id);
                } else {
                  localValues.splice(localValues.indexOf(dataset[index].id), 1);
                }

                setFieldValue(propertyName, localValues);
              }}
              disabled={disabled}
            >
              {name}
            </CheckBox>
          ))}
        </View>
      );
    }}
  </Field>
);

export default CheckboxesInput;

const styles = StyleService.create({
  input: {
    marginHorizontal: 12,
    marginVertical: 8,
  },
  label: {
    marginLeft: 12,
    marginTop: 8,
    fontWeight: '800',
    color: '#8F9BB3',
    fontSize: 12,
    fontFamily: 'System',
  },
});
