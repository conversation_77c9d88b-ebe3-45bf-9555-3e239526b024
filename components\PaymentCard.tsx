import React from 'react';
import { Card, Text } from '@ui-kitten/components';
import { ImageBackground, StyleSheet, View } from 'react-native';
import { format } from 'date-fns';
import { Payment } from '~types';
import CardLineItem from './CardLineItem';
import ExpandImageButton from './ExpandImageButton';

const PaymentCard = ({
  subjectMonth,
  amount,
  date,
  description,
  media,
}: Omit<Payment, 'userId'>) => (
  <View style={styles.container}>
    <Card header={header(media)}>
      <Text>{description}</Text>

      <View style={styles.itemFooter}>
        <CardLineItem
          text={`Date: ${format(new Date(date), 'Y MMMM d.')}`}
          iconName="clock"
        />
        <CardLineItem
          text={`Subject month: ${format(new Date(subjectMonth), 'Y MMMM.')}`}
          iconName="clock"
        />
        <CardLineItem
          text={`Amount: ${`$${Number(amount).toFixed(2)}`}`}
          iconName="credit-card"
        />
      </View>
    </Card>
  </View>
);

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  backgroundImage: {
    flex: 1,
    aspectRatio: 1,
    resizeMode: 'contain',
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
  },
  itemFooter: {
    alignItems: 'flex-start',
    marginTop: 16,
    marginHorizontal: -16,
  },
});

const header = (media: string) => () =>
  (
    <ImageBackground
      style={styles.backgroundImage}
      source={{ uri: media }}
      resizeMode="contain"
    >
      <ExpandImageButton imageUri={media} />
    </ImageBackground>
  );

export default PaymentCard;
