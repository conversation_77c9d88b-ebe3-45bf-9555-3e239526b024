import useTasksAPI from '~api/useTasks';
import { useQuery } from '~node_modules/react-query';

type Props = {
  propertyId?: number;
  textSearch?: string;
  date?: string;
  urgent?: boolean;
  overdue?: boolean;
};

export const useFilteredAvailableTasks = ({
  propertyId,
  textSearch,
  date,
  urgent,
  overdue,
}: Props) => {
  const { getFilteredAvailableTasks } = useTasksAPI();

  const { data, isLoading } = useQuery(
    ['tasks', propertyId, textSearch, date, urgent, overdue],
    () =>
      getFilteredAvailableTasks({
        propertyId,
        textSearch,
        date,
        urgent,
        overdue,
      }),
    { keepPreviousData: true },
  );

  return { data, isLoading };
};

export default useFilteredAvailableTasks;
