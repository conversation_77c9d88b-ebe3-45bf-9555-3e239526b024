import React, { ReactElement, useState } from 'react';
import { Button, StyleService, useStyleSheet } from '@ui-kitten/components';
import { useMutation, useQueryClient } from 'react-query';
import { StackNavigationProp } from '@react-navigation/stack';
import { useToast } from 'react-native-toast-notifications';
import Confirm from '~components/Confirm';
import useTasks from '~api/useTasks';
import { MyInvoicesParamList } from '~types';

type Props = {
  navigation: StackNavigationProp<MyInvoicesParamList>;
  id: number;
};

const UndoFinishTask = ({ navigation, id }: Props): ReactElement => {
  const toast = useToast();
  const styles = useStyleSheet(themedStyles);

  const [showConfirmUncomplete, setShowConfirmUncomplete] = useState(false);

  const { unfinishTaskAssignment } = useTasks();

  const queryClient = useQueryClient();

  const { mutate: undoFinishTaskAssignmentMutate } = useMutation(
    unfinishTaskAssignment,
    {
      onSuccess: () => {
        queryClient.invalidateQueries('task-assignments');
        queryClient.invalidateQueries('completed-tasks');
        queryClient.invalidateQueries('tasks');
      },
    },
  );

  const undoFinishTask = () => {
    undoFinishTaskAssignmentMutate(id);
    toast.show('Your Job has been moved back to My Jobs.');
    navigation.goBack();
  };

  return (
    <>
      <Button
        style={styles.button}
        onPress={() => setShowConfirmUncomplete(true)}
        appearance="outline"
      >
        I haven&apos;t completed this Job
      </Button>
      <Confirm
        show={showConfirmUncomplete}
        onCancel={() => setShowConfirmUncomplete(false)}
        onConfirm={undoFinishTask}
        text="Are you sure you want to remove this job from your invoice?"
      />
    </>
  );
};

const themedStyles = StyleService.create({
  button: {
    marginTop: 16,
    width: '100%',
  },
});

export default UndoFinishTask;
