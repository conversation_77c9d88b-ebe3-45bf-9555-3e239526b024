import React, { ReactNode } from 'react';
import { View } from 'react-native';
import {
  Button,
  Card,
  Modal,
  StyleService,
  useStyleSheet,
  Text,
} from '~node_modules/@ui-kitten/components';
import SpinnerButton from './SpinnerButton';

type Props = {
  show: boolean;
  onCancel?: () => void;
  onConfirm: () => void;
  text?: string | ReactNode;
  confirmButtonText?: string;
  cancelButtonText?: string;
  confirmTitle?: string;
  isLoading?: boolean;
};

const Confirm = ({
  show,
  onCancel,
  onConfirm,
  text,
  confirmButtonText = 'Yes',
  cancelButtonText = 'No',
  confirmTitle,
  isLoading = false,
}: Props) => {
  const styles = useStyleSheet(themedStyles);

  return (
    <Modal visible={show} onBackdropPress={onCancel}>
      <Card style={styles.container}>
        {confirmTitle && (
          <Text category="h5" style={styles.title}>
            {confirmTitle}
          </Text>
        )}
        <Text style={styles.text}>{text}</Text>
        <View style={styles.buttons}>
          <SpinnerButton
            text={confirmButtonText}
            style={styles.button}
            onPress={onConfirm}
            isLoading={isLoading}
            size="medium"
          />
          {onCancel && (
            <Button
              style={styles.button}
              onPress={onCancel}
              appearance="outline"
            >
              {cancelButtonText}
            </Button>
          )}
        </View>
      </Card>
    </Modal>
  );
};

const themedStyles = StyleService.create({
  container: {
    marginHorizontal: 16,
  },
  buttons: { flexDirection: 'row', justifyContent: 'center' },
  button: {
    marginHorizontal: 16,
  },
  text: {
    fontSize: 16,
    marginBottom: 16,
  },
  title: { textAlign: 'center', marginBottom: 16 },
});

export default Confirm;
