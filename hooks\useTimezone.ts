import {
  formatInTimeZone as dateFnsFormatInTimeZone,
  zonedTimeToUtc as dateFnsZonedTimeToUtc,
} from 'date-fns-tz';
import { useQuery } from 'react-query';
import useUser from '~api/useUser';

type ReturnType = {
  timezone: string;
  zonedTimeToUtc: (date: Date) => Date;
  formatInTimeZone: (
    date: Date | string | number | undefined,
    format: string,
  ) => string;
};

export default (): ReturnType => {
  const { getUser } = useUser();
  const { data: user } = useQuery('user', () => getUser());

  const timezone = user?.accounts[0].timezone
    ? user?.accounts[0].timezone.value
    : 'America/Los_Angeles';

  const formatInTimeZone = (
    date: Date | string | number | undefined,
    format: string,
  ): string => {
    if (!date) {
      return '';
    }

    return dateFnsFormatInTimeZone(date, timezone, format);
  };

  const zonedTimeToUtc = (date: Date) => dateFnsZonedTimeToUtc(date, timezone);

  return {
    timezone,
    zonedTimeToUtc,
    formatInTimeZone,
  };
};
